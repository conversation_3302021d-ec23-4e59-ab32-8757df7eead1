package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionEditorChangeEvent implements Serializable
{
    private static final long serialVersionUID = 936900739462244318L;

    private final int sessionId;

    private final String username;

    private final String content;

    private final Integer cursorRow;

    private final Integer cursorColumn;

    private final String clientId;

    public VCSessionEditorChangeEvent(int sessionId, String username,
            String content, Integer cursorRow, Integer cursorColumn,
            String clientId)
    {
        this.sessionId = sessionId;
        this.username = username;
        this.content = content;
        this.cursorRow = cursorRow;
        this.cursorColumn = cursorColumn;
        this.clientId = clientId;
    }

    public String getClientId()
    {
        return clientId;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getUsername()
    {
        return username;
    }

    public String getContent()
    {
        return content;
    }

    public Integer getCursorRow()
    {
        return cursorRow;
    }

    public Integer getCursorColumn()
    {
        return cursorColumn;
    }

}
