package com.orbis.messaging.sockets;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Predicate;

import jakarta.websocket.Session;

import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.orbis.redis.liveObject.builder.RTopicBuilder;

public class SocketEvent
{
    private String id;

    private Consumer<Session> onOpen;

    private Consumer<Session> onClose;

    private BiConsumer<Throwable, Session> onError;

    private Class<?> onMessageClass;

    private BiConsumer onMessageListener;

    private Class onPublishClass;

    private MessageListener onPublishListener;

    private Predicate<Session> onAuth = s -> true;

    public String getId()
    {
        return id;
    }

    public SocketEvent id(String id)
    {
        this.id = id;
        return this;
    }

    public Consumer<Session> getOnOpen()
    {
        return onOpen;
    }

    public SocketEvent onAuth(Predicate<Session> onAuth)
    {
        this.onAuth = onAuth;
        return this;
    }

    public SocketEvent onOpen(Consumer<Session> onOpen)
    {
        this.onOpen = onOpen;
        return this;
    }

    public Consumer<Session> getOnClose()
    {
        return onClose;
    }

    public SocketEvent onClose(Consumer<Session> onClose)
    {
        this.onClose = onClose;
        return this;
    }

    public <T extends Serializable> SocketEvent onPublish(Class<T> clazz,
            MessageListener<T> listener)
    {
        onPublishClass = clazz;
        onPublishListener = (MessageListener<T>) (f, s) -> {
            listener.onMessage(f, s);
        };
        return this;
    }

    public <T extends Serializable> SocketEvent onMessage(Class<T> clazz,
            BiConsumer<Session, T> listener)
    {
        onMessageClass = clazz;
        onMessageListener = listener;
        return this;
    }

    public SocketEvent onError(BiConsumer<Throwable, Session> onError)
    {
        this.onError = onError;
        return this;
    }

    public MessageListener getOnPublishListener()
    {
        return onPublishListener;
    }

    public Class getOnPublishClass()
    {
        return onPublishClass;
    }

    public RTopic getRTopic()
    {
        String name = onPublishClass.getSimpleName();

        return new RTopicBuilder()//
                .name(name)//
                .addListener(onPublishClass, onPublishListener)//
                .build();
    }

    public Class<?> getOnMessageClass()
    {
        return onMessageClass;
    }

    public BiConsumer<Session, Object> getOnMessageListener()
    {
        return onMessageListener;
    }

    public void message(JsonElement json, Session session)
    {
        if (onMessageListener == null || onMessageClass == null)
            return;
        Object data = new GsonBuilder().setDateFormat("MMM d, yyyy hh:mm a")
                .create().fromJson(json, onMessageClass);
        onMessageListener.accept(session, data);
    }

    public void publish(JsonElement json, SocketEventPublisher publisher)
    {
        if (onPublishListener == null || onPublishClass == null)
            return;
        Object data = new GsonBuilder().setDateFormat("MMM d, yyyy hh:mm a")
                .create().fromJson(json, onPublishClass);
        publisher.publish(data);
    }

    public void open(Session session)
    {
        Optional.ofNullable(onOpen).ifPresent(c -> c.accept(session));
    }

    public void close(Session session)
    {
        Optional.ofNullable(onClose).ifPresent(c -> c.accept(session));
    }

    public void error(Throwable throwable, Session session)
    {
        StringWriter errors = new StringWriter();
        throwable.printStackTrace(new PrintWriter(errors));

        Optional.ofNullable(onError).ifPresent(bc -> bc.accept(throwable, session));
    }

    public Predicate<Session> getOnAuth()
    {
        return onAuth;
    }

    public BiConsumer<Throwable, Session> getOnError()
    {
        return onError;
    }
}
