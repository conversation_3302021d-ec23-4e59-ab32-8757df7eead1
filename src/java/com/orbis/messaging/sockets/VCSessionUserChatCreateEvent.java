package com.orbis.messaging.sockets;

import java.io.Serializable;

import com.orbis.web.content.chat.payload.ChatThumbnailPayload;

public class VCSessionUserChatCreateEvent implements Serializable
{
    private static final long serialVersionUID = 940787889344327469L;

    private final ChatThumbnailPayload chat;

    public VCSessionUserChatCreateEvent(ChatThumbnailPayload chat)
    {
        this.chat = chat;
    }

    public ChatThumbnailPayload getChat()
    {
        return chat;
    }
}
