package com.orbis.messaging.sockets;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.servlet.http.HttpSession;
import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;
import jakarta.websocket.server.ServerEndpointConfig.Configurator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.WebApplicationContext;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.ObjectUtils;

public class SpringConfigurator extends Configurator
{
    private static Log logger = LogFactory.getLog(SpringConfigurator.class);

    private static final Map<String, Map<Class<?>, String>> cache = new ConcurrentHashMap<>();

    private static final String NO_VALUE = ObjectUtils
            .identityToString(new Object());

    public SpringConfigurator()
    {
    }

    @Override
    public <T> T getEndpointInstance(Class<T> endpointClass)
            throws InstantiationException
    {
        WebApplicationContext wac = (WebApplicationContext) PortalUtils
                .getApplicationContext();
        if (wac == null)
        {
            String message = "Failed to find the root WebApplicationContext. Was ContextLoaderListener not used?";
            logger.error(message);
            throw new IllegalStateException(message);
        }

        String beanName = ClassUtils.getShortNameAsProperty(endpointClass);
        if (wac.containsBean(beanName))
        {
            T endpoint = (T) wac.getBean(beanName, endpointClass);
            if (logger.isDebugEnabled())
            {
                logger.debug("Using @ServerEndpoint singleton " + endpoint);
            }
            return endpoint;
        }

        beanName = getBeanNameByType(wac, endpointClass);
        if (beanName != null)
        {
            return (T) wac.getBean(beanName);
        }

        if (logger.isDebugEnabled())
        {
            logger.debug("Creating new @ServerEndpoint instance of type "
                    + endpointClass);
        }

        return null;
    }

    @Override
    public void modifyHandshake(ServerEndpointConfig config,
            HandshakeRequest request, HandshakeResponse response)
    {
        HttpSession httpSession = (HttpSession) request.getHttpSession();
        config.getUserProperties().put("httpParameterMap",
                request.getParameterMap());
        config.getUserProperties().put(HttpSession.class.getName(), httpSession);
    }

    private String getBeanNameByType(WebApplicationContext wac,
            Class<?> endpointClass)
    {
        String wacId = wac.getDisplayName();

        Map<Class<?>, String> beanNamesByType = cache.get(wacId);
        if (beanNamesByType == null)
        {
            beanNamesByType = new ConcurrentHashMap<>();
            cache.put(wacId, beanNamesByType);
        }

        if (!beanNamesByType.containsKey(endpointClass))
        {
            String[] names = wac.getBeanNamesForType(endpointClass);
            if (names.length == 1)
            {
                beanNamesByType.put(endpointClass, names[0]);
            }
            else
            {
                beanNamesByType.put(endpointClass, NO_VALUE);
                if (names.length > 1)
                {
                    String message = "Found multiple @ServerEndpoint's of type "
                            + endpointClass + ", names=" + names;
                    logger.error(message);
                    throw new IllegalStateException(message);
                }
            }
        }

        String beanName = beanNamesByType.get(endpointClass);
        return NO_VALUE.equals(beanName) ? null : beanName;
    }

}
