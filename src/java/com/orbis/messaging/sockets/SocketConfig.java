package com.orbis.messaging.sockets;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class SocketConfig
{
    private final List<SocketEvent> events = new ArrayList<>();

    public SocketEvent newEvent()
    {
        SocketEvent event = new SocketEvent();
        events.add(event);
        return event;
    }

    public List<SocketEvent> getEvents()
    {
        return events;
    }

    public Optional<SocketEvent> optEventById(String id)
    {
        return events.stream().filter(e -> e.getId().equals(id)).findFirst();
    }

    public SocketEvent getEventById(String id)
    {
        return optEventById(id).orElse(null);
    }
}
