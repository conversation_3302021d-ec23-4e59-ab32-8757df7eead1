package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionUserKickEvent implements Serializable
{
    private static final long serialVersionUID = 8915452666700958919L;

    private final int sessionId;

    private final String username;

    public VCSessionUserKickEvent(int sessionId, String username)
    {
        this.sessionId = sessionId;
        this.username = username;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getUsername()
    {
        return username;
    }
}
