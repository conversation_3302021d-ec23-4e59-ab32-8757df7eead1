package com.orbis.messaging.sockets;

import jakarta.websocket.Session;

import java.util.concurrent.ConcurrentHashMap;

public class VCMessageSocketBroadcaster implements SocketBroadcaster
{
    private final ConcurrentHashMap<Session, Integer> wsSessions = new ConcurrentHashMap<>();

    private static VCMessageSocketBroadcaster INSTANCE;

    public static VCMessageSocketBroadcaster getInstance()
    {
        if (INSTANCE == null)
        {
            INSTANCE = new VCMessageSocketBroadcaster();
        }
        return INSTANCE;
    }

    public void register(Session session)
    {
        wsSessions.put(session, 0);
    }

    public void unregister(Session session)
    {
        wsSessions.remove(session);
    }

    public void broadcast(String msgType, Object msg)
    {
        for (Session s : wsSessions.keySet())
        {
            broadcast(s, msgType, msg, this::unregister);
        }
    }
}
