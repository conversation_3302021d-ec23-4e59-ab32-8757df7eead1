package com.orbis.messaging.sockets;

import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import jakarta.servlet.http.HttpSession;
import jakarta.websocket.EndpointConfig;
import jakarta.websocket.Session;

import com.orbis.configuration.security.core.OrbisUserDetails;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;

import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

public abstract class AbstractSocketEndPoint
{
    protected static Log logger = LogFactory.getLog(AbstractSocketEndPoint.class);

    public static final String ORBIS_SOCKETS_ENDPOINT_PREFIX = "/orbissockets/";

    private final Set<Session> sessions = Collections
            .synchronizedSet(new HashSet<Session>());

    public boolean registerSession(Session session)
    {
        return sessions.add(session);
    }

    public boolean removeSession(Session session)
    {
        return sessions.remove(session);
    }

    public static UserDetailsImpl getUserLoggedIn(EndpointConfig config)
    {
        HttpSession session = getHttpSession(config);
        return getUserLoggedIn(session);
    }

    public static UserDetailsImpl getUserLoggedIn(Session session)
    {
        return getUserLoggedIn((HttpSession) session.getUserProperties()
                .get(HttpSession.class.getName()));
    }

    public static UserDetailsImpl getUserLoggedIn(HttpSession session)
    {
        return Optional.ofNullable(session)
                .map(s -> ((SecurityContext) s.getAttribute(
                        HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY))
                        .getAuthentication())
                .map(it -> ((OrbisUserDetails) it.getPrincipal()).getOriginalUser())
                .orElse(null);
    }

    public static HttpSession getHttpSession(EndpointConfig config)
    {
        return (HttpSession) config.getUserProperties()
                .get(HttpSession.class.getName());
    }

    public Set<Session> getSessions()
    {
        return sessions;
    }
}
