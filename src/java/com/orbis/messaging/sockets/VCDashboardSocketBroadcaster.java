package com.orbis.messaging.sockets;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.websocket.Session;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimaps;
import com.google.common.collect.SetMultimap;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.WebSocketUtils;
import com.orbis.web.content.vc.VCAdmin;
import com.orbis.web.content.vc.VCHelper;
import com.orbis.web.content.vc.View;

public class VCDashboardSocketBroadcaster implements SocketBroadcaster
{
    private static final int NA = 0;

    /**
     * The websocket sessions and the ID of the chat they're listening to.
     * Sessions don't have to listen to a chat, so the ID can be null.
     */
    private final ConcurrentHashMap<Session, Integer> chatIdsByWsSession = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Session, Integer> vcAdminIdsBySession = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, Integer> connectedSessionsByUserAndSession = new ConcurrentHashMap<>();

    /**
     * The websocket sessions listening to each chat.
     */
    private final SetMultimap<Integer, Session> wsSessionsByChatId = Multimaps
            .synchronizedSetMultimap(HashMultimap.create());

    private static VCDashboardSocketBroadcaster INSTANCE;

    public static VCDashboardSocketBroadcaster getInstance()
    {
        if (INSTANCE == null)
        {
            INSTANCE = new VCDashboardSocketBroadcaster();
        }
        return INSTANCE;
    }

    public void register(Session session)
    {
        UserDetailsImpl user = WebSocketUtils.requireUser(session);
        View view = View
                .valueOf(session.getRequestParameterMap().get("view").get(0));

        chatIdsByWsSession.put(session, NA);

        VCAdmin vcAdmin = VCHelper.updateJoinVcAdmin(user, view);

        vcAdminIdsBySession.put(session, vcAdmin.getId());
    }

    public void unregister(Session session)
    {
        View view = View
                .valueOf(session.getRequestParameterMap().get("view").get(0));

        Integer chatId = chatIdsByWsSession.remove(session);
        if (chatId != null && chatId != NA)
        {
            wsSessionsByChatId.remove(chatId, session);
        }

        int vcAdminId = vcAdminIdsBySession.remove(session);

        VCHelper.updateLeftVcAdmin(vcAdminId, view);
    }

    public void setChat(Session session, Integer chatId)
    {
        Integer currentChatId = chatIdsByWsSession.get(session);
        chatIdsByWsSession.put(session, chatId);
        if (currentChatId != null && currentChatId != NA)
        {
            wsSessionsByChatId.remove(currentChatId, session);
        }
        if (chatId != NA)
        {
            wsSessionsByChatId.put(chatId, session);
        }
    }

    public void broadcast(String msgType, Object msg)
    {
        if ("VC_DASHBOARD_USER_CONNECTED".equals(msgType))
        {
            VCDashboardUserConnectedEvent event = (VCDashboardUserConnectedEvent) msg;
            String mapKey = event.getUserId() + "||" + event.getVcSessionId();
            if (!connectedSessionsByUserAndSession.containsKey(mapKey))
            {
                connectedSessionsByUserAndSession.put(mapKey, 1);
            }
            else
            {
                connectedSessionsByUserAndSession.replace(mapKey,
                        connectedSessionsByUserAndSession.get(mapKey) + 1);
            }
            for (Session s : chatIdsByWsSession.keySet())
            {
                broadcast(s, msgType, msg);
            }
        }
        else if ("VC_DASHBOARD_USER_DISCONNECTED".equals(msgType))
        {
            VCDashboardUserDisconnectedEvent event = (VCDashboardUserDisconnectedEvent) msg;
            String mapKey = event.getUserId() + "||" + event.getVcSessionId();
            connectedSessionsByUserAndSession.replace(mapKey,
                    connectedSessionsByUserAndSession.get(mapKey) - 1);
            if (connectedSessionsByUserAndSession.get(mapKey) == 0)
            {
                if (PortalUtils.isMasterSite())
                {
                    PortalUtils.getJt()
                            .update("UPDATE vc_session_participant SET leftAt = "
                                    + DateUtils.getCurrentDateClause()
                                    + ", online = 0 WHERE session = ? AND username = ?",
                                    new Object[] { event.getVcSessionId(),
                                            event.getUserId() });
                }
                for (Session s : chatIdsByWsSession.keySet())
                {
                    broadcast(s, msgType, msg);
                }
            }
        }
        else
        {
            for (Session s : chatIdsByWsSession.keySet())
            {
                broadcast(s, msgType, msg);
            }
        }
    }

    public void broadcastToChat(int chatId, String msgType, Object msg)
    {
        Set<Session> set = wsSessionsByChatId.get(chatId);

        Set<Session> copy;
        synchronized (set)
        {
            copy = new HashSet<>(set);
        }

        for (Session session : copy)
        {
            broadcast(session, msgType, msg);
        }
    }

    public void broadcast(Session session, String msgType, Object msg)
    {
        broadcast(session, msgType, msg, ses -> unregister(ses));
    }
}
