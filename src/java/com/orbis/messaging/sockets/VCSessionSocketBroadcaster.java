package com.orbis.messaging.sockets;

import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import jakarta.websocket.Session;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimaps;
import com.google.common.collect.SetMultimap;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;
import com.orbis.web.content.vc.VCHelper;
import com.orbis.web.content.vc.VCOnlineScheduleHelper;
import com.orbis.web.content.vc.session.VCSession;
import com.orbis.web.content.vc.session.VCTimeLog;

/**
 * Broadcasts messages to users connected to a websocket.
 *
 * <AUTHOR>
 *
 */
public class VCSessionSocketBroadcaster implements SocketBroadcaster
{
    private static final int NA = 0;

    private static VCSessionSocketBroadcaster INSTANCE;

    /**
     * The websocket sessions and the ID of the {@link VCSession} they're
     * listening to.
     */
    private final ConcurrentHashMap<Session, Integer> vcSessionIdsByWsSessions = new ConcurrentHashMap<>();

    /**
     * The websocket sessions listening to each {@link VCSession}.
     */
    private final SetMultimap<Integer, Session> wsSessionsByVcSessionId = Multimaps
            .synchronizedSetMultimap(HashMultimap.create());

    /**
     * The websocket sessions listening to each {@link VCSession}.
     */
    private final SetMultimap<String, Session> wsSessionsByUserId = Multimaps
            .synchronizedSetMultimap(HashMultimap.create());

    private final ConcurrentHashMap<Integer, Date> vcSessionEndDatesById = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Session, String> userIdsByWsSession = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Session, Integer> vcTimeLogIdsByWsSession = new ConcurrentHashMap<>();

    private final SetMultimap<String, Session> wsSessionsByVcSessionUserKey = Multimaps
            .synchronizedSetMultimap(HashMultimap.create());

    private final ConcurrentHashMap<Session, UserDetailsImpl> usersBySession = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, String> codeEditorContentByVcSessionId = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, Integer> codeEditorCursorRowByVcSessionId = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, Integer> codeEditorCursorColumnByVcSessionId = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, String> codeEditorModeByVcSessionId = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Session, Integer> chatIdsByWsSession = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, Boolean> vcSessionMuteAllState = new ConcurrentHashMap<>();

    private final SetMultimap<Integer, Session> wsSessionsByChatId = Multimaps
            .synchronizedSetMultimap(HashMultimap.create());

    private final ScheduledExecutorService scheduledExecutorService = Executors
            .newScheduledThreadPool(1, (r) -> new Thread(r,
                    VCSessionSocketBroadcaster.class.getSimpleName()));

    @SuppressWarnings("unused")
    private ScheduledFuture future;

    private VCSessionSocketBroadcaster()
    {
        future = scheduledExecutorService.scheduleAtFixedRate(() -> {
            int timeout = VCHelper.getTimeoutMinutes();
            for (Map.Entry<Integer, Date> entry : vcSessionEndDatesById.entrySet())
            {
                Date date = entry.getValue();
                if (DateUtils.isAfterOrEquals(new Date(),
                        DateUtils.addMinutes(date, timeout)))
                {
                    SocketEndPoint
                            .publish(new VCSessionExpireEvent(entry.getKey()));
                }
            }
        }, TimeUnit.MINUTES.toMillis(10), TimeUnit.MINUTES.toMillis(10),
                TimeUnit.MILLISECONDS);
    }

    public static VCSessionSocketBroadcaster getInstance()
    {
        if (INSTANCE == null)
        {
            INSTANCE = new VCSessionSocketBroadcaster();
        }
        return INSTANCE;
    }

    public void setChat(Session session, Integer chatId)
    {
        Integer currentChatId = chatIdsByWsSession.get(session);
        chatIdsByWsSession.put(session, chatId);
        if (currentChatId != null && currentChatId != NA)
        {
            wsSessionsByChatId.remove(currentChatId, session);
        }
        if (chatId != NA)
        {
            wsSessionsByChatId.put(chatId, session);
        }
    }

    public String getCodeEditorModeForVcSession(Integer vcSessionId)
    {
        return codeEditorModeByVcSessionId.get(vcSessionId);
    }

    public void setCodeEditorModeForVcSession(Integer vcSessionId, String mode)
    {
        codeEditorModeByVcSessionId.put(vcSessionId,
                StringUtils.defaultString(mode));
    }

    public String getCodeEditorContentForVcSession(Integer vcSessionId)
    {
        return codeEditorContentByVcSessionId.get(vcSessionId);
    }

    public void setCodeEditorContentForVcSession(Integer vcSessionId,
            String content)
    {
        codeEditorContentByVcSessionId.put(vcSessionId,
                StringUtils.defaultString(content));
    }

    public Integer getCodeEditorCursorRowForVcSession(Integer vcSessionId)
    {
        return codeEditorCursorRowByVcSessionId.get(vcSessionId);
    }

    public void setCodeEditorCursorRowForVcSession(Integer vcSessionId,
            Integer cursorRow)
    {
        codeEditorCursorRowByVcSessionId.put(vcSessionId,
                Optional.ofNullable(cursorRow).orElse(0));
    }

    public Integer getCodeEditorCursorColumnForVcSession(Integer vcSessionId)
    {
        return codeEditorCursorColumnByVcSessionId.get(vcSessionId);
    }

    public void setCodeEditorCursorColumnForVcSession(Integer vcSessionId,
            Integer cursorColumn)
    {
        codeEditorCursorColumnByVcSessionId.put(vcSessionId,
                Optional.ofNullable(cursorColumn).orElse(0));
    }

    public Boolean getVCSessionMuteAllState(Integer vcSessionId)
    {
        return vcSessionMuteAllState.get(vcSessionId);
    }

    public void toggleVCSessionMuteAllState(Integer vcSessionId, boolean muteAll)
    {
        vcSessionMuteAllState.put(vcSessionId, muteAll);
    }

    public void broadcastToChat(int chatId, String msgType, Object msg)
    {
        Set<Session> set = wsSessionsByChatId.get(chatId);

        Set<Session> copy;
        synchronized (set)
        {
            copy = new HashSet<>(set);
        }

        for (Session session : copy)
        {
            broadcast(session, msgType, msg, false);
        }
    }

    /**
     * Register the websocket session
     *
     * @param session
     */
    @SuppressWarnings("resource")
    public void register(Session session)
    {
        int vcSessionId = Integer
                .parseInt(session.getRequestParameterMap().get("sessionId").get(0));

        VCSession vcSession = PortalUtils.getHt().l(VCSession.class, vcSessionId);

        UserDetailsImpl user = VCOnlineScheduleHelper
                .requireVcOnlineScheduleLoggedInUserOrUserLoggedIn(session,
                        vcSession);

        vcSessionMuteAllState.putIfAbsent(vcSessionId, false);

        vcSessionIdsByWsSessions.put(session, vcSessionId);

        wsSessionsByVcSessionId.put(vcSessionId, session);

        wsSessionsByVcSessionUserKey.put(
                createVcSessionUserKey(vcSessionId, user.getUsername()), session);

        userIdsByWsSession.put(session, user.getUsername());

        wsSessionsByUserId.put(user.getUsername(), session);

        UserDetailsImpl u = new UserDetailsImpl();
        u.setUsername(user.getUsername());
        u.setFirstName(user.getPreferredFirstName());
        u.setLastName(user.getLastName());
        usersBySession.put(session, u);

        VCTimeLog timeLog = new VCTimeLog();
        timeLog.setVcSessionType(vcSession.getType().name());
        PortalUtils.getHt().save(timeLog);

        vcTimeLogIdsByWsSession.put(session, timeLog.getId());

        vcSessionEndDatesById.put(vcSessionId, vcSession.getEntity().getToDate());

        VCHelper.updateJoinSessionParticipant(vcSessionId, user);
    }

    private String createVcSessionUserKey(int vcSessionId, String username)
    {
        return vcSessionId + "_" + username;
    }

    /**
     * Unregister the websocket session
     *
     * @param session
     */
    @SuppressWarnings("resource")
    public void unregister(Session session)
    {
        Integer vcSessionId = vcSessionIdsByWsSessions.remove(session);

        String userId = userIdsByWsSession.remove(session);

        wsSessionsByUserId.remove(userId, session);

        wsSessionsByVcSessionUserKey
                .remove(createVcSessionUserKey(vcSessionId, userId), session);

        UserDetailsImpl user = usersBySession.remove(session);

        Integer chatId = chatIdsByWsSession.remove(session);
        if (chatId != null && chatId != NA)
        {
            wsSessionsByChatId.remove(chatId, session);
        }

        Integer timeLogId = vcTimeLogIdsByWsSession.remove(session);
        PortalUtils.getJt().update(
                " UPDATE vc_time_log SET leftAt = ? WHERE id = ? ",
                new Date(), timeLogId);

        if (vcSessionId != null)
        {
            wsSessionsByVcSessionId.remove(vcSessionId, session);
            if (CollectionUtils.isEmpty(wsSessionsByVcSessionId.get(vcSessionId)))
            {
                vcSessionMuteAllState.remove(vcSessionId);
                codeEditorContentByVcSessionId.remove(vcSessionId);
                codeEditorCursorRowByVcSessionId.remove(vcSessionId);
                codeEditorCursorColumnByVcSessionId.remove(vcSessionId);
                codeEditorModeByVcSessionId.remove(vcSessionId);
                vcSessionEndDatesById.remove(vcSessionId);
            }

            if (user != null)
            {
                VCHelper.updateLeftSessionParticipant(vcSessionId, user);
            }
        }
    }

    /**
     * Broadcast to all users listening to the {@link VCSession}.
     *
     * @param vcSessionId
     * @param force
     * @param broadcast
     */
    public void broadcastToVcSession(int vcSessionId, String msgType, Object msg,
            boolean force)
    {
        Set<Session> set = wsSessionsByVcSessionId.get(vcSessionId);

        Set<Session> copy;
        synchronized (set)
        {
            copy = new HashSet<>(set);
        }

        for (Session session : copy)
        {
            broadcast(session, msgType, msg, force);
        }
    }

    public void broadcastToUser(String userId, String msgType, Object msg)
    {
        Set<Session> set = wsSessionsByUserId.get(userId);

        Set<Session> copy;
        synchronized (set)
        {
            copy = new HashSet<>(set);
        }

        for (Session session : copy)
        {
            broadcast(session, msgType, msg, false);
        }
    }

    @SuppressWarnings("resource")
    public void broadcastToVcSessionUser(int vcSessionId, String userId,
            String msgType, Object msg, boolean force)
    {
        Set<Session> sessions = wsSessionsByVcSessionUserKey
                .get(createVcSessionUserKey(vcSessionId, userId));
        if (sessions != null && !sessions.isEmpty())
        {
            sessions.forEach(session -> broadcast(session, msgType, msg,
                    ses -> unregister(ses), force));
        }
    }

    public void broadcast(String msgType, Object msg)
    {
        for (Session s : vcSessionIdsByWsSessions.keySet())
        {
            broadcast(s, msgType, msg, false);
        }
    }

    private void broadcast(Session session, String msgType, Object msg,
            boolean force)
    {
        broadcast(session, msgType, msg, ses -> unregister(ses), force);
    }
}
