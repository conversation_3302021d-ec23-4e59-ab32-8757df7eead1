package com.orbis.messaging.sockets;

public class VCSessionUserConnectedEventBroadcastPayload
        extends VCSessionUserConnectedEvent
{
    private static final long serialVersionUID = 8668167469894460413L;

    private final String codeEditorContent;

    private final String codeEditorMode;

    public VCSessionUserConnectedEventBroadcastPayload(
            VCSessionUserConnectedEvent payload, String codeEditorContent,
            String codeEditorMode)
    {
        super(payload);
        this.codeEditorContent = codeEditorContent;
        this.codeEditorMode = codeEditorMode;
    }

    public String getCodeEditorContent()
    {
        return codeEditorContent;
    }

    public String getCodeEditorMode()
    {
        return codeEditorMode;
    }
}