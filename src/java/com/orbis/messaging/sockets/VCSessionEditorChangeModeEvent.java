package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionEditorChangeModeEvent implements Serializable
{
    private static final long serialVersionUID = 936900739462244318L;

    private final int sessionId;

    private final String username;

    private final String mode;

    public VCSessionEditorChangeModeEvent(int sessionId, String username,
            String mode)
    {
        this.sessionId = sessionId;
        this.username = username;
        this.mode = mode;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getUsername()
    {
        return username;
    }

    public String getMode()
    {
        return mode;
    }

}
