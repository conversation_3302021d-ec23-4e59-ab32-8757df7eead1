package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionMuteAllEvent implements Serializable
{

    private final int sessionId;

    private final String username;

    private final boolean muteAll;

    public VCSessionMuteAllEvent(int sessionId, String username, boolean muteAll)
    {
        this.sessionId = sessionId;
        this.username = username;
        this.muteAll = muteAll;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getUsername()
    {
        return username;
    }

    public boolean isMuteAll()
    {
        return muteAll;
    }
}
