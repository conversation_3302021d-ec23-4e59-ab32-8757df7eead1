package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCDashboardBroadcastMessageToggledEvent implements Serializable
{
    private static final long serialVersionUID = -7692893843104524200L;

    private final VCBroadcastMessagePayload payload;

    public VCDashboardBroadcastMessageToggledEvent(
            VCBroadcastMessagePayload payload)
    {
        this.payload = payload;
    }

    public VCBroadcastMessagePayload getPayload()
    {
        return payload;
    }
}
