package com.orbis.messaging.sockets;

import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.redisson.api.RTopic;

public class SocketEventPublisher
{
    private final SocketConfig config;

    private final ConcurrentHashMap<Class<?>, RTopic> topics;

    public SocketEventPublisher(SocketConfig config)
    {
        this.config = config;
        this.topics = config.getEvents().stream()//
                .filter(t -> t.getOnPublishClass() != null
                        && t.getOnPublishListener() != null)//
                .collect(Collectors.toMap(SocketEvent::getOnPublishClass,
                        SocketEvent::getRTopic, (f, s) -> f,
                        ConcurrentHashMap::new));
    }

    public void publish(Object msg)
    {
        RTopic topic = topics.get(msg.getClass());
        topic.publish(msg);
    }

    public SocketConfig getConfig()
    {
        return config;
    }
}
