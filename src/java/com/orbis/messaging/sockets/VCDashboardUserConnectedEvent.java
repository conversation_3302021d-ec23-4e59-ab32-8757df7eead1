package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCDashboardUserConnectedEvent implements Serializable
{
    private static final long serialVersionUID = 7744790558105065443L;

    private final int vcSessionId;

    private final String userId;

    public VCDashboardUserConnectedEvent(int vcSessionId, String userId)
    {
        this.vcSessionId = vcSessionId;
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }

    public int getVcSessionId()
    {
        return vcSessionId;
    }
}
