package com.orbis.messaging.sockets;

import java.io.Serializable;

import com.orbis.web.content.chat.payload.ChatMessagePayload;

public class VCSessionUserChatSendMessageEvent implements Serializable
{
    private static final long serialVersionUID = 1193755729261666560L;

    private final int chatId;

    private final ChatMessagePayload message;

    public VCSessionUserChatSendMessageEvent(int chatId, ChatMessagePayload message)
    {
        this.chatId = chatId;
        this.message = message;
    }

    public ChatMessagePayload getMessage()
    {
        return message;
    }

    public int getChatId()
    {
        return chatId;
    }
}