package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCDashboardUserDisconnectedEvent implements Serializable
{
    private static final long serialVersionUID = 4247088839065135036L;

    private final int vcSessionId;

    private final String userId;

    public VCDashboardUserDisconnectedEvent(int vcSessionId, String userId)
    {
        this.vcSessionId = vcSessionId;
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }

    public int getVcSessionId()
    {
        return vcSessionId;
    }
}
