package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionSendMessageEvent implements Serializable
{
    private static final long serialVersionUID = -5443860860638996765L;

    private int sessionId;

    private String username, fullName, message, img;

    public void setSessionId(int sessionId)
    {
        this.sessionId = sessionId;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public void setFullName(String fullName)
    {
        this.fullName = fullName;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }

    public void setImg(String img)
    {
        this.img = img;
    }

    public String getUsername()
    {
        return username;
    }

    public String getFullName()
    {
        return fullName;
    }

    public String getMessage()
    {
        return message;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getImg()
    {
        return img;
    }
}