package com.orbis.messaging.sockets;

import java.util.function.Consumer;
import java.util.function.Predicate;

import jakarta.websocket.Session;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.function.OrbisPredicates;
import com.orbis.web.content.chat.ChatHelper;
import com.orbis.web.content.chat.payload.ChatThumbnailPayload;
import com.orbis.web.content.vc.VCHelper;
import com.orbis.web.content.vc.VCOnlineScheduleHelper;
import com.orbis.web.content.vc.schedule.VCOnlineScheduleVisibility;
import com.orbis.web.content.vc.session.VCSession;

public class SocketConfigBootstrapper
{
    public static void bootstrap(SocketConfig config)
    {
        registerVcDashboardEvents(config);
        registerVcSessionEvents(config);
        registerVcBroadcastMessageEvents(config);
    }

    private static void registerVcSessionEvents(SocketConfig config)
    {
        Consumer<Session> onOpen = VCSessionSocketBroadcaster
                .getInstance()::register;

        Consumer<Session> onClose = VCSessionSocketBroadcaster
                .getInstance()::unregister;

        Predicate<Session> onAuth = session -> {
            UserDetailsImpl user = VCOnlineScheduleHelper
                    .requireVcOnlineScheduleLoggedInUserOrUserLoggedIn(session,
                            null);

            if (user == null)
                return false;

            if (user.isPortalStaff())
                return true;

            int sessionId = Integer.parseInt(
                    session.getRequestParameterMap().get("sessionId").get(0));

            return VCHelper.isSessionParticipant(sessionId, user.getUsername());
        };

        Predicate<Session> onAuthIsMod = session -> {
            UserDetailsImpl user = VCOnlineScheduleHelper
                    .requireVcOnlineScheduleLoggedInUserOrUserLoggedIn(session,
                            null);

            if (user == null)
                return false;

            if (user.isPortalStaff())
                return true;

            VCSession vcSession = PortalUtils.getHt().l(VCSession.class,
                    Integer.parseInt(session.getRequestParameterMap()
                            .get("sessionId").get(0)));

            return vcSession.getType().isMod(vcSession, user);
        };

        config.newEvent()//
                .id("VC_DASHBOARD_CLAIM_CHAT")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardClaimChatEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_CLAIM_CHAT", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_UNCLAIM_CHAT")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardUnclaimChatEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_UNCLAIM_CHAT", msg));

        config.newEvent()//
                .id("VC_SESSION_USER_CHAT_SEND_MESSAGE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionUserChatSendMessageEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .broadcastToChat(msg.getChatId(),
                                        "VC_SESSION_USER_CHAT_SEND_MESSAGE", msg));

        config.newEvent()//
                .id("VC_SESSION_USER_CHAT_CREATE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionUserChatCreateEvent.class, (c, msg) -> {
                    ChatThumbnailPayload payload = msg.getChat();

                    int chatId = payload.getId();

                    for (String id : ChatHelper.getChatParticipantUserIds(chatId))
                        VCSessionSocketBroadcaster.getInstance().broadcastToUser(id,
                                "VC_SESSION_USER_CHAT_CREATE", msg);
                });

        config.newEvent()//
                .id("VC_SESSION_USER_CHAT_SET")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onMessage(VCSessionUserChatSetEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .setChat(session, msg.getChatId()));

        config.newEvent()//
                .id("VC_SESSION_USER_KICK")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuthIsMod)//
                .onPublish(VCSessionUserKickEvent.class, (session, msg) -> {
                    VCSessionSocketBroadcaster.getInstance()
                            .broadcastToVcSessionUser(msg.getSessionId(),
                                    msg.getUsername(), "VC_SESSION_USER_KICK", msg,
                                    true);
                });

        config.newEvent()//
                .id("VC_SESSION_EXPIRE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuthIsMod)//
                .onPublish(VCSessionExpireEvent.class, (session, msg) -> {
                    VCSessionSocketBroadcaster.getInstance().broadcastToVcSession(
                            msg.getSessionId(), "VC_SESSION_EXPIRE", msg, true);
                });

        config.newEvent()//
                .id("VC_SESSION_USER_CONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionUserConnectedEvent.class, (session, msg) -> {
                    String codeEditorContent = VCSessionSocketBroadcaster
                            .getInstance()
                            .getCodeEditorContentForVcSession(msg.getSessionId());
                    String codeEditorMode = VCSessionSocketBroadcaster.getInstance()
                            .getCodeEditorModeForVcSession(msg.getSessionId());
                    VCSessionUserConnectedEventBroadcastPayload payload = new VCSessionUserConnectedEventBroadcastPayload(
                            msg, codeEditorContent, codeEditorMode);
                    VCSessionSocketBroadcaster.getInstance().broadcastToVcSession(
                            msg.getSessionId(), "VC_SESSION_USER_CONNECTED",
                            payload, false);
                });

        config.newEvent()//
                .id("VC_SESSION_USER_DISCONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionUserDisconnectedEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .broadcastToVcSession(msg.getSessionId(),
                                        "VC_SESSION_USER_DISCONNECTED", msg,
                                        false));

        config.newEvent()//
                .id("VC_SESSION_ADMIN_CONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionAdminConnectedEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .broadcast("VC_SESSION_ADMIN_CONNECTED", msg));

        config.newEvent()//
                .id("VC_SESSION_ADMIN_DISCONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionAdminDisconnectedEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .broadcast("VC_SESSION_ADMIN_DISCONNECTED", msg));

        config.newEvent()//
                .id("VC_SESSION_SEND_MESSAGE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionSendMessageEvent.class,
                        (session, msg) -> VCSessionSocketBroadcaster.getInstance()
                                .broadcastToVcSession(msg.getSessionId(),
                                        "VC_SESSION_SEND_MESSAGE", msg, false));

        config.newEvent()//
                .id("VC_SESSION_EDITOR_CHANGE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionEditorChangeEvent.class, (session, msg) -> {
                    VCSessionSocketBroadcaster.getInstance()
                            .setCodeEditorContentForVcSession(msg.getSessionId(),
                                    msg.getContent());
                    VCSessionSocketBroadcaster.getInstance()
                            .setCodeEditorCursorRowForVcSession(msg.getSessionId(),
                                    msg.getCursorRow());
                    VCSessionSocketBroadcaster.getInstance()
                            .setCodeEditorCursorColumnForVcSession(
                                    msg.getSessionId(), msg.getCursorColumn());
                    VCSessionSocketBroadcaster.getInstance().broadcastToVcSession(
                            msg.getSessionId(), "VC_SESSION_EDITOR_CHANGE", msg,
                            false);
                });

        config.newEvent()//
                .id("VC_SESSION_EDITOR_CHANGE_MODE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionEditorChangeModeEvent.class, (session, msg) -> {
                    VCSessionSocketBroadcaster.getInstance()
                            .setCodeEditorModeForVcSession(msg.getSessionId(),
                                    msg.getMode());
                    VCSessionSocketBroadcaster.getInstance().broadcastToVcSession(
                            msg.getSessionId(), "VC_SESSION_EDITOR_CHANGE_MODE",
                            msg, false);
                });

        config.newEvent()//
                .id("VC_SESSION_MUTE_ALL")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCSessionMuteAllEvent.class, (session, msg) -> {
                    VCSessionSocketBroadcaster.getInstance()
                            .toggleVCSessionMuteAllState(msg.getSessionId(),
                                    msg.isMuteAll());
                    VCSessionSocketBroadcaster.getInstance()
                            .broadcast("VC_SESSION_MUTE_ALL", msg);
                });
    }

    private static void registerVcDashboardEvents(SocketConfig config)
    {
        Consumer<Session> onOpen = VCDashboardSocketBroadcaster
                .getInstance()::register;

        Consumer<Session> onClose = VCDashboardSocketBroadcaster
                .getInstance()::unregister;

        Predicate<Session> onAuth = session -> {
            UserDetailsImpl user = VCOnlineScheduleHelper
                    .requireVcOnlineScheduleLoggedInUserOrUserLoggedIn(session,
                            null);
            VCOnlineScheduleVisibility scheduleVisibility = VCOnlineScheduleVisibility
                    .of(user);
            return user != null && scheduleVisibility.isAnyHelpScheduleVisible();
        };

        config.newEvent()//
                .id("VC_DASHBOARD_CLAIM_CHAT")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardClaimChatEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_CLAIM_CHAT", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_UNCLAIM_CHAT")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardUnclaimChatEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_UNCLAIM_CHAT", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_USER_CONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardUserConnectedEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_USER_CONNECTED", msg));
        config.newEvent()//
                .id("VC_DASHBOARD_USER_DISCONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardUserDisconnectedEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_USER_DISCONNECTED", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_ADMIN_CONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardAdminConnectedEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_ADMIN_CONNECTED", msg));
        config.newEvent()//
                .id("VC_DASHBOARD_ADMIN_DISCONNECTED")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardAdminDisconnectedEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_ADMIN_DISCONNECTED", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_CHAT_SET")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onMessage(VCDashboardChatSetEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .setChat(session, msg.getChatId()));

        config.newEvent()//
                .id("VC_DASHBOARD_CHAT_SEND_MESSAGE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardChatSendMessageEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_CHAT_SEND_MESSAGE", msg));

        config.newEvent()//
                .id("VC_DASHBOARD_CHAT_CREATE")//
                .onOpen(onOpen)//
                .onClose(onClose)//
                .onAuth(onAuth)//
                .onPublish(VCDashboardChatCreateEvent.class,
                        (session, msg) -> VCDashboardSocketBroadcaster.getInstance()
                                .broadcast("VC_DASHBOARD_CHAT_CREATE", msg));
    }

    private static void registerVcBroadcastMessageEvents(SocketConfig config)
    {
        config.newEvent()//
                .id("VC_DASHBOARD_BROADCAST_MESSAGE_TOGGLED")//
                .onOpen(VCMessageSocketBroadcaster.getInstance()::register)//
                .onClose(VCMessageSocketBroadcaster.getInstance()::unregister)//
                .onAuth(OrbisPredicates.alwaysTrue())//
                .onPublish(VCDashboardBroadcastMessageToggledEvent.class,
                        (session, msg) -> {
                            VCMessageSocketBroadcaster.getInstance().broadcast(
                                    "VC_DASHBOARD_BROADCAST_MESSAGE_TOGGLED",
                                    msg.getPayload());
                        });
    }
}