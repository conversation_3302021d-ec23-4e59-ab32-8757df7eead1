package com.orbis.messaging.sockets;

import java.io.Serializable;

import com.orbis.web.content.vc.View;

public class VCDashboardAdminConnectedEvent implements Serializable
{
    private static final long serialVersionUID = -3690502792415621327L;

    private final String view;

    public VCDashboardAdminConnectedEvent(View view)
    {
        this.view = view.name();
    }

    public String getView()
    {
        return view;
    }
}
