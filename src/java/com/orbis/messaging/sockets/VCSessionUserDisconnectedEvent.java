package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionUserDisconnectedEvent implements Serializable
{
    private static final long serialVersionUID = -951468450692598683L;

    private final int sessionId;

    private final String username;

    private final String fullName;

    private final String img;

    public VCSessionUserDisconnectedEvent(int sessionId, String username,
            String fullName, String img)
    {
        this.sessionId = sessionId;
        this.username = username;
        this.fullName = fullName;
        this.img = img;
    }

    public String getUsername()
    {
        return username;
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getFullName()
    {
        return fullName;
    }

    public String getImg()
    {
        return img;
    }
}