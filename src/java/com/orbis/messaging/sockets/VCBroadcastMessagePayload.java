package com.orbis.messaging.sockets;

import java.io.Serializable;

public abstract class VCBroadcastMessagePayload implements Serializable
{
    private static final long serialVersionUID = -8586661447263350857L;

    private final boolean checked;

    private final String message;

    public VCBroadcastMessagePayload(boolean checked, String message)
    {
        this.checked = checked;
        this.message = message;
    }

    public boolean isChecked()
    {
        return checked;
    }

    public String getMessage()
    {
        return message;
    }
}
