package com.orbis.messaging.sockets;

import java.util.Arrays;
import java.util.function.Consumer;

import jakarta.websocket.Session;

import org.json.JSONObject;

import com.orbis.utils.MapBuilder;

/**
 * Broadcasts messages to users connected to a websocket.
 * 
 * <AUTHOR>
 *
 */
public interface SocketBroadcaster
{
    public default void broadcast(Session session, String msgType, Object msg,
            Consumer<Session> onClosed)
    {
        broadcast(session, msgType, msg, onClosed, false);
    }

    /**
     * 
     * @param session
     * @param msgType
     * @param msg
     * @param onClosed
     * @param force
     *            - Force the session to receive the broadcast even if the
     *            websocket session isn't subscribed to the event.
     */
    public default void broadcast(Session session, String msgType, Object msg,
            Consumer<Session> onClosed, boolean force)
    {
        Boolean isOpen = null;
        synchronized (session)
        {
            try
            {
                isOpen = session.isOpen();
                if (isOpen)
                {
                    if (force || Arrays.asList(SocketEndPoint.getSub(session))
                            .contains(msgType))
                    {
                        String message = new JSONObject(
                                new MapBuilder<String, Object>()
                                        .put("msgType", msgType).put("msg", msg)
                                        .build()).toString();
                        session.getBasicRemote().sendText(message);
                    }
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        if (isOpen != null && !isOpen)
        {
            onClosed.accept(session);
        }
    }
}
