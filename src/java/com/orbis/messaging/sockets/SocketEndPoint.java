package com.orbis.messaging.sockets;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;

import org.springframework.beans.factory.InitializingBean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

@ServerEndpoint(value = AbstractSocketEndPoint.ORBIS_SOCKETS_ENDPOINT_PREFIX
        + "socket", configurator = SpringConfigurator.class)
public class SocketEndPoint extends AbstractSocketEndPoint
        implements InitializingBean
{
    private static final String PING = "PING";

    private static final String PONG = "PONG";

    private final static SocketConfig CONFIG = new SocketConfig();

    private static SocketEventPublisher PUBLISHER;

    private Timer ticker;

    private final Set<Session> sessions = Collections
            .synchronizedSet(new HashSet<>());

    @Override
    public void afterPropertiesSet() throws Exception
    {
        SocketConfigBootstrapper.bootstrap(CONFIG);
        PUBLISHER = new SocketEventPublisher(CONFIG);

        ticker = new Timer();
        ticker.schedule(new TimerTask()
        {
            @Override
            public void run()
            {
                // thread-safe iteration
                for (Session s : sessions.toArray(new Session[0]))
                {
                    try
                    {
                        synchronized (s)
                        {
                            s.getBasicRemote().sendText(PING);
                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
            }
        }, 0, TimeUnit.SECONDS.toMillis(20));
    }

    public static void publish(Object msg)
    {
        PUBLISHER.publish(msg);
    }

    @OnOpen
    public void onOpen(Session session)
    {
        sessions.add(session);

        getAuthorizedSubbedEventsForOpen(session).stream()
                .filter(se -> se.getOnOpen() != null)//
                .collect(Collectors.toMap(SocketEvent::getOnOpen, k -> k,
                        (f, s) -> f))
                .forEach((c, se) -> {
                    try
                    {
                        c.accept(session);
                    }
                    catch (Exception e)
                    {
                        se.error(e, session);
                    }
                });
    }

    public List<SocketEvent> getAuthorizedSubbedEventsForOpen(Session session)
    {
        Map<Predicate<Session>, Boolean> authCache = new HashMap<>();
        return Arrays.stream(getSub(session))//
                .map(CONFIG::getEventById)//
                .filter(Objects::nonNull)//
                .filter(e -> {
                    try
                    {
                        if (session.isOpen())
                            return authCache.computeIfAbsent(e.getOnAuth(),
                                    k -> k.test(session));
                        return false;
                    }
                    catch (Exception ex)
                    {
                        e.error(ex, session);
                        return false;
                    }
                })//
                .collect(Collectors.toList());
    }

    public List<SocketEvent> getAuthorizedSubbedEventsForClose(Session session)
    {
        return Arrays.stream(getSub(session))//
                .map(CONFIG::getEventById)//
                .filter(Objects::nonNull)//
                .collect(Collectors.toList());
    }

    @OnMessage
    public void onMessage(String message, Session session)
    {
        if (PONG.equals(message))
            return;

        try
        {
            JsonObject json = new JsonParser().parse(message).getAsJsonObject();

            String msgType = json.get("msgType").getAsString();

            CONFIG.optEventById(msgType).filter(e -> {
                try
                {
                    if (session.isOpen())
                        return e.getOnAuth().test(session);
                    e.close(session);
                    return false;
                }
                catch (Exception ex)
                {
                    e.error(ex, session);
                    return false;
                }
            }).ifPresent(e -> {
                JsonElement msg = json.get("msg");
                try
                {
                    e.message(msg, session);
                }
                catch (Exception ex)
                {
                    e.error(ex, session);
                }

                try
                {
                    e.publish(msg, PUBLISHER);
                }
                catch (Exception ex)
                {
                    e.error(ex, session);
                }
            });
        }
        catch (Exception e)
        {
            StringWriter errors = new StringWriter();
            e.printStackTrace(new PrintWriter(errors));
        }
    }

    @OnClose
    public void onClose(Session session)
    {
        sessions.remove(session);

        getAuthorizedSubbedEventsForClose(session).stream()
                .filter(se -> se.getOnClose() != null)//
                .collect(Collectors.toMap(SocketEvent::getOnClose, k -> k,
                        (f, s) -> f))
                .forEach((c, se) -> {
                    try
                    {
                        c.accept(session);
                    }
                    catch (Exception e)
                    {
                        se.error(e, session);
                    }
                });
    }

    @OnError
    public void onError(Session session, Throwable error)
    {
        StringWriter errors = new StringWriter();
        error.printStackTrace(new PrintWriter(errors));
    }

    /**
     * Gets the subscribed event types for the session. Throws an exception if
     * the user is unauthorized for an event type.
     * 
     * @param session
     * @return The subscribed event types for the session
     */
    public static String[] getSub(Session session)
    {
        return session.getRequestParameterMap().get("sub").get(0).toUpperCase()
                .split(",");
    }
}