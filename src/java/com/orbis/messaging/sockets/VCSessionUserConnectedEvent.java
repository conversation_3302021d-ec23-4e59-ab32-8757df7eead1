package com.orbis.messaging.sockets;

import java.io.Serializable;

public class VCSessionUserConnectedEvent implements Serializable
{
    private static final long serialVersionUID = -7647060447935411886L;

    private final String username;

    private final String fullName;

    private final int sessionId;

    private final String img;

    public VCSessionUserConnectedEvent(int sessionId, String username,
            String fullName, String img)
    {
        this.sessionId = sessionId;
        this.username = username;
        this.fullName = fullName;
        this.img = img;
    }

    public VCSessionUserConnectedEvent(VCSessionUserConnectedEvent payload)
    {
        this(payload.sessionId, payload.username, payload.fullName, payload.img);
    }

    public int getSessionId()
    {
        return sessionId;
    }

    public String getFullName()
    {
        return fullName;
    }

    public String getUsername()
    {
        return username;
    }

    public String getImg()
    {
        return img;
    }
}