package com.orbis.messaging.sockets;

import com.orbis.web.content.chat.payload.ChatMessagePayload;
import com.orbis.web.content.chat.payload.VCSessionPayload;

public class VCDashboardChatSendMessageEvent
        extends VCSessionUserChatSendMessageEvent
{
    private static final long serialVersionUID = 316818368382264293L;

    private final VCSessionPayload vcSession;

    public VCDashboardChatSendMessageEvent(int chatId, ChatMessagePayload message,
            VCSessionPayload vcSession)
    {
        super(chatId, message);
        this.vcSession = vcSession;
    }

    public VCSessionPayload getVcSession()
    {
        return vcSession;
    }
}
