package com.orbis.tripleLookup;

import java.util.List;

import org.hibernate.HibernateException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.PortalUtils;
import com.orbis.portal.QueryCallback;
import com.orbis.utils.LookupUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.crm.Company;
import com.orbis.web.content.crm.Organization;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.servlet.http.HttpServletRequest;

public class TripleLookupHelper
{
    public static Organization getTripleLookupOrganization(
            HttpServletRequest request)
    {
        Organization ret = null;
        int orgId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupOrganizationId", 0);
        if (orgId > 0)
        {
            ret = (Organization) PortalUtils.getHt().load(Organization.class,
                    orgId);
        }
        return ret;
    }

    public static Company getTripleLookupDivision(HttpServletRequest request)
    {
        Company ret = null;
        int divId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupDivisionId", 0);
        if (divId > 0)
        {
            ret = (Company) PortalUtils.getHt().load(Company.class, divId);
        }
        return ret;
    }

    public static UserDetailsImpl getTripleLookupUser(HttpServletRequest request)
    {
        UserDetailsImpl ret = null;
        int userId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupUserId", 0);
        if (userId > 0)
        {
            ret = (UserDetailsImpl) PortalUtils.getHt().load(UserDetailsImpl.class,
                    userId);
        }
        return ret;
    }

    public static List<Object[]> getTripleLookupDivSelectOptions(
            HttpServletRequest request)
    {
        List<Object[]> ret = null;
        Integer orgId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupOrganizationId", 0);
        Integer userId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupUserId", 0);
        if (orgId > 0)
        {
            ret = PortalUtils.getHt().find(
                    "select d.id, d.name, d.organization.name from Company d where d.organization.id=? and d.userStatus='Active' and d.deleted=false",
                    orgId);
        }
        else
        {
            ret = PortalUtils.getHt().find(
                    "select cu.company.organization.id, cu.company.organization.name cu.company.id, cu.company.name from CompanyUsers cu where cu.user.id=? and cu.company.userStatus='Active' and cu.company.deleted",
                    userId);
        }
        return ret;
    }

    public static List<Object[]> getTripleLookupUserSelectOptions(
            HttpServletRequest request)
    {
        List<Object[]> ret = null;
        Integer divId = ServletRequestUtils.getIntParameter(request,
                "tripleLookupDivisionId", 0);
        ret = PortalUtils.getHt().find(
                "select cu.user.id, cu.user.firstAndLastName, cu.user.username from CompanyUsers cu where cu.company.id=? and cu.user.userStatus='Active' and cu.user.deleted=false and cu.user.enabled=true",
                divId);
        return ret;
    }

    public static String getJsonResponseForTripleLookupDivResults(
            List<Object[]> results)
    {
        JSONArray jsonResponse = new JSONArray();
        String ret = "";
        JSONObject result;
        try
        {
            for (Object[] div : results)
            {
                result = new JSONObject();
                result.put("id", div[0]);
                String label = StringUtils.fillInPlaceholders("[2] - [1] Division",
                        div);
                result.put("label", label);
                result.put("value", label);
                result.put("orgName", div[2]);
                result.put("orgId", div[3]);

                jsonResponse.put(result);
            }

            ret = jsonResponse.toString();
        }
        catch (JSONException e)
        {

        }
        return ret;
    }

    public static String getTripleLookupUserQuery(String searchTerm,
            boolean includeOrderBy)
    {
        StringBuilder hibQuery = new StringBuilder();
        hibQuery.append(
                "select cu.user.id, cu.user.preferredFirstName, cu.user.lastName, cu.user.username, count(*)  ");
        hibQuery.append(
                "from CompanyUsers cu join cu.user join cu.company join cu.company.organization where cu.user.deleted=false and cu.user.temprary='N' and cu.user.userStatus='Active' and cu.company.deleted=false and cu.company.organization.deleted=false and cu.company.userStatus='Active' and cu.company.organization.userStatus='Active' and ");
        hibQuery.append(LookupUtils.getUserSearchTermHqlWhereClause(searchTerm,
                "cu.user", null));
        if (includeOrderBy)
        {
            hibQuery.append(
                    " order by cu.user.preferredFirstName group by cu.user.id, cu.user.preferredFirstName, cu.user.lastName, cu.user.username");
        }
        final String hql = hibQuery.toString();
        return hql;
    }

    public static String getTripleLookupResponseForUserQuery(final String hql)
    {
        final CommandQueryTemplate ht = PortalUtils.getHt();
        List<Object[]> hqlData = ht.<List<Object[]>>executeFind(
                (QueryCallback) session -> {
                    Query query = ht.createQuery(session, hql);
                    query.setFirstResult(0);
                    query.setMaxResults(20);
                    return query.getResultList();
                });
        JSONArray jsonResponse = new JSONArray();
        String ret = "";
        JSONObject result;
        try
        {
            for (Object[] data : hqlData)
            {
                result = new JSONObject();
                result.put("id", data[0]);
                String label = StringUtils.fillInPlaceholders("[1] [2] ([3])",
                        data);
                result.put("label", label);
                result.put("value", label);
                result.put("cuCount", data[4]);

                jsonResponse.put(result);
            }

            ret = jsonResponse.toString();
        }
        catch (JSONException e)
        {

        }
        return ret;
    }
}
