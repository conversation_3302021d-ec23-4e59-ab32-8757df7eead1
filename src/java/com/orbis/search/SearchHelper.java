package com.orbis.search;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.orbis.configuration.security.core.csrf.CsrfTokenHolder;
import com.orbis.configuration.security.core.csrf.CsrfUtils;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.ClassUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.metamodel.MappingMetamodel;
import org.hibernate.persister.entity.AbstractEntityPersister;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFQuestion;
import com.orbis.email.EmailModel;
import com.orbis.email.EmailRecipient;
import com.orbis.expressions.Functions;
import com.orbis.jqgrid.JQGridColumn;
import com.orbis.jqgrid.JQGridExcelExportTask;
import com.orbis.jqgrid.JQGridHelper;
import com.orbis.jqgrid.JQGridModel;
import com.orbis.jqgrid.JQGridSearch;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.search.criteria.CriteriaAnswer;
import com.orbis.search.criteria.CriteriaAnswerMatrixHQLClause;
import com.orbis.search.criteria.CriteriaAnswerMatrixUIChoice;
import com.orbis.search.criteria.CriteriaAnswerValueChoice;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaPlugin;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.criteria.CriteriaQuestionHQLBuilder;
import com.orbis.search.criteria.CriteriaQuestionHandler;
import com.orbis.search.criteria.CriteriaQuestionJsonOptionChoices;
import com.orbis.search.criteria.CriteriaQuestionMatrixColumnLabel;
import com.orbis.search.criteria.CriteriaQuestionMatrixRowLabel;
import com.orbis.search.criteria.CriteriaQuestionMultiChoiceHQLBuilder;
import com.orbis.search.entity.EmailerEmailBeanPath;
import com.orbis.search.entity.EmailingEmailer;
import com.orbis.search.entity.EmailingModel;
import com.orbis.search.entity.Entity;
import com.orbis.search.entity.EntityAttribute;
import com.orbis.search.entity.Relationship;
import com.orbis.search.entity.Relationship.TYPE;
import com.orbis.utils.ActionHelper;
import com.orbis.utils.ArrayUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.I18nMessageList;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.utils.function.OrbisPredicates;
import com.orbis.web.content.acrm.SkillsQuestion;
import com.orbis.web.content.grid.NameConversionParameters;
import com.orbis.web.content.grid.NameValuePair;
import com.orbis.web.content.lv.LastViewedHelper;
import com.orbis.web.content.pc.PCHelper;
import com.orbis.web.site.SiteElement;
import org.springframework.security.web.csrf.CsrfToken;

/**
 * Helper functions for the search framework.
 *
 * <AUTHOR>
 */
public class SearchHelper
{
    private static final Log logger = LogFactory.getLog(SearchHelper.class);

    public static final String SEARCH_MODEL_SESSION_KEY = "SEARCH_MODEL_SESSION_KEY";

    public static final String SERIALIZED_MODEL_PATH = "/content/documents/searchModels/";

    public static final String SERIALIZED_MODEL_FILENAME = "SearchModel.serialized";

    /**
     * Returns a SearchModel from the user's session.
     */
    public static SearchModel getSearchModelFromSession(HttpServletRequest request,
            String searchModelSessionKey)
    {
        SearchModel searchModel = null;

        if (request.getSession().getAttribute(searchModelSessionKey) != null)
        {
            searchModel = (SearchModel) request.getSession()
                    .getAttribute(searchModelSessionKey);
        }

        return searchModel;
    }

    /**
     * Puts the SearchModel into the user's session.
     */
    public static SearchModel setSearchModelToSession(SearchModel searchModel,
            HttpServletRequest request)
    {
        request.getSession().setAttribute(SEARCH_MODEL_SESSION_KEY, searchModel);
        return searchModel;
    }

    public static void deleteSearchModelFile(String urlPath)
    {
        if (!StringUtils.isEmpty(urlPath))
        {
            FileUtils.deleteFileAndFolder(PortalUtils.getRealPath(urlPath));
        }
    }

    public static JQGridModel createJQGridModel(SearchModel searchModel,
            Locale locale)
    {

        // SET UP A NEW GRID-MODEL...
        JQGridModel gridModel = new JQGridModel();

        // SET UP MASTER-GRID...
        Entity masterEntity = searchModel.getMasterEntity();
        cleanQuestionsWithDuplicateKeys(masterEntity);
        String masterHQL = buildHQL(masterEntity, true);
        JQGridSearch masterGrid = new JQGridSearch(masterEntity.getEntityHqlName(),
                masterEntity.getEntityHqlAlias(), masterEntity.getEntityHqlId(),
                masterHQL, getJQGridColumns(masterEntity, locale), locale);

        PortalConfig conf = PortalConfigHelper
                .getPortalConfig(PortalConfig.GRID_SHOW_NEW_WINDOW_BUTTON);

        if ("1".equals(conf.getOrbisValue()) && masterGrid.isMaster()
                && searchModel.isCanViewDetails())
        {
            JSONArray colModel = masterGrid.getColModelJSON();

            try
            {
                JSONArray newColModel = new JSONArray();
                JSONObject model = new JSONObject();
                model.put("name", "");
                model.put("isNewWindowsCol", true);
                model.put("index", 0);
                if (PortalUtils.isSpiralRobot())
                {
                    model.put("label",
                            PortalUtils.getI18nMessage(
                                    "i18n.SearchHelper.ViewinWind1799747768487552",
                                    null, locale));

                }
                model.put("sortable", false);
                model.put("hidden", false);
                model.put("formatter", "showlink");
                model.put("align", "center");
                model.put("width", 150);
                newColModel.put(model);
                for (int i = 0; i < colModel.length(); i++)
                {
                    newColModel.put(colModel.get(i));
                }

                masterGrid.setColModelJSON(newColModel);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        masterGrid.setSortIndex(masterEntity.getInitialSortIndex());
        masterGrid.setSortOrder(masterEntity.getInitialSortOrder());
        masterGrid.setSearchTypeLabel(masterEntity.getEntityLabel());
        if (searchModel != null)
        {
            masterGrid.setModuleClassName(searchModel.getOwnerModuleClassName());
            masterGrid.setModuleId(searchModel.getOwnerModuleId());
        }
        masterGrid.setEntity(masterEntity);

        if (masterEntity.getDataSausageClass() != null)
        {
            masterGrid.setDataSausageClassName(
                    masterEntity.getDataSausageClass().getName());
        }
        else
        {
            masterGrid.setDataSausageClassName(
                    SearchJQGridDataSausage.class.getName());
        }

        gridModel.setMaster(masterGrid);

        // SET UP DETAIL-GRIDS...
        if (masterEntity.getRelationships() != null)
        {
            for (Relationship r : masterEntity.getDetailRelationships())
            {
                Entity detailEntity = r.getRelatedEntity();
                cleanQuestionsWithDuplicateKeys(detailEntity);
                detailEntity.setSearchModel(searchModel);
                String detailHQL = "";

                switch (r.getType())
                {
                    case CUSTOM:
                        detailHQL = getCustomDetailHQL(detailEntity, r);
                        break;

                    case DETAIL:
                        detailHQL = getDetailRelationshipHQL(detailEntity, r, null);
                        break;

                    case COUNT:
                        detailHQL = "";
                        break;

                    default:
                        detailHQL = buildHQL(detailEntity, false);
                        break;
                }

                JQGridSearch detailGrid = new JQGridSearch(
                        detailEntity.getEntityHqlName(),
                        detailEntity.getEntityHqlAlias(),
                        detailEntity.getEntityHqlId(), detailHQL,
                        getJQGridColumns(detailEntity, locale),
                        r.getForiegnKeyHql(), locale);

                detailGrid.setSortIndex(detailEntity.getInitialSortIndex());
                detailGrid.setSortOrder(detailEntity.getInitialSortOrder());
                detailGrid.setSearchTypeLabel(detailEntity.getEntityLabel());
                detailGrid.setEntity(detailEntity);
                detailGrid.setDetailType(r.getType());

                if (r.getRelatedEntity().getGridBuilderClass() != null)
                {
                    detailGrid.setGridBuilderClassName(
                            r.getRelatedEntity().getGridBuilderClass().getName());
                }

                gridModel.addDetail(detailEntity.getEnityKey(), detailGrid);

                if (detailEntity.getDataSausageClass() != null)
                {
                    detailGrid.setDataSausageClassName(
                            detailEntity.getDataSausageClass().getName());
                }
                else
                {
                    detailGrid.setDataSausageClassName(
                            SearchJQGridDataSausage.class.getName());
                }
            }
        }

        return gridModel;
    }

    public static void bindAnswersFromRequest(Entity entity,
            HttpServletRequest request)
    {
        // BIND ANSWERS TO ENTITY QUESTIONS...
        entity.getCriteriaModel().getCriteriaQuestions().stream().forEach(q -> {
            q.setPreSelectedChoices(null);
            if (!q.isForwardAnswer())
            {
                bindQuestion(entity, q, request);
            }
        });

        // BIND MASTER-ENTITY RELATIONSHIP QUESTIONS...
        if (entity.isMaster() && entity.getRelationships() != null)
        {
            for (Relationship r : entity.getRelationships())
            {
                r.getRelatedEntity().setSearchModel(entity.getSearchModel());

                if (r.getRelationQuestion() != null && r.getType() != null
                        && CriteriaQuestion.TYPE_BOOLEAN
                                .equals(r.getRelationQuestion().getType())
                        && (r.getType() == TYPE.MANY_TO_ONE
                                || r.getType() == TYPE.ONE_TO_ONE))
                {
                    bindAnswer_Boolean(r.getRelatedEntity(),
                            r.getRelationQuestion(), request);
                }

                bindAnswersFromRequest(r.getRelatedEntity(), request);
            }
        }

    }

    public static void bindQuestion(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        if (CriteriaQuestion.TYPE_TEXT.equals(q.getType()))
        {
            bindAnswer_Text(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_DATE.equals(q.getType()))
        {
            bindAnswer_Date(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_NUMBER.equals(q.getType())
                || CriteriaQuestion.TYPE_FLOAT.equals(q.getType()))
        {
            bindAnswer_Number(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_RATING_RANGE.equals(q.getType()))
        {
            bindAnswer_RatingRange(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_IS_EMPTY.equals(q.getType()))
        {
            bindAnswer_Boolean(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_BOOLEAN.equals(q.getType()))
        {
            bindAnswer_Boolean(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_CHOICE_MULTI.equals(q.getType()))
        {
            bindAnswer_ChoiceMulti(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_CHOICE_SINGLE.equals(q.getType()))
        {
            bindAnswer_ChoiceSingle(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_FILE_UPLOAD.equals(q.getType()))
        {
            bindAnswer_FileUpload(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_TREE.equals(q.getType()))
        {
            bindAnswer_Tree(entity, q, request);
        }
        else if (CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType()))
        {
            bindAnswer_Matrix(entity, q, request);
        }
    }

    public static void bindAnswer_Matrix(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        CriteriaAnswer a = null;
        String paramName = entity.getPrefix() + q.getParamName();
        String[] answerValues = getParameterValues(request, paramName);
        String opValue = getParamValue(request, paramName + "_op");

        if (answerValues != null && q.getMatrixRowLabels() != null
                && q.getMatrixColumnLabels() != null
                && !StringUtils.isEmpty(opValue))
        {
            List<String> matrixUIChoices = new ArrayList<>();
            List<String> matrixHQLClauses = new ArrayList<>();
            String answerValue = null;
            int rowIndex = 0;
            int colIndex = 0;
            String rowLabel = null;
            String colLabel = null;
            StringBuilder sb = null;

            for (int i = 0; i < answerValues.length; i++)
            {
                answerValue = answerValues[i];

                matrixUIChoices.add(answerValue);

                rowIndex = Integer.valueOf(
                        answerValue.substring(0, answerValue.indexOf("_")));

                colIndex = Integer.valueOf(
                        answerValue.substring(answerValue.indexOf("_") + 1));

                rowLabel = q.getMatrixRowLabels().get(rowIndex);
                colLabel = q.getMatrixColumnLabels().get(colIndex);

                if (rowLabel != null && colLabel != null)
                {
                    sb = new StringBuilder();
                    sb.append("^").append(rowLabel).append("~").append(colLabel)
                            .append("^");

                    matrixHQLClauses.add(sb.toString());
                }
            }

            if (!matrixHQLClauses.isEmpty())
            {
                q.setMatrixOperation(opValue);
                if (CriteriaQuestion.MATRIX_OPERATION_EXACT.equals(opValue))
                {
                    StringBuilder s = new StringBuilder();
                    s.append("^");
                    for (String clause : matrixHQLClauses)
                    {
                        s.append(clause.substring(1));
                    }
                    matrixHQLClauses = new ArrayList<>();
                    matrixHQLClauses.add(s.toString());
                }

                a = new CriteriaAnswer();
                a.setMatrixUIChoices(matrixUIChoices);
                a.setMatrixHQLClauses(matrixHQLClauses);
            }
        }

        q.setAnswer(a);
    }

    public static void bindAnswer_Text(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        CriteriaAnswer a = null;

        String paramName = entity.getPrefix() + q.getParamName();

        String answerValue = getParamValue(request, paramName);
        if (!StringUtils.isEmpty(answerValue))
        {
            a = new CriteriaAnswer();
            a.setValue(answerValue);
        }
        q.setAnswer(a);

        String opValue = getParamValue(request, paramName + "_op");
        if (!StringUtils.isEmpty(opValue))
        {
            q.setOperation(opValue);
        }
    }

    public static String getParamValue(HttpServletRequest request, String paramName)
    {
        String answerValue = "";

        if (null != request.getParameter(paramName)
                && !StringUtils.isEmpty(request.getParameter(paramName)))
        {
            answerValue = request.getParameter(paramName);
        }
        else if (null != request.getAttribute(paramName))
        {
            answerValue = (String) request.getAttribute(paramName);
        }

        return answerValue;
    }

    private static String[] getParameterValues(HttpServletRequest request,
            String paramName)
    {
        String[] answerValue = null;

        if (null != request.getParameterValues(paramName))
        {
            answerValue = request.getParameterValues(paramName);
        }
        else if (null != request.getAttribute(paramName))
        {
            Object obj = request.getAttribute(paramName);
            if (obj instanceof String)
            {
                answerValue = new String[1];
                answerValue[0] = (String) obj;
            }
            else
            {
                answerValue = (String[]) obj;
            }
        }

        return answerValue;
    }

    public static void bindAnswer_Date(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        CriteriaAnswer a = null;

        String fromParamName = entity.getPrefix() + q.getParamName() + "From";
        String toParamName = entity.getPrefix() + q.getParamName() + "To";

        String fromValue = getParamValue(request, fromParamName);
        String toValue = getParamValue(request, toParamName);

        if (!StringUtils.isEmpty(fromValue) || !StringUtils.isEmpty(toValue))
        {
            a = new CriteriaAnswer();
            a.setFromValue(fromValue);
            a.setToValue(toValue);
            q.setDateFormatForQuery(
                    DateUtils.getDatepickerFormat(request, fromParamName));
        }

        q.setAnswer(a);
    }

    public static void bindAnswer_Boolean(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        String paramName = entity.getPrefix() + q.getParamName();

        String answerValue = getParamValue(request, paramName);

        if (StringUtils.isEmpty(answerValue))
        {
            q.setAnswer(null);
        }
        else
        {
            CriteriaAnswer a = new CriteriaAnswer();
            a.setValue(answerValue);

            q.setAnswer(a);
        }
    }

    public static void bindAnswer_Number(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        CriteriaAnswer a = null;

        String paramName = entity.getPrefix() + q.getParamName();
        String num1ParamName = entity.getPrefix() + q.getParamName() + "From";
        String num2ParamName = entity.getPrefix() + q.getParamName() + "To";

        String num1Value = getParamValue(request, num1ParamName);
        String num2Value = getParamValue(request, num2ParamName);

        if (!StringUtils.isEmpty(num1Value) || !StringUtils.isEmpty(num2Value))
        {
            a = new CriteriaAnswer();
            a.setFromValue(num1Value);
            a.setToValue(num2Value);
        }

        q.setAnswer(a);

        String opValue = getParamValue(request, paramName + "_op");
        if (!StringUtils.isEmpty(opValue))
        {
            q.setNumberOperation(opValue);
        }
    }

    public static void bindAnswer_RatingRange(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        CriteriaAnswer a = null;

        String paramName = entity.getPrefix() + q.getParamName();

        String fromRatingValue = getParamValue(request, paramName + "RatingFrom");
        String toRatingValue = getParamValue(request, paramName + "RatingTo");

        if ((fromRatingValue != null && StringUtils.isNumber(fromRatingValue))
                || (toRatingValue != null && StringUtils.isNumber(toRatingValue)))
        {
            a = new CriteriaAnswer();
            a.setRatingFromValue(fromRatingValue);
            a.setRatingToValue(toRatingValue);
        }

        String NAchoice = getParamValue(request, paramName + "RatingNAChoice");
        if (!StringUtils.isEmpty(NAchoice))
        {
            q.setRatingNAChoice(NAchoice);
        }

        q.setAnswer(a);
    }

    public static void bindAnswer_ChoiceMulti(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        q.setAnswer(bindChoiceAnswer(entity, q, request));
    }

    public static void bindAnswer_ChoiceSingle(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        q.setAnswer(bindChoiceAnswer(entity, q, request));
    }

    public static CriteriaAnswer bindChoiceAnswer(Entity entity,
            CriteriaQuestion q, HttpServletRequest request)
    {
        CriteriaAnswer a = q.getAnswer();
        String paramName = entity.getPrefix() + q.getParamName();

        String[] answerValues = getParameterValues(request, paramName);
        String opValue = getParamValue(request, paramName + "_andOr");

        Map<String, Boolean> selectionMap = new HashMap<>();

        if ((answerValues != null && !StringUtils.isEmpty(opValue))
                || (CriteriaAnswer.CHOICE_OPERATION_NULL.equals(opValue)
                        || CriteriaAnswer.CHOICE_OPERATION_NOT_NULL
                                .equals(opValue)))
        {
            if (a == null)
            {
                a = new CriteriaAnswer();
            }

            a.setAndOrValue(opValue);

            for (String key : q.getOptionChoices().keySet())
            {
                selectionMap.put(key, false);
            }

            if (answerValues != null)
            {
                for (int i = 0; i < answerValues.length; i++)
                {
                    selectionMap.put(answerValues[i], true);
                }

                if (selectionMap.containsKey(q.getParamName() + "other"))
                {
                    a.setValueOther(getParamValue(request, paramName + "_other"));
                }
            }
            a.setValueChoices(selectionMap);
        }
        else if (answerValues == null)
        {
            a = null;
        }

        return a;
    }

    public static void bindAnswer_FileUpload(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        String paramName = entity.getPrefix() + q.getParamName();

        String answerValue = getParamValue(request, paramName);

        if (StringUtils.isEmpty(answerValue))
        {
            q.setAnswer(null);
        }
        else
        {
            CriteriaAnswer a = new CriteriaAnswer();
            a.setValue(answerValue);

            q.setAnswer(a);
        }

    }

    public static void bindAnswer_Tree(Entity entity, CriteriaQuestion q,
            HttpServletRequest request)
    {
        if (q.getQuestionHandlerClass() != null && CriteriaQuestionHandler.class
                .isAssignableFrom(q.getQuestionHandlerClass()))
        {
            try
            {
                CriteriaQuestionHandler h = q.getQuestionHandlerClass()
                        .newInstance();

                h.bindAnswer(entity, q, request);
            }
            catch (Exception e)
            {
            }
        }
        else
        {
            String paramName = entity.getPrefix() + q.getParamName();
            String valueTree = getParamValue(request, paramName);
            String andOr = getParamValue(request, paramName + "_andOr");

            if (!StringUtils.isEmpty(valueTree) && andOr != null)
            {
                CriteriaAnswer a = new CriteriaAnswer();
                a.setAndOrValue(andOr);
                a.setValue(valueTree);
                q.setAnswer(a);
            }
        }
    }

    public static SearchCustomRelationshipInterface getCustomSearchInterface(
            Entity detailEntity, Relationship r)
    {
        SearchCustomRelationshipInterface scri = null;

        try
        {
            try
            {
                scri = r.getSearchCustomRelationshipInterface()
                        .getConstructor(Entity.class, Relationship.class)
                        .newInstance(detailEntity, r);
            }
            catch (Exception e)
            {
                scri = r.getSearchCustomRelationshipInterface().newInstance();
            }
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }

        return scri;
    }

    private static String getCustomDetailHQL(Entity detailEntity, Relationship r)
    {
        SearchCustomRelationshipInterface scri = getCustomSearchInterface(
                detailEntity, r);

        String detailHql = scri != null ? scri.getDetailEntityHQL(r) : "";

        return detailHql;
    }

    public static String buildFullDescription(Entity entity,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        // Make sure the entity's "searchModel" transient-property is set...
        if (null == entity.getSearchModel())
        {
            throw new RuntimeException("SEARCH MODEL NOT SET!");
        }

        // construct 'where' clause...
        StringBuilder description = new StringBuilder(
                buildDescription(entity, locale));

        // append 'where' clauses for Related Entities...
        description.append(buildRelatedDescription(entity, request));

        String hql = description.length() > 0 ? description.toString() : "";
        return hql;
    }

    public static String getFilterDescriptions(HttpServletRequest request,
            SearchModel searchModel)
    {
        return buildFullDescription(searchModel.getMasterEntity(), request);
    }

    public static Relationship getDetailRelationship(JQGridSearch jqSearch,
            JQGridModel gridModel, SearchModel searchModel)
    {
        Relationship relationship = null;
        if (searchModel.getMasterEntity() != null)
        {
            List<Relationship> relationships = searchModel.getMasterEntity()
                    .getRelationships();
            for (Relationship r : relationships)
            {
                if (jqSearch.equals(
                        gridModel.getDetail(r.getRelatedEntity().getEnityKey())))
                {
                    relationship = r;
                    break;
                }
            }
        }
        return relationship;
    }

    public static String getDetailRelationshipHQL(Entity detailEntity,
            Relationship r, Integer masterId)
    {
        String detailHql = "";
        try
        {
            SearchDetailRelationshipInterface scri = r
                    .getDetailRelationshipInterface().newInstance();

            detailHql = scri.getDetailEntityHQL(r, masterId);
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }

        return detailHql;
    }

    public static String buildRelationshipHQL(Entity masterEntity,
            Relationship relationship)
    {
        final Entity relatedEntity = relationship.getRelatedEntity();

        // Make sure the entity's "searchModel" transient-property is set...
        if (null == relatedEntity.getSearchModel())
        {
            throw new RuntimeException("SEARCH MODEL NOT SET!");
        }

        FromWhereClause fromWhereClause = new FromWhereClause(
                relatedEntity.getEntityHqlAlias());

        String relatedEntitySuperclass = null;
        final boolean relationQuestionAnswered = isRelationQuestionAnswered(
                relationship);
        final CriteriaQuestion relationQuestion = relationQuestionAnswered
                ? relationship.getRelationQuestion()
                : null;
        if (relationQuestionAnswered && relationQuestion != null)
        {
            masterEntity.getAttributes().put(relationQuestion.getQuestionText(),
                    relationQuestion.getAnswer().getValue());
        }
        final boolean ignoreNull = relationship.isAlwaysPresent()
                || (relationQuestionAnswered && relationQuestion != null
                        && relationQuestion.getAnswer().getBooleanValue());
        boolean hasSubqueries = false;
        boolean joinMaster = false;

        if (!ignoreNull)
        {
            try
            {
                AbstractEntityPersister entityPersister = (AbstractEntityPersister) ((MappingMetamodel) PortalUtils
                        .getHt().getSessionFactory().getMetamodel())
                        .getEntityDescriptor(
                                relatedEntity.getEntityClass().getName());
                // we need to use concrete class of the related entity if it is
                // mapped similar
                // to CompetencyAnticipated (subclasses where each subclass has
                // a many-to-one
                // mapped inside the subclass)
                relatedEntitySuperclass = relatedEntity.isUseConcreteClass()
                        ? relatedEntity.getEntityClass().getName()
                        : entityPersister.getMappedSuperclass();
            }
            catch (Exception e)
            {

            }
        }

        if (relationship.getMasterJoinHql() == null)
        {
            relationship.setMasterJoinHql(
                    String.format("%s.%s", masterEntity.getEntityHqlAlias(),
                            masterEntity.getEntityHqlId()));
        }

        List<CriteriaGroup> criteriaGroups = relatedEntity.getCriteriaModel()
                .getCriteriaGroups();
        List<CriteriaQuestion> multiChoiceQuestions = new ArrayList<>();
        // List<CriteriaAnswer> invertedAnswers = new ArrayList<>();
        List<String> answerTypes = new ArrayList<>();
        List<Boolean> questionIsEquals = new ArrayList<>();
        if (!criteriaGroups.isEmpty()
                && (!relationQuestionAnswered || (relationQuestion != null
                        && relationQuestion.getAnswer().getBooleanValue())))
        {
            List<CriteriaQuestion> allQuestions = criteriaGroups.stream()
                    .map(CriteriaGroup::getQuestions).flatMap(List::stream)
                    .collect(Collectors.toList());
            List<CriteriaQuestion> nottableQuestions = allQuestions.stream()
                    .filter(CriteriaQuestion::isNottableAnswered)
                    .collect(Collectors.toList());
            multiChoiceQuestions.addAll(nottableQuestions.stream()
                    .filter(CriteriaQuestion::isMultiChoiceAnswered)
                    .collect(Collectors.toList()));

            /*
             * for (CriteriaQuestion q : multiChoiceQuestions) { CriteriaAnswer
             * answer = q.getAnswer(); Map<String, Boolean> valueChoices =
             * answer.getValueChoices(); if (valueChoices.values().stream()
             * .filter(OrbisPredicates.booleanCompare(true)) .count() >
             * valueChoices.size() >> 1) { if (answer.invertOperation()) {
             * invertedAnswers.add(answer); } } }
             */
            if (!multiChoiceQuestions.isEmpty())
            {
                if (multiChoiceQuestions.stream().map(CriteriaQuestion::getAnswer)
                        .map(CriteriaAnswer::getAndOrValue).distinct().count() == 1)
                {
                    CriteriaQuestion firstQuestion = multiChoiceQuestions.get(0);
                    CriteriaAnswer firstAnswer = firstQuestion.getAnswer();
                    if ((firstAnswer.isOrOperation()
                            || firstAnswer.isNullOperation())
                            && !firstQuestion.isAnsweredNot())
                    {
                        answerTypes.add(firstAnswer.getAndOrValue());
                        questionIsEquals.add(CriteriaQuestion.OPERATION_EQUALS
                                .equals(firstQuestion.getOperation()));
                        multiChoiceQuestions.clear();
                    }
                }
            }
            else if (!nottableQuestions.isEmpty() && nottableQuestions.stream()
                    .map(CriteriaQuestion::isAnsweredNot)
                    .allMatch(OrbisPredicates.booleanCompare(true)))
            {
                joinMaster = !ignoreNull;
            }
            if (!multiChoiceQuestions.isEmpty())
            {
                hasSubqueries = true;
                if (multiChoiceQuestions.stream()
                        .anyMatch(CriteriaQuestion::isAnsweredNot))
                {
                    joinMaster = !ignoreNull;
                }
            }
        }

        buildFromHQL(relatedEntity, fromWhereClause);

        List<String> existsQueries = new ArrayList<>();
        List<Integer> answerSelectedOptionCounts = new ArrayList<>();

        String mainJoinHql = String.format("%s = %s",
                relationship.getForiegnKeyHql(), relationship.getMasterJoinHql());
        String mainFromHql = fromWhereClause.getFromClause().toString();
        String hql = null;

        if (PortalUtils.getHt().findInt(String.format("SELECT COUNT(*) FROM %s",
                relatedEntity.getEntityClass().getSimpleName())) > 0)
        {
            if (!hasSubqueries)
            {
                if (!relationQuestionAnswered || ignoreNull)
                {
                    fromWhereClause.setSelectClause(new StringBuilder(String.format(
                            "select %s.%s", relatedEntity.getEntityHqlAlias(),
                            relatedEntity.getEntityHqlId())));
                    buildMasterWhereHql(relatedEntity, true, false, null,
                            ignoreNull, !ignoreNull && joinMaster, fromWhereClause);
                }
                else
                {
                    fromWhereClause.clearWhereClause();
                    if (!StringUtils.isEmpty(relatedEntity.getStaticWhereHql()))
                    {
                        fromWhereClause.appendToWhereClause(
                                relatedEntity.getStaticWhereHql());
                        fixMasterWhereHQL(false, fromWhereClause);
                    }
                }
                fromWhereClause.getWhereClause()
                        .append(fromWhereClause.getWhereClause().length() > 0
                                ? " and "
                                : " where ")
                        .append(mainJoinHql);
                existsQueries.add(fromWhereClause.toString());
            }
            else
            {
                if (relatedEntitySuperclass != null)
                {
                    mainFromHql = mainFromHql.replaceFirst(
                            String.format(" %s ",
                                    relatedEntity.getEntityClass().getSimpleName()),
                            String.format(" %s ",
                                    relatedEntitySuperclass));
                }

                buildRelationshipSubqueryHQL(relationship, multiChoiceQuestions,
                        existsQueries, answerTypes, answerSelectedOptionCounts,
                        questionIsEquals, fromWhereClause);
            }
        }
        else
        {
            StringBuilder whereClauseBuilder = new StringBuilder();
            if (!hasSubqueries)
            {
                whereClauseBuilder.append(" and (1 = ").append(
                        (relationQuestionAnswered ? !ignoreNull : joinMaster) ? 1
                                : 0);
            }
            else
            {
                for (CriteriaQuestion q : multiChoiceQuestions)
                {
                    whereClauseBuilder.append(" and ");
                    if (whereClauseBuilder.length() > 0)
                    {
                        whereClauseBuilder.append("(");
                    }
                    whereClauseBuilder.append("1 = ")
                            .append(q.isAnsweredNot() ? 1 : 0);
                }
            }
            whereClauseBuilder.append(")");
            hql = whereClauseBuilder.toString();
        }

        if (hql == null)
        {
            /*
             * for (int a = 0; a < invertedAnswers.size(); a++) {
             * invertedAnswers.get(a).invertOperation(); }
             */

            hql = buildRelationshipExistsHQL(masterEntity, relationship,
                    multiChoiceQuestions, existsQueries, answerTypes,
                    answerSelectedOptionCounts, questionIsEquals,
                    multiChoiceQuestions.isEmpty() && joinMaster);

            if (hasSubqueries)
            {
                boolean isSubclass = relatedEntitySuperclass != null;

                hql = buildRelationshipExistsSubqueryHQL(masterEntity, relationship,
                        mainJoinHql, mainFromHql, hql, joinMaster, isSubclass);
            }
        }

        return hql;
    }

    private static void buildRelationshipSubqueryHQL(Relationship relationship,
            List<CriteriaQuestion> multiChoiceQuestions, List<String> existsQueries,
            List<String> answerTypes, List<Integer> answerSelectedOptionCounts,
            List<Boolean> questionIsEquals, FromWhereClause fromWhereClause)
    {
        final Entity relatedEntity = relationship.getRelatedEntity();
        final String relatedEntityHqlAlias = relatedEntity.getEntityHqlAlias();
        final boolean relationQuestionAnswered = isRelationQuestionAnswered(
                relationship);
        final CriteriaQuestion relationQuestion = relationQuestionAnswered
                ? relationship.getRelationQuestion()
                : null;
        final boolean ignoreNull = relationship.isAlwaysPresent()
                || (relationQuestionAnswered && relationQuestion != null
                        && relationQuestion.getAnswer().getBooleanValue());
        final String joinHql = String.format("%s = %s",
                relationship.getForiegnKeyHql().replaceAll("^([^\\.]+)", "$1_"),
                relationship.getForiegnKeyHql());

        for (CriteriaQuestion q : multiChoiceQuestions)
        {
            final String questionKey = q.getQuestionKey();
            final CriteriaAnswer answer = q.getAnswer();
            final boolean isEquals = CriteriaQuestion.OPERATION_EQUALS
                    .equals(q.getOperation());
            final boolean hasMultipleSubqueries = isEquals
                    && (q.isMultiChoiceEquals()
                            || (q.isChoiceNull() && q.isAnsweredNot()));
            String questionSelectClause = null;
            String questionSelectClause2 = null;
            questionSelectClause = String.format("%s_%s", relatedEntityHqlAlias,
                    questionKey.substring(relatedEntityHqlAlias.length()));
            if (!isEquals || answer.isOrOperation() || answer.isNullOperation())
            {
                questionSelectClause = String.format("select %s",
                        questionSelectClause);
                if (hasMultipleSubqueries)
                {
                    questionSelectClause2 = questionSelectClause;
                }
            }
            else
            {
                if (hasMultipleSubqueries)
                {
                    questionSelectClause2 = String.format("select %s",
                            questionSelectClause);
                }
                questionSelectClause = String.format("select count(distinct %s)",
                        questionSelectClause);
            }

            final String initialFromClause = fromWhereClause.getFromClause()
                    .toString().replaceAll(String.format("( |,)(%s)( |,|\\.|$)",
                            relatedEntityHqlAlias), "$1$2_$3");
            final String outerJoinWhereClause = getRelationshipOuterJoinWhereClause(
                    fromWhereClause, relatedEntity.getStaticWhereHql(),
                    relatedEntityHqlAlias, true);
            final Map<String, String> outerJoinAliasJoinClauses = hasMultipleSubqueries
                    && fromWhereClause.getOuterJoinAliasJoinClauses() != null
                            ? Maps.newLinkedHashMap(
                                    fromWhereClause.getOuterJoinAliasJoinClauses())
                            : null;

            for (int c = 0; c < (hasMultipleSubqueries ? 2 : 1); c++)
            {
                fromWhereClause.setSelectClause(new StringBuilder(
                        c == 0 ? questionSelectClause : questionSelectClause2));
                fromWhereClause.setFromClause(new StringBuilder(initialFromClause));
                if (c == 1)
                {
                    fromWhereClause.setOuterJoinAliasJoinClauses(
                            outerJoinAliasJoinClauses);
                }
                buildMasterWhereHql(relatedEntity, true, true, q, ignoreNull,
                        c == 1, fromWhereClause);
                if (!fromWhereClause.getJoinProperties().isEmpty()
                        && !fromWhereClause.getJoinProperties()
                                .containsValue(FromWhereClause.JOIN_TYPE_INNER))
                {
                    fromWhereClause.setFromClause(new StringBuilder(
                            fromWhereClause.getFromClause().toString().replaceAll(
                                    "(?i)(^| |inner )join ", "$1left join ")));
                }
                fromWhereClause.getWhereClause().append(" and ").append(joinHql)
                        .append(outerJoinWhereClause);
                existsQueries.add(fromWhereClause.toString());
            }
            answerTypes.add(answer.getAndOrValue());
            answerSelectedOptionCounts.add(((Long) (answer.getValueChoices()
                    .values().stream()
                    .filter(v -> OrbisPredicates.booleanCompare(true).test(v))
                    .count())).intValue());
            questionIsEquals.add(isEquals);
        }
    }

    private static String buildRelationshipExistsSubqueryHQL(Entity masterEntity,
            Relationship relationship, String mainJoinHql, String mainFromHql,
            String hql, boolean joinMaster, boolean isSubclass)
    {
        final Entity relatedEntity = relationship.getRelatedEntity();
        final String relatedEntityHqlAlias = relatedEntity.getEntityHqlAlias();
        final String relatedEntityHqlId = relatedEntity.getEntityHqlId();
        final boolean relationQuestionAnswered = isRelationQuestionAnswered(
                relationship);
        final CriteriaQuestion relationQuestion = relationQuestionAnswered
                ? relationship.getRelationQuestion()
                : null;
        final boolean ignoreNull = relationship.isAlwaysPresent()
                || (relationQuestionAnswered && relationQuestion != null
                        && relationQuestion.getAnswer().getBooleanValue());
        boolean includeNotExistsQuery = false;

        String oldStaticWhereHql = relatedEntity.getStaticWhereHql();

        if (relatedEntity.getStaticWhereHql() == null)
        {
            relatedEntity.setStaticWhereHql("");
        }

        final FromWhereClause mainFromWhereClause = new FromWhereClause(
                relatedEntity.getEntityHqlAlias());

        try
        {
            StringBuilder staticWhereHqlBuilder = new StringBuilder();
            if (oldStaticWhereHql != null
                    && !StringUtils.isEmpty(oldStaticWhereHql.trim())
                    && !ignoreNull)
            {
                if (joinMaster)
                {
                    String staticWhereFromWhereHQL = buildRelationshipSubqueryStaticWhereNegationHQL(
                            relationship, oldStaticWhereHql);
                    staticWhereHqlBuilder.append(" not exists(( ")
                            .append(staticWhereFromWhereHQL).append(" )) and ")
                            .append(relatedEntityHqlAlias).append(".")
                            .append(relatedEntityHqlId).append(" is not NULL or ");
                    hql = removeWhereClauseLeadingAndOr(hql.trim());
                }
                else
                {
                    staticWhereHqlBuilder.append(" (( ")
                            .append(removeWhereClauseLeadingAndOr(
                                    oldStaticWhereHql.trim()))
                            .append(") or ").append(relatedEntityHqlAlias)
                            .append(".").append(relatedEntityHqlId)
                            .append(" is NULL) ");
                }
            }
            staticWhereHqlBuilder.append(hql);
            relatedEntity.setStaticWhereHql(staticWhereHqlBuilder.toString());
            final String joinProperty = relationship.getForiegnKeyHql().substring(0,
                    relationship.getForiegnKeyHql().lastIndexOf('.'));

            String masterJoinAlias = null;

            // Ignore right joining in the main entity if it is already added to
            // the static from HQL
            if (joinMaster
                    && (StringUtils.isEmpty(relatedEntity.getStaticFromHql())
                            || !Pattern
                                    .compile(String.format("(\\.| )%s ",
                                            masterEntity.getEntityClass()
                                                    .getSimpleName()))
                                    .matcher(relatedEntity.getStaticFromHql())
                                    .find()))
            {
                masterJoinAlias = joinProperty.replace('.', '_');
            }
            mainFromWhereClause.setFromClause(new StringBuilder(mainFromHql));
            if (masterJoinAlias != null)
            {
                if (masterJoinAlias.equals(joinProperty))
                {
                    joinMaster = false;
                    includeNotExistsQuery = !ignoreNull;
                }
                else
                {
                    mainFromWhereClause.setSelectClause(new StringBuilder(
                            String.format("select %s.%s", masterJoinAlias,
                                    masterEntity.getEntityHqlId())));
                    /*
                     * mainFromWhereClause.getFromClause().append(" right join " )
                     * .append(joinProperty).append(" ") .append(masterJoinAlias);
                     */
                }
            }
            if (!joinMaster)
            {
                mainFromWhereClause.setSelectClause(
                        new StringBuilder(String.format("select %s.%s",
                                relatedEntityHqlAlias, relatedEntityHqlId)));
            }
            if (!StringUtils.isEmpty(relatedEntity.getStaticFromHql()))
            {
                mainFromWhereClause
                        .setStaticJoinClause(relatedEntity.getStaticFromHql());
            }
            buildMasterWhereHql(relatedEntity, true, true, null, ignoreNull, false,
                    mainFromWhereClause);
            if (masterJoinAlias != null && !masterJoinAlias.equals(joinProperty))
            {
                mainFromWhereClause.cacheProperty(relationship.getForiegnKeyHql(),
                        FromWhereClause.JOIN_TYPE_RIGHT);
                mainFromWhereClause.buildJoinClause();
            }
        }
        finally
        {
            relatedEntity.setStaticWhereHql(oldStaticWhereHql);
        }

        StringBuilder whereClause = mainFromWhereClause.getWhereClause();
        whereClause.append(" and (").append(mainJoinHql);
        if (isSubclass)
        {
            whereClause.append(") and (").append(relatedEntity.getEntityHqlAlias())
                    .append(".class = '")
                    .append(relatedEntity.getEntityClass().getName())
                    .append("' or ").append(relatedEntity.getEntityHqlAlias())
                    .append(".").append(relatedEntity.getEntityHqlId())
                    .append(" is NULL");
        }
        whereClause.append(")");

        List<String> subqueries = Lists
                .newArrayList(mainFromWhereClause.toString());

        String notExistsClause = null;
        if (includeNotExistsQuery)
        {
            final FromWhereClause notExistsFromWhereClause = new FromWhereClause(
                    relatedEntity.getEntityHqlAlias());
            notExistsFromWhereClause
                    .setSelectClause(mainFromWhereClause.getSelectClause());
            notExistsFromWhereClause
                    .setFromClause(mainFromWhereClause.getFromClause());
            if (!StringUtils.isEmpty(relatedEntity.getStaticFromHql()))
            {
                notExistsFromWhereClause
                        .setStaticJoinClause(relatedEntity.getStaticFromHql());
            }
            if (!StringUtils.isEmpty(relatedEntity.getStaticWhereHql()))
            {
                notExistsFromWhereClause
                        .appendToWhereClause(relatedEntity.getStaticWhereHql());
                fixMasterWhereHQL(false, notExistsFromWhereClause);
            }
            notExistsFromWhereClause.getWhereClause()
                    .append(notExistsFromWhereClause.getWhereClause().length() > 0
                            ? " and "
                            : " where ")
                    .append(mainJoinHql);
            notExistsClause = String.format("not exists( %s )",
                    notExistsFromWhereClause.toString());
        }
        else if (joinMaster && !relationship.getMasterJoinHql()
                .equals(masterEntity.getEntityHqlAlias().concat(".")
                        .concat(masterEntity.getEntityHqlId())))
        {
            notExistsClause = String.format("%s is NULL",
                    relationship.getMasterJoinHql());
        }

        hql = buildRelationshipExistsHQL(masterEntity, relatedEntity, subqueries,
                null, null, null, notExistsClause, false);

        return hql;
    }

    private static String buildRelationshipSubqueryStaticWhereNegationHQL(
            Relationship relationship, String oldStaticWhereHql)
    {
        final Entity relatedEntity = relationship.getRelatedEntity();
        final String joinHql = String.format("%s = %s",
                relationship.getForiegnKeyHql().replaceAll("^([^\\.]+)", "$1_"),
                relationship.getForiegnKeyHql());
        final FromWhereClause staticWhereFromWhereClause = new FromWhereClause(
                relatedEntity.getEntityHqlAlias());
        buildFromHQL(relatedEntity, staticWhereFromWhereClause);
        staticWhereFromWhereClause.setFromClause(
                new StringBuilder(staticWhereFromWhereClause.toString().replaceAll(
                        "(?i)( *from [a-z0-9\\_]+ (as )?[^ ]+)( |$)", "$1_$3")));
        final Set<String> staticWhereHqlJoinAliases = new HashSet();
        Matcher staticWhereHqlJoinAliasMatcher = Pattern
                .compile("(?: join [a-z0-9\\_\\.]+ (?:as )?([^ ]+))")
                .matcher(oldStaticWhereHql);
        while (staticWhereHqlJoinAliasMatcher.find())
        {
            staticWhereHqlJoinAliases.add(staticWhereHqlJoinAliasMatcher.group(1));
        }
        String staticWhereHql = oldStaticWhereHql
                .replaceAll("( join [a-z0-9\\_]+)(\\.)", "$1_$2").replaceAll(
                        "(?i)(([ \\r\\n\\t=][a-z0-9\\_]+)(\\.)|( *from [a-z0-9\\_]+ (?:as )?[^ ]+)|( join [a-z0-9\\_\\.]+ (?:as )?[^ ]+))",
                        "$2$4$5_$3");
        final String escapedHqlSeparators = Pattern
                .quote("");
        for (String staticWhereHqlJoinAlias : staticWhereHqlJoinAliases)
        {
            staticWhereHql = staticWhereHql.replaceAll(
                    String.format("([%s])(%s)([%s\\.])", escapedHqlSeparators,
                            staticWhereHqlJoinAlias, escapedHqlSeparators),
                    "$1$2_$3");
        }
        staticWhereFromWhereClause.getWhereClause().append(staticWhereHql)
                .append(" and ").append(joinHql);
        fixMasterWhereHQL(true, staticWhereFromWhereClause);
        return staticWhereFromWhereClause.toString();
    }

    private static String buildRelationshipExistsHQL(Entity masterEntity,
            Relationship relationship, List<CriteriaQuestion> multiChoiceQuestions,
            List<String> existsQueries, List<String> answerTypes,
            List<Integer> answerSelectedOptionCounts,
            List<Boolean> questionIsEquals, boolean negate)
    {
        final String ret;
        final Entity relatedEntity = relationship.getRelatedEntity();
        final boolean relationQuestionAnswered = isRelationQuestionAnswered(
                relationship);
        final CriteriaQuestion relationQuestion = relationQuestionAnswered
                ? relationship.getRelationQuestion()
                : null;
        final boolean alwaysPresent = relationship.isAlwaysPresent();
        if (alwaysPresent || (relationQuestionAnswered && relationQuestion != null
                && !relationQuestion.getAnswer().getBooleanValue()))
        {
            ret = buildRelationshipExistsHQL(masterEntity, null, existsQueries,
                    null, null, null, null, !alwaysPresent);
        }
        else
        {
            ret = buildRelationshipExistsHQL(masterEntity, relatedEntity,
                    existsQueries, answerTypes, answerSelectedOptionCounts,
                    questionIsEquals, null, negate);
        }
        return ret;
    }

    private static String buildRelationshipExistsHQL(Entity entity,
            Entity relatedEntity, List<String> existsQueries,
            List<String> answerTypes, List<Integer> answerSelectedOptionCounts,
            List<Boolean> questionIsEquals, String notExistsClause, boolean negate)
    {
        String ret;
        boolean includeNotRelated = relatedEntity != null
                && !StringUtils.isEmpty(relatedEntity.getIncludeNotRelatedQuery());

        ret = " and ";
        if (!StringUtils.isEmpty(notExistsClause))
        {
            ret += "( ";
        }
        if (includeNotRelated)
        {
            ret += "(( ";
        }
        if (answerTypes == null || answerTypes.isEmpty())
        {
            ret += (negate ? "not " : "").concat("exists( ")
                    .concat(existsQueries.get(0)).concat(" )");
        }
        else
        {
            int q = 0;
            for (int a = 0; a < answerTypes.size(); a++)
            {
                if (a > 0)
                {
                    ret += " and ";
                }
                String answerType = answerTypes.get(a);
                boolean not = negate;
                boolean exactMatch = CriteriaAnswer.CHOICE_MULTI_OPERATION_EQUALS
                        .equals(answerType)
                        || CriteriaAnswer.CHOICE_MULTI_OPERATION_NOT_EQUALS
                                .equals(answerType);
                boolean equals = questionIsEquals.get(a);
                boolean useExistsQuery = false;
                if (exactMatch && equals)
                {
                    ret += "(";
                }
                switch (answerType)
                {
                    case CriteriaAnswer.CHOICE_OPERATION_NOT_CONTAINS:
                        if (equals)
                        {
                            not = !negate;
                        }
                    case CriteriaAnswer.CHOICE_OPERATION_CONTAINS:
                        useExistsQuery = true;
                        break;
                    case CriteriaAnswer.CHOICE_MULTI_OPERATION_NOT_EQUALS:
                    case CriteriaAnswer.CHOICE_MULTI_OPERATION_NOT_CONTAINS_ALL:
                        if (equals)
                        {
                            not = !negate;
                        }
                    case CriteriaAnswer.CHOICE_MULTI_OPERATION_EQUALS:
                    case CriteriaAnswer.CHOICE_MULTI_OPERATION_CONTAINS_ALL:
                        Integer selectedOptionsCount = answerSelectedOptionCounts
                                .get(a);
                        if (equals)
                        {
                            ret += "( ".concat(existsQueries.get(q)).concat(" ) ");
                            ret += (not ? "< " : "= ")
                                    .concat(selectedOptionsCount.toString());
                            if (exactMatch)
                            {
                                ret += (not ? " or " : " and not ")
                                        .concat("exists( ")
                                        .concat(existsQueries.get(++q))
                                        .concat(" ))");
                            }
                        }
                        else
                        {
                            useExistsQuery = true;
                        }
                        break;
                    case CriteriaAnswer.CHOICE_OPERATION_NULL:
                        if (equals)
                        {
                            ret += "( not exists( ".concat(existsQueries.get(q))
                                    .concat(" ) or exists( ")
                                    .concat(existsQueries.get(++q)).concat(" ) )");
                            break;
                        }
                    case CriteriaAnswer.CHOICE_OPERATION_NOT_NULL:
                        if (equals)
                        {
                            ret += "exists( ".concat(existsQueries.get(q))
                                    .concat(" )");
                        }
                        else
                        {
                            useExistsQuery = true;
                        }
                        break;
                }
                if (useExistsQuery)
                {
                    ret += (not ? "not " : "").concat("exists( ")
                            .concat(existsQueries.get(q)).concat(" )");
                }
                q++;
            }
        }
        if (includeNotRelated && relatedEntity != null)
        {
            ret += " ) or not exists("
                    .concat(relatedEntity.getIncludeNotRelatedQuery())
                    .concat(") )");
        }
        if (!StringUtils.isEmpty(notExistsClause))
        {
            ret += " or ".concat(notExistsClause).concat(" )");
        }

        return ret;
    }

    public static String getRelationshipOuterJoinWhereClause(
            FromWhereClause fromWhereClause, String staticWhereClause,
            String entityAlias, boolean appendToAlias)
    {
        String ret = "";
        if (fromWhereClause.getOuterJoinAliasJoinClauses() != null)
        {
            String[] whereClauses = Pattern
                    .compile(" +(and|or) +", Pattern.CASE_INSENSITIVE)
                    .split(staticWhereClause.trim().replaceAll("[\\(\\)]", ""));
            String joinPatternString = "%s\\.[a-zA-Z0-9\\_\\.]+ *= *%s\\.[a-zA-Z0-9\\_\\.]+";
            List<String> outerJoinAliases = Lists.newArrayList(
                    fromWhereClause.getOuterJoinAliasJoinClauses().keySet());
            int oa = 0;
            for (String alias : outerJoinAliases)
            {
                if (alias != null)
                {
                    List<String> joinWhereClauses = new ArrayList<>();
                    for (int a = 0; a < oa; a++)
                    {
                        String mainAlias = a == 0 ? entityAlias
                                : outerJoinAliases.get(a);
                        Pattern joinPattern = Pattern.compile("("
                                + String.format(joinPatternString, alias, mainAlias)
                                + "|"
                                + String.format(joinPatternString, mainAlias, alias)
                                + ")");
                        for (String whereClause : whereClauses)
                        {
                            if (joinPattern.matcher(whereClause).matches())
                            {
                                joinWhereClauses.add(whereClause);
                            }
                        }
                    }
                    if (!joinWhereClauses.isEmpty())
                    {
                        ret += String.format(" and ( %s ) ",
                                StringUtils.join(joinWhereClauses, " and ")
                                        .replaceAll("(?i)(^| |=)([a-z0-9\\_]+)\\.",
                                                "$1$2_."));
                    }
                }
                oa++;
            }
        }
        return ret;
    }

    public static String buildHQL(Entity entity, boolean forMaster)
    {
        // Make sure the entity's "searchModel" transient-property is set...
        if (null == entity.getSearchModel())
        {
            throw new RuntimeException("SEARCH MODEL NOT SET!");
        }

        FromWhereClause fromWhereClause = new FromWhereClause(
                entity.getEntityHqlAlias());

        buildFromHQL(entity, fromWhereClause);
        buildMasterWhereHql(entity, forMaster, false, null, false, false,
                fromWhereClause);

        return fromWhereClause.toString();
    }

    private static void buildFromHQL(Entity entity, FromWhereClause fromWhereClause)
    {
        StringBuilder fromClause = new StringBuilder(" from ")
                .append(entity.getEntityHqlName()).append(" as ")
                .append(entity.getEntityHqlAlias());

        if (!StringUtils.isEmpty(entity.getStaticFromHql()))
        {
            fromWhereClause.setStaticJoinClause(entity.getStaticFromHql());
        }

        if (entity.isMaster() && entity.getRelationships() != null)
        {
            for (Relationship r : entity.getRelationships())
            {
                r.getRelatedEntity().setSearchModel(entity.getSearchModel());

                if (r.getRelatedEntity().isCriteriaAnswered())
                {
                    switch (r.getType())
                    {
                        case JOINED:
                            fromClause.append(r.getJoinClause());
                            break;

                        case CUSTOM:
                            fromClause.append(getCustomFromClause(entity, r));
                            break;

                        case COUNT:
                            break;
                        case DETAIL:
                            break;
                        case MANY_TO_ONE:
                            break;
                        case ONE_TO_ONE:
                            break;
                        case SAUSAGE:
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        fromWhereClause.setFromClause(fromClause);
    }

    private static String getCustomFromClause(Entity entity, Relationship r)
    {
        SearchCustomRelationshipInterface scri = getCustomSearchInterface(entity,
                r);

        String fromHql = scri != null
                ? Optional.ofNullable(scri).map(s -> s.getMasterFromHQL(entity, r))
                        .orElse("")
                : "";

        return fromHql;
    }

    private static LinkedList<JQGridColumn> getJQGridColumns(Entity entity,
            Locale locale)
    {
        LinkedList<JQGridColumn> gridColumns = new LinkedList<>();
        MessageSource msgSrc = PortalUtils.getMessageSource();
        // Append Entity's columns...
        for (CriteriaQuestion q : entity.getCriteriaModel()
                .getColSortedCriteriaQuestions())
        {
            final String questionKey = q.getQuestionKey();

            if (questionKey != null && (q.isVisibleResults()
                    || (!q.isVisibleCriteria() && !q.isVisibleResults())))
            {
                String questionText = StringUtils
                        .defaultString(q.getQuestionText());
                if (questionText.startsWith("i18n"))
                {
                    questionText = msgSrc.getMessage(questionText, null, locale);
                }

                JQGridColumn col = new JQGridColumn();
                col.setLabel(questionText);
                col.setIndex(questionKey);
                col.setSortable(q.isSortable());
                // used to automatically set the grid width
                // based on the header text if not explicitly set
                int cw = ((questionText.length() * 8) + 20);
                if (q.getColWidth() == 0)
                {
                    int columnWidth = (cw > 50) ? cw : 50;
                    col.setWidth(columnWidth);
                }
                else
                {
                    col.setWidth(q.getColWidth());
                }

                col.setAlignment(q.getColAlignment());
                col.setHidden(!q.isVisibleResults());

                gridColumns.add(col);
            }
        }

        if (entity.isMaster())
        {
            if (entity.getRelationships() != null)
            {
                for (Relationship r : entity.getRelationships())
                {
                    // Append the additional columns of any found
                    // "sausage type entities"...
                    if (r.getType().equals(Relationship.TYPE.SAUSAGE))
                    {
                        LinkedList<JQGridColumn> sausageColumns = new LinkedList<>();

                        for (CriteriaQuestion q : r.getRelatedEntity()
                                .getCriteriaModel().getColSortedCriteriaQuestions())
                        {
                            if (q.isVisibleResults())
                            {
                                JQGridColumn col = new JQGridColumn();
                                col.setLabel(q.getQuestionText());
                                col.setIndex(q.getQuestionKey());
                                col.setSortable(q.isSortable());
                                col.setWidth(q.getColWidth());
                                col.setHidden(false);
                                col.setVirtual(true);

                                sausageColumns.add(col);
                            }
                        }

                        if (r.getSausageColIndex() == -1)
                        {
                            gridColumns.addAll(sausageColumns);
                        }
                        else
                        {
                            if (gridColumns.size() < r.getSausageColIndex())
                            {
                                gridColumns.addAll(sausageColumns);
                            }
                            else
                            {
                                gridColumns.addAll(r.getSausageColIndex(),
                                        sausageColumns);
                            }
                        }
                    }
                }
            }
        }

        return gridColumns.stream().distinct()
                .collect(Collectors.toCollection(LinkedList::new));
    }

    public static void buildMasterWhereHql(Entity entity, boolean forMaster,
            boolean forSubquery, CriteriaQuestion multiChoiceQuestion,
            boolean ignoreNull, boolean negate, FromWhereClause fromWhereClause)
    {
        fromWhereClause.clearWhereClause();
        // construct 'where' clause...
        buildWhereHQL(entity, forMaster, forSubquery, multiChoiceQuestion,
                ignoreNull, negate, fromWhereClause);

        // append 'where' clauses for Related Entities...
        buildWhereRelatedHQL(entity, fromWhereClause);

        // "massage" the where clause and construct the join clause...
        fixMasterWhereHQL(forSubquery && multiChoiceQuestion != null,
                fromWhereClause);
    }

    /**
     * If where HQL starts with 'and' or 'and', change it to 'where'. Additionally,
     * construct the join clause from the where HQL.
     *
     * @param leftJoin
     *            Use a left join when building the join clause
     * @param fromWhereClause
     */
    public static void fixMasterWhereHQL(boolean subquery,
            FromWhereClause fromWhereClause)
    {
        if (fromWhereClause != null
                && fromWhereClause.getWhereClause().length() > 0)
        {
            String whereClause = fromWhereClause.getWhereClause().toString().trim();

            whereClause = removeWhereClauseLeadingAndOr(whereClause);

            fromWhereClause.setWhereClause(
                    new StringBuilder(" where (").append(whereClause).append(")"));

            Map<String, String> outerJoinAliasJoinClauses = fromWhereClause
                    .getOuterJoinAliasJoinClauses();
            if (outerJoinAliasJoinClauses != null)
            {
                String masterReplacementAlias = null;
                Pattern hasMainAliasPattern = Pattern.compile(
                        " (" + fromWhereClause.getMainAlias() + ")(?: |$)");
                Matcher hasMainAliasMatcher = null;
                for (Map.Entry<String, String> jce : outerJoinAliasJoinClauses
                        .entrySet())
                {
                    String outerJoinAlias = jce.getKey();
                    if (outerJoinAlias != null)
                    {
                        String joinClause = jce.getValue();
                        hasMainAliasMatcher = hasMainAliasPattern
                                .matcher(joinClause);
                        if (hasMainAliasMatcher.find())
                        {
                            masterReplacementAlias = outerJoinAlias;
                            break;
                        }
                    }
                }
                if (masterReplacementAlias != null && hasMainAliasMatcher != null)
                {
                    String newFromClauseFragment = fromWhereClause
                            .massageOuterJoinClause(subquery,
                                    outerJoinAliasJoinClauses
                                            .get(masterReplacementAlias))
                            .toString();
                    newFromClauseFragment = newFromClauseFragment
                            .substring(newFromClauseFragment.indexOf(',') + 1);
                    fromWhereClause.setFromClause(new StringBuilder(" from ")
                            .append(newFromClauseFragment.trim()));
                    outerJoinAliasJoinClauses.remove(masterReplacementAlias);
                }
            }

            fromWhereClause.buildJoinClause();
        }
    }

    public static String removeWhereClauseLeadingAndOr(String whereClause)
    {
        StringBuilder whereClauseBuilder = new StringBuilder(whereClause);

        if (whereClauseBuilder.toString().toUpperCase().startsWith("AND "))
        {
            whereClauseBuilder = new StringBuilder(
                    whereClauseBuilder.toString().substring(4));
        }
        else if (whereClause.toString().toUpperCase().startsWith("OR "))
        {
            whereClauseBuilder = new StringBuilder(
                    whereClauseBuilder.toString().substring(3));
        }

        return whereClauseBuilder.toString();
    }

    public static String buildWhereHQL(Entity entity, boolean forMaster)
    {
        FromWhereClause fromWhereClause = new FromWhereClause(
                entity.getEntityHqlAlias());
        buildWhereHQL(entity, forMaster, false, null, false, false,
                fromWhereClause);
        return fromWhereClause.toString();
    }

    public static void buildWhereHQL(Entity entity, boolean forMaster,
            FromWhereClause fromWhereClause)
    {
        buildWhereHQL(entity, forMaster, false, null, false, false,
                fromWhereClause);
    }

    public static void buildWhereHQL(Entity entity, boolean forMaster,
            boolean forSubquery, CriteriaQuestion multiChoiceQuestion,
            boolean ignoreNull, boolean negate, FromWhereClause fromWhereClause)
    {
        StringBuilder whereClause = fromWhereClause.getWhereClause();

        if (!forSubquery || multiChoiceQuestion == null)
        {
            if (!StringUtils.isEmpty(entity.getStaticWhereHql()))
            {
                whereClause.append(entity.getStaticWhereHql());
            }
            if (!forMaster
                    && !StringUtils.isEmpty(entity.getDetailOnlyStaticWhereHql()))
            {
                whereClause.append(entity.getDetailOnlyStaticWhereHql());
            }
        }

        // Whether the entity is the master entity (different from forMaster)
        boolean master = entity.isMaster();

        if (forMaster || master)
        {
            StringBuilder questionsWhereClause = new StringBuilder();

            for (CriteriaGroup cg : entity.getCriteriaModel().getCriteriaGroups())
            {
                if ((!forSubquery || multiChoiceQuestion == null)
                        && null != cg.getCriteriaPluginClass())
                {
                    CriteriaPlugin criteriaPlugin = entity.getSearchModel()
                            .getCriteriaPluginsMap().get(cg.getCriteriaPluginKey());

                    if (criteriaPlugin.isAnswered())
                    {
                        whereClause.append(criteriaPlugin.buildWhereHql());
                    }
                }

                List<CriteriaQuestion> questions = cg.getQuestions();

                for (CriteriaQuestion q : questions)
                {
                    boolean multiChoice = CriteriaQuestion.TYPE_CHOICE_MULTI
                            .equals(q.getType());
                    boolean choice = multiChoice
                            || CriteriaQuestion.TYPE_CHOICE_SINGLE
                                    .equals(q.getType());
                    boolean multiChoiceNull = choice && q.isChoiceNull();
                    boolean leftJoin = (forSubquery && multiChoiceQuestion == null)
                            || (!ignoreNull && negate != multiChoiceNull)
                            || (master && !choice && q.isAnsweredNot());
                    String questionWhereClause = null;
                    if (!forSubquery || (multiChoiceQuestion == null
                            && (!choice || !q.isMultiChoiceAnswered())))
                    {
                        boolean negateQuestion = (multiChoice && q.isAnsweredNot())
                                || (choice && q.isAnsweredNot() != negate)
                                || (!choice && negate);
                        questionWhereClause = buildCriteriaQuestionWhereHQL(entity,
                                q, ignoreNull, negateQuestion);
                    }
                    else if (q.equals(multiChoiceQuestion))
                    {
                        questionWhereClause = buildWhereHQL_Choice(q, entity,
                                ignoreNull, negate);
                        if (!ignoreNull && !negate
                                && q.isAnsweredNot() == multiChoiceNull
                                && (multiChoiceNull
                                        || !q.getAnswer().getValueChoices().values()
                                                .stream().anyMatch(OrbisPredicates
                                                        .booleanCompare(true))))
                        {
                            if (!multiChoiceNull)
                            {
                                fromWhereClause.cacheProperty(
                                        entity.getRelationship(null)
                                                .getForiegnKeyHql(),
                                        FromWhereClause.JOIN_TYPE_RIGHT);
                            }
                            leftJoin = true;
                        }
                    }
                    if (q.getQuestionKey() != null
                            && q.getQuestionKey().contains("cu")
                            && (q.getQuestionKey().contains(".company.")
                                    || q.getQuestionKey().contains(".user.")))
                    {
                        leftJoin = false;
                    }
                    if (!StringUtils.isEmpty(questionWhereClause))
                    {
                        questionsWhereClause.append(questionWhereClause);
                        String questionKey = q.getQuestionKey();
                        if (forSubquery && multiChoiceQuestion != null)
                        {
                            questionKey = questionKey
                                    .substring(0, questionKey.indexOf('.'))
                                    .concat("_").concat(questionKey
                                            .substring(questionKey.indexOf('.')));
                        }
                        fromWhereClause.cacheProperty(questionKey,
                                leftJoin ? FromWhereClause.JOIN_TYPE_LEFT
                                        : FromWhereClause.JOIN_TYPE_INNER);
                    }
                }
            }

            if (questionsWhereClause.length() > 0)
            {
                String whereClauseFragment = forSubquery
                        && multiChoiceQuestion != null
                                ? questionsWhereClause.toString().replaceAll(
                                        "(\\(| )".concat(entity.getEntityHqlAlias()
                                                .concat("\\.")),
                                        "$1".concat(entity.getEntityHqlAlias()
                                                .concat("_.")))
                                : questionsWhereClause.toString();
                whereClause.append(whereClauseFragment);
            }
        }

        fromWhereClause.setWhereClause(whereClause);
    }

    private static String buildCriteriaQuestionWhereHQL(Entity entity,
            CriteriaQuestion q, boolean ignoreNull, boolean negate)
    {
        StringBuilder ret = new StringBuilder();
        if (q.getCustomHQLBuilder() == null
                && !StringUtils.isEmpty(q.getCustomHql()) && q.getAnswer() != null)
        {
            ret.append(q.getCustomHql());
        }
        else if (q.getCustomHQLBuilder() != null && q.getAnswer() != null)
        {
            String hql = q.getCustomHQLBuilder().getHQL(q, entity);
            if (!StringUtils.isEmpty(hql) && StringUtils.isEmpty(q.getCustomHql()))
            {
                q.setCustomHql(hql);
            }
            ret.append(hql);
        }
        else
        {
            ret.append(buildWhereClause(q, entity, ignoreNull, negate));
        }
        return ret.toString();
    }

    public static String buildDescription(Entity entity, Locale locale)
    {
        StringBuilder description = new StringBuilder();

        for (CriteriaGroup cg : entity.getCriteriaModel().getCriteriaGroups())
        {
            StringBuilder temp = new StringBuilder("");
            if (null != cg.getCriteriaPluginClass())
            {
                CriteriaPlugin criteriaPlugin = entity.getSearchModel()
                        .getCriteriaPluginsMap().get(cg.getCriteriaPluginKey());

                if (criteriaPlugin.isAnswered())
                {
                    temp.append(criteriaPlugin.buildDescription(locale));
                }
            }

            for (CriteriaQuestion q : cg.getQuestions())
            {
                temp.append(buildDescription(q, entity, locale));
            }
            if (temp.length() > 0)
            {
                String label = "";
                if (cg.getGroupLabel() != null
                        && cg.getGroupLabel().startsWith("i18n"))
                {
                    label = PortalUtils.getMessageSource()
                            .getMessage(cg.getGroupLabel(), null, locale);
                }
                else
                {
                    label = cg.getGroupLabel();
                }
                description.append("<li>").append(label).append("<ul>").append(temp)
                        .append("</ul></li>");
            }
        }

        return description.toString();
    }

    public static String buildDescription(CriteriaQuestion q, Entity entity,
            Locale locale)
    {
        String description = "";

        if (CriteriaQuestion.TYPE_TEXT.equals(q.getType()))
        {
            description = buildDescription_Text(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_DATE.equals(q.getType()))
        {
            description = buildDescription_Date(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_NUMBER.equals(q.getType())
                || CriteriaQuestion.TYPE_FLOAT.equals(q.getType()))
        {
            description = buildDescription_Number(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_RATING_RANGE.equals(q.getType()))
        {
            description = buildDescription_Rating_Range(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_IS_EMPTY.equals(q.getType()))
        {
            description = buildDescription_IsEmpty(q, locale);
        }
        else if (CriteriaQuestion.TYPE_BOOLEAN.equals(q.getType()))
        {
            description = buildDescription_Boolean(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_CHOICE_MULTI.equals(q.getType())
                || CriteriaQuestion.TYPE_CHOICE_SINGLE.equals(q.getType()))
        {
            description = buildDescription_Choice(q, entity, locale);
        }
        else if (CriteriaQuestion.TYPE_FILE_UPLOAD.equals(q.getType()))
        {
            description = buildDescription_File(q, locale);
        }
        else if (CriteriaQuestion.TYPE_TREE.equals(q.getType()))
        {
            description = buildDescription_Tree(q, locale);
        }
        else if (CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType())
                || CriteriaQuestion.TYPE_MATRIX_SINGLE.equals(q.getType()))
        {
            description = (String) buildDescription_Matrix(q, locale);
        }

        if (!StringUtils.isEmpty(description))
        {
            description = "<li>" + description + "</li>";
        }

        return (description != null ? description.toString() : "");
    }

    private static Object buildDescription_Matrix(CriteriaQuestion q, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();

        String hql = "";

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered() && q.getMatrixOperation() != null
                && q.getAnswer().getMatrixHQLClauses() != null
                && !q.getAnswer().getMatrixHQLClauses().isEmpty())
        {
            if (CriteriaQuestion.MATRIX_OPERATION_FUZZY
                    .equals(q.getMatrixOperation()))
            {
                for (String clause : q.getAnswer().getMatrixHQLClauses())
                {
                    hql += label + " "
                            + messageSource.getMessage(
                                    "i18n.search_criteriaQuestionText.contains",
                                    null, locale)
                            + " '" + clause.replaceAll("'", "''") + "' or ";
                }

                hql = hql.substring(0, hql.lastIndexOf(" or"));
            }
            else if (CriteriaQuestion.MATRIX_OPERATION_EXACT
                    .equals(q.getMatrixOperation()))
            {
                hql += label + " like '" + q.getAnswer().getMatrixHQLClauses()
                        .get(0).replaceAll("'", "''") + "'";
            }

            hql += ")";
        }

        return hql;
    }

    private static String buildDescription_File(CriteriaQuestion q, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String hql = "";

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered())
        {
            hql = " " + label + " "
                    + (q.getAnswer().getBooleanValue() ? messageSource.getMessage(
                            "i18n.filterDescription.hasFileUpload", null, locale)
                            : messageSource.getMessage(
                                    "i18n.filterDescription.doesNotHaveFileUpload",
                                    null, locale));
        }
        return hql;
    }

    private static String buildDescription_IsEmpty(CriteriaQuestion q,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String hql = "";

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered())
        {
            boolean emptyOnly = q.getAnswer().getBooleanValue();

            if (emptyOnly)
            {
                hql = label + " "
                        + messageSource.getMessage(
                                "i18n.search_criteriaQuestionText.isNotEmpty", null,
                                locale);
            }
            else
            {
                hql = label + " " + messageSource.getMessage(
                        "i18n.search_criteriaQuestionText.isEmpty", null, locale);
            }
        }
        return hql;
    }

    private static String buildDescription_Boolean(CriteriaQuestion q,
            Entity entity, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String hql = "";

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered())
        {
            hql = label + " " + (q.getAnswer().getBooleanValue()
                    ? messageSource.getMessage("i18n.filterDescription.isTrue",
                            null, locale)
                    : messageSource.getMessage("i18n.filterDescription.isFalse",
                            null, locale));
        }
        return hql;
    }

    private static String buildDescription_Text(CriteriaQuestion q, Entity entity,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String hql = "";

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.getOperation().equals(CriteriaQuestion.OPERATION_EMPTY))
        {
            hql = label + " " + messageSource.getMessage(
                    "i18n.search_criteriaQuestionText.isEmpty", null, locale);
        }
        else if (q.getOperation().equals(CriteriaQuestion.OPERATION_NOT_EMPTY))
        {
            hql = label + " " + messageSource.getMessage(
                    "i18n.search_criteriaQuestionText.isNotEmpty", null, locale);
        }
        else if (q.isAnswered())
        {
            if (CriteriaQuestion.OPERATION_EQUALS.equals(q.getOperation()))
            {
                hql = label + " " + messageSource.getMessage(
                        "i18n.search_criteriaQuestionText.equal", null, locale)
                        + " '" + q.getAnswer().getValue().trim() + "'";
            }
            else if (CriteriaQuestion.OPERATION_NOT_EQUALS.equals(q.getOperation()))
            {
                hql = label + " "
                        + messageSource.getMessage(
                                "i18n.search_criteriaQuestionText.doesntEqual",
                                null, locale)
                        + " '" + q.getAnswer().getValue().trim() + "'";
            }
            else if (CriteriaQuestion.OPERATION_BEGINS_WITH
                    .equals(q.getOperation()))
            {
                hql = label + " " + messageSource.getMessage(
                        "i18n.search_criteriaQuestionText.beginsWith", null, locale)
                        + " '" + q.getAnswer().getValue().trim() + "'";
            }
            else if (CriteriaQuestion.OPERATION_ENDS_WITH.equals(q.getOperation()))
            {
                hql = label + " " + messageSource.getMessage(
                        "i18n.search_criteriaQuestionText.endsWith", null, locale)
                        + " '" + q.getAnswer().getValue().trim() + "'";
            }
            else if (CriteriaQuestion.OPERATION_NOT_CONTAINS
                    .equals(q.getOperation()))
            {
                hql = label + " "
                        + messageSource.getMessage(
                                "i18n.search_criteriaQuestionText.doesntContain",
                                null, locale)
                        + " '" + q.getAnswer().getValue() + "'";
            }
            else
            {
                hql = label + " "
                        + messageSource.getMessage(
                                "i18n.search_criteriaQuestionText.contains", null,
                                locale)
                        + " '" + q.getAnswer().getValue() + "'";
            }
        }
        return hql;
    }

    private static String buildDescription_Number(CriteriaQuestion q, Entity entity,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        StringBuilder hql = new StringBuilder();

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered())
        {
            CriteriaAnswer a = q.getAnswer();

            if (!StringUtils.isEmpty(a.getFromValue())
                    && !StringUtils.isEmpty(a.getToValue()))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isGreaterEqual", null,
                                locale))
                        .append(" ").append(a.getFromValue().trim());
                hql.append(" ")
                        .append(messageSource.getMessage("i18n.common.andLOWERCASE",
                                null, locale))
                        .append(" ").append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isLessEqual", null, locale))
                        .append(" ").append(a.getToValue().trim());
            }
            else if (!StringUtils.isEmpty(a.getFromValue()))
            {
                // provide support for saved searches which will not have
                // the operation field set
                q.setNumberOperation(
                        q.getNumberOperation() != null ? q.getNumberOperation()
                                : CriteriaQuestion.NUMBER_OPERATION_BETWEEN);

                if (CriteriaQuestion.NUMBER_OPERATION_EQUALS
                        .equals(q.getNumberOperation()))
                {
                    hql.append(label).append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isEqual", null, locale))
                            .append(" ")
                            .append(q.getAnswer().getFromValue().trim());
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_LESS_THAN
                        .equals(q.getNumberOperation()))
                {
                    hql.append(label).append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isLess", null, locale))
                            .append(" ")
                            .append(q.getAnswer().getFromValue().trim());
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_LESS_THAN_EQUALS
                        .equals(q.getNumberOperation()))
                {
                    hql.append(label).append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isLessEqual", null,
                                    locale))
                            .append(" ")
                            .append(q.getAnswer().getFromValue().trim());
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_GREATER_THAN
                        .equals(q.getNumberOperation()))
                {
                    hql.append(label).append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isGreater", null,
                                    locale))
                            .append(" ")
                            .append(q.getAnswer().getFromValue().trim());
                }
                else
                {
                    hql.append(label).append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isGreaterEqual", null,
                                    locale))
                            .append(" ")
                            .append(q.getAnswer().getFromValue().trim());
                }
            }
            else if (!StringUtils.isEmpty(a.getToValue()))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isLessEqual", null, locale))
                        .append(" ").append(q.getAnswer().getToValue().trim());
            }
        }

        return hql.toString();
    }

    private static String buildDescription_Rating_Range(CriteriaQuestion q,
            Entity entity, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        StringBuilder hql = new StringBuilder();

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (!q.isAnswered())
        {
            if (q.getRatingNAChoice()
                    .equals(CriteriaQuestion.RATING_NA_CHOICE_INCLUDE_NA))
            {
            }
            else if (q.getRatingNAChoice()
                    .equals(CriteriaQuestion.RATING_NA_CHOICE_EXCLUDE_NA))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isNotEqualNA", null,
                                locale))
                        .append(" ");
            }
        }
        else
        {
            CriteriaAnswer a = q.getAnswer();

            if (!StringUtils.isEmpty(a.getRatingFromValue())
                    && !StringUtils.isEmpty(a.getRatingToValue()))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isGreaterEqual", null,
                                locale))
                        .append(" ").append(a.getRatingFromValue());
                hql.append(" ").append(messageSource
                        .getMessage("i18n.common.andLOWERCASE", null, locale))
                        .append(" ");
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isLessEqual", null, locale))
                        .append(" ").append(a.getRatingToValue());
                appendNADescription(hql, q, locale);
            }
            else if (!StringUtils.isEmpty(a.getRatingFromValue()))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isGreaterEqual", null,
                                locale))
                        .append(" ").append(a.getRatingFromValue().trim());
                appendNADescription(hql, q, locale);
            }
            else if (!StringUtils.isEmpty(a.getRatingToValue()))
            {
                hql.append(label).append(" ")
                        .append(messageSource.getMessage(
                                "i18n.filterDescription.isLessEqual", null, locale))
                        .append(" ").append(a.getRatingToValue().trim());
                appendNADescription(hql, q, locale);
            }
        }

        return hql.toString();
    }

    private static void appendNADescription(StringBuilder hql, CriteriaQuestion q,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.getRatingNAChoice()
                .equals(CriteriaQuestion.RATING_NA_CHOICE_INCLUDE_NA))
        {
            hql.append(" ").append(messageSource
                    .getMessage("i18n.common.orLOWERCASE", null, locale))
                    .append(" ");
            hql.append(label).append(" ").append(messageSource
                    .getMessage("i18n.filterDescription.isEqualNA", null, locale));
        }
        else if (q.getRatingNAChoice()
                .equals(CriteriaQuestion.RATING_NA_CHOICE_EXCLUDE_NA))
        {
            hql.append(" ").append(messageSource
                    .getMessage("i18n.common.andLOWERCASE", null, locale))
                    .append(" ");
            hql.append(label).append(" ").append(messageSource.getMessage(
                    "i18n.filterDescription.isNotEqualNA", null, locale));
        }
    }

    private static String buildDescription_Date(CriteriaQuestion q, Entity entity,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String ret = null;
        StringBuilder hql = new StringBuilder("");

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        if (q.isAnswered())
        {
            String customDescription = "";
            if (q.getCustomHQLBuilder() != null
                    && StringUtils.isNotEmpty(customDescription = q
                            .getCustomHQLBuilder().getDescription(q, entity)))
            {
                ret = customDescription;
            }
            else
            {
                Date fromDate = DateUtils.parseDate(q.getAnswer().getFromValue(),
                        q.getDateFormatForQuery(), null);

                Date toDate = DateUtils.parseDate(q.getAnswer().getToValue(),
                        q.getDateFormatForQuery(), null);

                Date startDate = !q.isIncludeTime()
                        ? DateUtils.getStartDate(fromDate)
                        : fromDate;
                Date endDate = !q.isIncludeTime() ? DateUtils.getEndDate(toDate)
                        : toDate;
                if (fromDate != null && toDate != null
                        && DateUtils.isBeforeOrSame(fromDate, toDate))
                {
                    hql.append(label);
                    hql.append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.search_criteriaQuestionNumber.between",
                                    null, locale))
                            .append(" '").append(DateUtils.formatDate(startDate,
                                    DBUtils.DB_DATE_TIME_FORMAT, null));
                    hql.append("' ")
                            .append(messageSource.getMessage(
                                    "i18n.common.andLOWERCASE", null, locale))
                            .append(" '").append(DateUtils.formatDate(endDate,
                                    DBUtils.DB_DATE_TIME_FORMAT, null))
                            .append("'");
                }
                else if (fromDate != null)
                {
                    hql.append(label);
                    hql.append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isOnOrAfter", null,
                                    locale))
                            .append(" '").append(DateUtils.formatDate(startDate,
                                    DBUtils.DB_DATE_TIME_FORMAT, null));
                    hql.append("'");
                }
                else if (toDate != null)
                {
                    hql.append(label);
                    hql.append(" ")
                            .append(messageSource.getMessage(
                                    "i18n.filterDescription.isBeforeOrOn", null,
                                    locale))
                            .append(" '").append(DateUtils.formatDate(endDate,
                                    DBUtils.DB_DATE_TIME_FORMAT, null));
                    hql.append("'");
                }
                ret = hql.toString();
            }
        }

        return ret;
    }

    private static String buildDescription_Choice(CriteriaQuestion q, Entity e,
            Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();

        String ret = null;
        StringBuilder hql = new StringBuilder("");

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        CriteriaAnswer a = q.getAnswer();
        if (q.isAnswered() && a.getValueChoices() != null
                && !a.getValueChoices().isEmpty())
        {
            if (!StringUtils.isEmpty(a.getAndOrValue()))
            {
                ret = Optional.of(q).map(CriteriaQuestion::getCustomHQLBuilder)
                        .map(b -> b.getDescription(q, e)).orElse(null);

                if (StringUtils.isEmpty(ret))
                {
                    Map<String, Boolean> valueChoices = a.getValueChoices();
                    boolean isNot = a.isNotOperation();
                    boolean isOr = a.isOrOperation();
                    boolean isEquals = q.isMultiChoiceEquals();
                    boolean isNullability = q.isChoiceNull();
                    isOr = !isEquals && !isNullability && isOr != isNot;
                    String operatorLabel = messageSource.getMessage(
                            "i18n.search_criteriaQuestionText.".concat(isNullability
                                    ? isNot ? "isEmpty" : "isNotEmpty"
                                    : CriteriaQuestion.OPERATION_CONTAINS
                                            .equals(q.getOperation())
                                                    ? !isNot ? "contains"
                                                            : "doesntContain"
                                                    : !isNot ? "equal"
                                                            : "doesntEqual")
                                    .concat(isEquals ? "Only" : ""),
                            null, locale);
                    if (!isNullability)
                    {
                        String andOr = " "
                                .concat(messageSource.getMessage(
                                        "i18n.search_criteriaQuestionText.".concat(
                                                isOr ? "or" : "and"),
                                        null, locale))
                                .concat(" ")
                                .concat(isEquals ? "" : operatorLabel.concat(" "));
                        for (String choice : valueChoices.keySet())
                        {
                            if (valueChoices.get(choice))
                            {
                                String choiceName;
                                if (q.isOptionIncludeOtherFlag()
                                        && (q.getParamName() + "other")
                                                .equals(choice))
                                {
                                    choice = a.getValueOther();
                                    if (StringUtils.isEmpty(a.getValueOther()))
                                    {
                                        continue;
                                    }
                                    choiceName = choice;
                                }
                                else
                                {
                                    choiceName = q.getOptionChoices().get(choice);
                                    if (choiceName == null
                                            && choice.contains("&amp;"))
                                    {
                                        choiceName = q.getOptionChoices()
                                                .get(choice.replace("&amp;", "&"));
                                    }
                                    if (choiceName != null
                                            && choiceName.indexOf('|') > -1)
                                    {
                                        choiceName = Functions.getLocalizedString(
                                                choiceName, locale.getLanguage());
                                    }
                                }
                                String delimiter = q.getAnswerDelimiter() == null
                                        || !CriteriaQuestion.OPERATION_CONTAINS
                                                .equals(q.getOperation()) ? ""
                                                        : q.getAnswerDelimiter();
                                if (hql.length() > 0)
                                {
                                    hql.append(andOr).append(" ");
                                }
                                hql.append("'").append(delimiter)
                                        .append(choiceName != null
                                                ? choiceName.trim()
                                                : null)
                                        .append(delimiter).append("'");
                            }
                        }
                    }

                    hql = new StringBuilder(label).append(" ").append(operatorLabel)
                            .append(" ").append(hql);

                    ret = hql.toString();
                }
            }
        }

        return ret;
    }

    private static String buildDescription_Tree(CriteriaQuestion q, Locale locale)

    {
        MessageSource messageSource = PortalUtils.getMessageSource();

        StringBuilder hql = new StringBuilder("");

        String label = "";
        if (q.getQuestionText() != null && q.getQuestionText().startsWith("i18n"))
        {
            label = messageSource.getMessage(q.getQuestionText(), null, locale);
        }
        else
        {
            label = q.getQuestionText();
        }

        CriteriaAnswer a = q.getAnswer();
        if (q.isAnswered() && !StringUtils.isEmpty(a.getValue())
                && !StringUtils.isEmpty(a.getAndOrValue()))
        {
            if (q.getQuestionHandlerClass() != null && CriteriaQuestionHandler.class
                    .isAssignableFrom(q.getQuestionHandlerClass()))
            {
                try
                {
                    CriteriaQuestionHandler h = q.getQuestionHandlerClass()
                            .newInstance();

                    String s = h.getWhereHQL(q);
                    if (!StringUtils.isEmpty(s))
                    {
                        hql.append(s);
                    }
                }
                catch (Exception e)
                {
                }
            }
            else
            {
                try
                {
                    JSONArray answer = new JSONArray(a.getValue());
                    for (int i = 0; i < answer.length(); i++)
                    {
                        hql.append(a.getAndOrValue()).append(" ").append(label)
                                .append(" ")
                                .append(messageSource.getMessage(
                                        "i18n.search_criteriaQuestionText.contains",
                                        null, locale))
                                .append(" '\"").append(answer.get(i)).append("\"'");
                    }
                }
                catch (JSONException e)
                {
                    e.printStackTrace();
                }
            }
        }

        if (!StringUtils.isEmpty(hql.toString()))
        {
            hql = new StringBuilder(hql.toString().trim());
            if (hql.toString().toUpperCase().startsWith("AND "))
            {
                hql = new StringBuilder(hql.substring(4));
            }
            else if (hql.toString().toUpperCase().startsWith("OR "))
            {
                hql = new StringBuilder(hql.substring(3));
            }

            hql = new StringBuilder(" and (").append(hql).append(")");
        }

        return hql.toString();
    }

    public static String buildWhereClause(CriteriaQuestion q, Entity entity)
    {
        return buildWhereClause(q, entity, false, false);
    }

    private static String buildWhereClause(CriteriaQuestion q, Entity entity,
            boolean ignoreNull, boolean negate)
    {
        String whereClause = "";

        if (CriteriaQuestion.TYPE_TEXT.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Text(q, entity, negate);
        }
        else if (CriteriaQuestion.TYPE_DATE.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Date(q, entity);
        }
        else if (CriteriaQuestion.TYPE_NUMBER.equals(q.getType())
                || CriteriaQuestion.TYPE_FLOAT.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Number(q, entity);
        }
        else if (CriteriaQuestion.TYPE_RATING_RANGE.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Rating_Range(q);
        }
        else if (CriteriaQuestion.TYPE_IS_EMPTY.equals(q.getType()))
        {
            whereClause = buildWhereHQL_IsEmpty(q);
        }
        else if (CriteriaQuestion.TYPE_BOOLEAN.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Boolean(q, entity);
        }
        else if (CriteriaQuestion.TYPE_CHOICE_MULTI.equals(q.getType())
                || CriteriaQuestion.TYPE_CHOICE_SINGLE.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Choice(q, entity, ignoreNull, negate);
        }
        else if (CriteriaQuestion.TYPE_FILE_UPLOAD.equals(q.getType()))
        {
            whereClause = buildWhereHQL_File(q);
        }
        else if (CriteriaQuestion.TYPE_TREE.equals(q.getType()))
        {
            whereClause = buildWhereHQL_Tree(q);
        }
        else if (CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType())
                || CriteriaQuestion.TYPE_MATRIX_SINGLE.equals(q.getType()))
        {
            whereClause = (String) buildWhereHQL_Matrix(q);
        }

        return whereClause.toString();
    }

    private static Object buildWhereHQL_Matrix(CriteriaQuestion q)
    {
        String hql = "";

        if (q.isAnswered() && q.getMatrixOperation() != null
                && q.getAnswer().getMatrixHQLClauses() != null
                && !q.getAnswer().getMatrixHQLClauses().isEmpty())
        {
            hql = " and (";

            if (CriteriaQuestion.MATRIX_OPERATION_FUZZY
                    .equals(q.getMatrixOperation()))
            {
                for (String clause : q.getAnswer().getMatrixHQLClauses())
                {
                    hql += q.getQuestionKey() + " like '%"
                            + clause.replaceAll("'", "''") + "%' or ";
                }

                hql = hql.substring(0, hql.lastIndexOf(" or"));
            }
            else if (CriteriaQuestion.MATRIX_OPERATION_EXACT
                    .equals(q.getMatrixOperation()))
            {
                hql += q.getQuestionKey() + " like '" + q.getAnswer()
                        .getMatrixHQLClauses().get(0).replaceAll("'", "''") + "'";
            }

            hql += ")";
        }

        return hql;
    }

    private static String buildWhereHQL_File(CriteriaQuestion q)
    {
        String hql = "";
        if (q.isAnswered())
        {
            hql = " and (" + q.getQuestionKey()
                    + (q.getAnswer().getBooleanValue() ? " is not null"
                            : " is null")
                    + ")";
        }
        return hql;
    }

    private static String buildWhereHQL_IsEmpty(CriteriaQuestion q)
    {
        String hql = "";
        if (q.isAnswered())
        {
            hql = " and (" + q.getQuestionKey()
                    + (q.getAnswer().getBooleanValue() ? " is not null"
                            : " is null")
                    + ")";
        }
        return hql;
    }

    private static String buildWhereHQL_Boolean(CriteriaQuestion q, Entity entity)
    {
        String hql = "";
        if (q.isAnswered())
        {
            hql = q.getCustomHQLBuilder() != null
                    ? q.getCustomHQLBuilder().getHQL(q, entity)
                    : (" and (" + q.getQuestionKey() + "="
                            + (q.getAnswer().getBooleanValue() ? 1 : 0) + ")");
        }
        return hql;
    }

    private static String buildWhereHQL_Text(CriteriaQuestion q, Entity entity,
            boolean negate)
    {
        String hql = "";

        if (q.isAnswered())
        {
            boolean not = negate;
            boolean emptyOperation = false;
            String searchTerm = q.getAnswer() != null
                    ? q.getAnswer().getValue().trim().replaceAll("'", "''")
                    : "";
            switch (q.getOperation())
            {
                case CriteriaQuestion.OPERATION_EMPTY:
                    not = !negate;
                case CriteriaQuestion.OPERATION_NOT_EMPTY:
                    emptyOperation = true;
                    break;
                case CriteriaQuestion.OPERATION_NOT_EQUALS:
                    not = !negate;
                case CriteriaQuestion.OPERATION_EQUALS:
                    break;
                case CriteriaQuestion.OPERATION_BEGINS_WITH:
                    searchTerm = searchTerm + "%";
                    break;
                case CriteriaQuestion.OPERATION_ENDS_WITH:
                    searchTerm = "%" + searchTerm;
                    break;
                case CriteriaQuestion.OPERATION_NOT_CONTAINS:
                    not = !negate;
                case CriteriaQuestion.OPERATION_CONTAINS:
                    searchTerm = "%" + searchTerm + "%";
                    break;
            }
            hql = " and (";
            hql += "(";
            hql += q.getQuestionKey();

            hql += (not != emptyOperation ? " not " : "");
            hql += " like '" + searchTerm + "' ";

            if (searchTerm.contains("&amp;"))
            {
                hql += (not != emptyOperation ? " and " : " or ");

                hql += q.getQuestionKey();
                hql += (not != emptyOperation ? " not " : "");

                hql += " like '" + searchTerm.replace("&amp;", "&") + "' ";
            }

            hql += ")";

            if (not)
            {
                hql += " or " + q.getQuestionKey() + " is null";
            }
            hql += ")";
        }

        return hql;
    }

    private static String buildWhereHQL_Number(CriteriaQuestion q, Entity entity)
    {
        StringBuilder hql = new StringBuilder();

        if (q.isAnswered())
        {
            CriteriaAnswer a = q.getAnswer();

            if (!StringUtils.isEmpty(a.getFromValue())
                    && !StringUtils.isEmpty(a.getToValue()))
            {
                hql.append(" and (");
                hql.append(q.getQuestionKey()).append(" >= ")
                        .append(a.getFromValue().contains(".")
                                ? a.getFromValue().trim()
                                : a.getFromValue().trim() + ".0");
                hql.append(" and ").append(q.getQuestionKey()).append(" <= ")
                        .append(a.getToValue().contains(".") ? a.getToValue().trim()
                                : a.getToValue().trim() + ".0");
                hql.append(") ");
            }
            else if (!StringUtils.isEmpty(a.getFromValue()))
            {
                // provide support for saved searches which will not have
                // the operation field set
                q.setNumberOperation(
                        q.getNumberOperation() != null ? q.getNumberOperation()
                                : CriteriaQuestion.NUMBER_OPERATION_BETWEEN);

                if (CriteriaQuestion.NUMBER_OPERATION_EQUALS
                        .equals(q.getNumberOperation()))
                {
                    hql.append(" and (" + q.getQuestionKey() + " = "
                            + (a.getFromValue().contains(".")
                                    ? a.getFromValue().trim()
                                    : a.getFromValue().trim() + ".0")
                            + ")");
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_LESS_THAN
                        .equals(q.getNumberOperation()))
                {
                    hql.append(" and (" + q.getQuestionKey() + " < "
                            + (a.getFromValue().contains(".")
                                    ? a.getFromValue().trim()
                                    : a.getFromValue().trim() + ".0")
                            + ")");
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_LESS_THAN_EQUALS
                        .equals(q.getNumberOperation()))
                {
                    hql.append(" and (" + q.getQuestionKey() + " <= "
                            + (a.getFromValue().contains(".")
                                    ? a.getFromValue().trim()
                                    : a.getFromValue().trim() + ".0")
                            + ")");
                }
                else if (CriteriaQuestion.NUMBER_OPERATION_GREATER_THAN
                        .equals(q.getNumberOperation()))
                {
                    hql.append(" and (" + q.getQuestionKey() + " > "
                            + (a.getFromValue().contains(".")
                                    ? a.getFromValue().trim()
                                    : a.getFromValue().trim() + ".0")
                            + ")");
                }
                else
                {
                    hql.append(" and (" + q.getQuestionKey() + " >= "
                            + (a.getFromValue().contains(".")
                                    ? a.getFromValue().trim()
                                    : a.getFromValue().trim() + ".0")
                            + ")");
                }
            }
            else if (!StringUtils.isEmpty(a.getToValue()))
            {
                hql.append(" and (" + q.getQuestionKey() + " <= "
                        + (a.getToValue().contains(".") ? a.getToValue().trim()
                                : a.getToValue().trim() + ".0")
                        + ")");
            }
        }

        return hql.toString();
    }

    private static String buildWhereHQL_Rating_Range(CriteriaQuestion q)
    {
        StringBuilder hql = new StringBuilder();

        if (!q.isAnswered())
        {
            if (!q.isShowRatingNAMessage())
            {
                if (q.getRatingNAChoice()
                        .equals(CriteriaQuestion.RATING_NA_CHOICE_INCLUDE_NA))
                {
                }
                else if (q.getRatingNAChoice()
                        .equals(CriteriaQuestion.RATING_NA_CHOICE_EXCLUDE_NA))
                {
                    hql.append(" and (");
                    hql.append(q.getQuestionKey()).append(" <> ")
                            .append(q.getRatingNaDbValue());
                    hql.append(")");
                }
            }
        }
        else
        {
            CriteriaAnswer a = q.getAnswer();

            if (!StringUtils.isEmpty(a.getRatingFromValue())
                    && !StringUtils.isEmpty(a.getRatingToValue()))
            {
                hql.append(" and (");
                hql.append(q.getQuestionKey()).append(" >= ")
                        .append(a.getRatingFromValue());
                hql.append(" and ");
                hql.append(q.getQuestionKey()).append(" <= ")
                        .append(a.getRatingToValue());
                appendNAHql(hql, q);
                hql.append(") ");
            }
            else if (!StringUtils.isEmpty(a.getRatingFromValue()))
            {
                hql.append(" and (");
                hql.append(q.getQuestionKey()).append(" >= ")
                        .append(a.getRatingFromValue().trim());
                appendNAHql(hql, q);
                hql.append(") ");
            }
            else if (!StringUtils.isEmpty(a.getRatingToValue()))
            {
                hql.append(" and (");
                hql.append(q.getQuestionKey()).append(" <= ")
                        .append(a.getRatingToValue().trim());
                appendNAHql(hql, q);
                hql.append(") ");
            }
        }

        return hql.toString();
    }

    private static void appendNAHql(StringBuilder hql, CriteriaQuestion q)
    {
        if (!q.isShowRatingNAMessage())
        {
            if (q.getRatingNAChoice()
                    .equals(CriteriaQuestion.RATING_NA_CHOICE_INCLUDE_NA))
            {
                hql.append(" or ");
                hql.append(q.getQuestionKey()).append(" = ")
                        .append(q.getRatingNaDbValue());
            }
            else if (q.getRatingNAChoice()
                    .equals(CriteriaQuestion.RATING_NA_CHOICE_EXCLUDE_NA))
            {
                hql.append(" and (");
                hql.append(q.getQuestionKey()).append(" <> ")
                        .append(q.getRatingNaDbValue());
                hql.append(")");
            }
        }
    }

    private static String buildWhereHQL_Date(CriteriaQuestion q, Entity entity)
    {
        StringBuilder hql = new StringBuilder("");

        if (q.isAnswered())
        {
            if (q.getCustomHQLBuilder() == null)
            {
                Date fromDate = DateUtils.parseDate(q.getAnswer().getFromValue(),
                        q.getDateFormatForQuery(), null);
                Date toDate = DateUtils.parseDate(q.getAnswer().getToValue(),
                        q.getDateFormatForQuery(), null);
                if (isEntityQuestionEqualTypeAndClass(entity, q,
                        CriteriaQuestion.TYPE_DATE, Date.class))
                {
                    Date startDate = !q.isIncludeTime()
                            ? DateUtils.getStartDate(fromDate)
                            : fromDate;
                    Date endDate = !q.isIncludeTime() ? DateUtils.getEndDate(toDate)
                            : toDate;
                    if (fromDate != null && toDate != null
                            && DateUtils.isBeforeOrSame(fromDate, toDate))
                    {
                        hql.append(" and (").append(q.getQuestionKey());
                        hql.append(" between '").append(DateUtils.formatDate(
                                startDate, DBUtils.DB_DATE_TIME_FORMAT, null));
                        hql.append("' and '")
                                .append(DateUtils.formatDate(endDate,
                                        DBUtils.DB_DATE_TIME_FORMAT, null))
                                .append("') ");
                    }
                    else if (fromDate != null)
                    {
                        hql.append(" and (").append(q.getQuestionKey());
                        hql.append(" >= '").append(DateUtils.formatDate(startDate,
                                DBUtils.DB_DATE_TIME_FORMAT, null));
                        hql.append("') ");
                    }
                    else if (toDate != null)
                    {
                        hql.append(" and (").append(q.getQuestionKey());
                        hql.append(" <= '").append(DateUtils.formatDate(endDate,
                                DBUtils.DB_DATE_TIME_FORMAT, null));
                        hql.append("') ");
                    }
                }
                else if (isEntityQuestionEqualTypeAndClass(entity, q,
                        CriteriaQuestion.TYPE_DATE, String.class))
                {
                    if (fromDate != null && toDate == null)
                    {
                        toDate = fromDate;
                    }

                    if (toDate != null && fromDate == null)
                    {
                        fromDate = toDate;
                    }

                    StringBuilder dc = new StringBuilder("");
                    while (DateUtils.isBeforeOrSame(fromDate, toDate))
                    {
                        dc.append(" ").append(q.getQuestionKey()).append(" like '")
                                .append(DateUtils.formatDate(fromDate,
                                        "MM/dd/yyyy"))
                                .append("' ");

                        fromDate = DateUtils.addDays(fromDate, 1);

                        if (DateUtils.isBeforeOrSame(fromDate, toDate))
                        {
                            dc.append(" or ");
                        }
                    }
                    if (!dc.equals(""))
                    {
                        dc = new StringBuilder(" and (").append(dc).append(")");
                        hql = new StringBuilder(dc);
                    }
                }
                else
                {
                    logger.warn("buildHQL_Date(): unknown date at \""
                            + q.getQuestionKey() + "\"");
                }
            }
            else
            {
                hql.append(q.getCustomHQLBuilder().getHQL(q, entity));
            }

        }

        return hql.toString();
    }

    public static Map getChoiceMapForTranslation(Map<String, Boolean> choices,
            CriteriaQuestion q)
    {
        Map<String, Boolean> ret = new LinkedHashMap<>();
        for (Entry<String, Boolean> c : choices.entrySet())
        {
            if (!StringUtils.isEmpty(c.getKey()))
            {
                String[] parts = c.getKey().split("\\|");
                ret.put(parts[0], c.getValue());
                if (parts.length > 1)
                {
                    ret.put(parts[1], c.getValue());
                    if (q.isDfMultiChoice())
                    {
                        // OUTCOME-9635 temporary fix
                        ret.put(c.getKey(), c.getValue());
                    }
                }
            }
        }

        return ret;
    }

    /**
     * Returns TRUE if criteriaQuestion.type=type, and if the
     * "criteriaQuestion.questionKey" represents an actual property in the
     * "entity.entityClass" whose data-type matches "clazz". Returns FALSE
     * otherwise.
     */
    public static boolean isEntityQuestionEqualTypeAndClass(Entity entity,
            CriteriaQuestion criteriaQuestion, String type, Class clazz)
    {
        boolean ret = false;

        if (entity != null && criteriaQuestion != null && type != null
                && clazz != null && criteriaQuestion.getType().equals(type))
        {
            try
            {
                Object testInstance = makeTestInstance(entity.getEntityClass());

                int beginIndex = criteriaQuestion.getQuestionKey().indexOf(".") + 1;
                String propKey = criteriaQuestion.getQuestionKey()
                        .substring(beginIndex);
                String propAlias = criteriaQuestion.getQuestionKey().substring(0,
                        beginIndex - 1);

                if (propAlias.equals(entity.getEntityHqlAlias()))
                {
                    String[] splitProps = propKey.split("\\.");

                    StringBuilder propPath = new StringBuilder("");

                    for (int i = 0; i < splitProps.length; i++)
                    {
                        propPath.append(splitProps[i]);

                        Class propType = PropertyUtils.getPropertyType(testInstance,
                                propPath.toString());

                        PropertyUtils.setProperty(testInstance, propPath.toString(),
                                makeTestInstance(propType));

                        if ((i + 1) < splitProps.length)
                        {
                            propPath.append(".");
                        }
                    }

                    Class propertyClass = PropertyUtils
                            .getPropertyType(testInstance, propKey);

                    ret = propertyClass != null
                            && propertyClass.isAssignableFrom(clazz);
                }
                else
                {
                    // case when we have implicit joins in a static where
                    // developer to ensure the joined tables date
                    // questions do not map to a string field
                    // otherwise this method will need to be updated
                    ret = true;
                }

            }
            catch (Exception e)
            {
                if (!criteriaQuestion.getQuestionKey().contains("extendedData"))
                {
                    logger.debug(
                            "isEntityQuestionEqualTypeAndClass(): could not introspect entity property \""
                                    + criteriaQuestion.getQuestionKey()
                                    + "\" - reason is: " + e.getMessage());
                }
            }
        }

        return ret;
    }

    /**
     * Attempts to return a "test instance" of the specified "klass"
     */
    private static Object makeTestInstance(Class klass)
    {
        if (klass == null)
        {
            throw new IllegalArgumentException("klass may not be null.");
        }

        Object testInstance = null;

        try
        {
            // attempt invoking default constructor...
            testInstance = klass.newInstance();
        }
        catch (Exception e)
        {
            // klass has no default constructor...
            testInstance = null;
        }

        if (testInstance == null)
        {
            // klass has no default constructor to invoke. Therefore we must
            // attempt identify the specific class-type of the klass so we can
            // invoke its custom constructor.

            if (ClassUtils.isAssignable(klass, Integer.class))
            {
                testInstance = Integer.valueOf(0);
            }
            else if (ClassUtils.isAssignable(klass, int.class))
            {
                testInstance = 0;
            }
            else if (ClassUtils.isAssignable(klass, Long.class))
            {
                testInstance = new Long(0);
            }
            else if (ClassUtils.isAssignable(klass, long.class))
            {
                testInstance = 0l;
            }
            else if (ClassUtils.isAssignable(klass, Float.class))
            {
                testInstance = new Float(0);
            }
            else if (ClassUtils.isAssignable(klass, float.class))
            {
                testInstance = 0f;
            }
            else if (ClassUtils.isAssignable(klass, Double.class))
            {
                testInstance = new Double(0);
            }
            else if (ClassUtils.isAssignable(klass, double.class))
            {
                testInstance = 0d;
            }
            else if (ClassUtils.isAssignable(klass, Boolean.class))
            {
                testInstance = new Boolean(false);
            }
            else if (ClassUtils.isAssignable(klass, boolean.class))
            {
                testInstance = false;
            }
            // TODO: add more conditions, as needed...
        }

        return testInstance;
    }

    private static String buildWhereHQL_Choice(CriteriaQuestion q, Entity entity,
            boolean ignoreNull, boolean negate)
    {
        StringBuilder hql = new StringBuilder("");
        CriteriaAnswer a = q.getAnswer();
        if (q.isAnswered() && (a.isNullOperation()
                || (a.getValueChoices() != null && !a.getValueChoices().isEmpty())))
        {
            if (!StringUtils.isEmpty(a.getAndOrValue()))
            {
                if (q.getCustomHQLBuilder() == null)
                {
                    Map<String, Boolean> valueChoices = PortalUtils.supportedLanguages.length > 1
                            ? getChoiceMapForTranslation(a.getValueChoices(), q)
                            : a.getValueChoices();
                    boolean isMultiChoice = q.isMultiChoiceAnswered();
                    boolean isContains = CriteriaQuestion.OPERATION_CONTAINS
                            .equals(q.getOperation());
                    boolean isNot = a.isNotOperation();
                    boolean isOr = a.isOrOperation();
                    boolean isEquals = a.isEqualsOperation();
                    boolean isNullability;
                    boolean otherSelected = false;
                    List<String> selectedChoices = new ArrayList<>();
                    for (String choice : valueChoices.keySet())
                    {
                        if (valueChoices.get(choice))
                        {
                            if (q.isOptionIncludeOtherFlag()
                                    && (q.getParamName() + "other").equals(choice))
                            {
                                if (!StringUtils.isEmpty(a.getValueOther()))
                                {
                                    choice = a.getValueOther();
                                }
                                otherSelected = true;
                            }
                            selectedChoices.add(choice);
                            if (choice.contains("&amp;"))
                            {
                                selectedChoices.add(choice.replace("&amp;", "&"));
                            }
                            else if (choice.contains("&"))
                            {
                                selectedChoices.add(choice.replace("&", "&amp;"));
                            }
                        }
                    }
                    isNullability = a.isNullOperation() || (isOr
                            && (!otherSelected
                                    || StringUtils.isEmpty(a.getValueOther()))
                            && !valueChoices.containsValue(Boolean.FALSE));

                    if (isContains || (!isMultiChoice && isNullability))
                    {
                        String answerDelimiter = q.getAnswerDelimiter();
                        boolean isWrappedDelimiter = "^".equals(answerDelimiter);
                        String questionKey = q.getQuestionKey();
                        String optionStringPrefix = " (".concat(questionKey)
                                .concat(isNot == isNullability ? "" : " not")
                                .concat(" like '").concat(!isNullability ? "%" : "")
                                .concat(isWrappedDelimiter ? answerDelimiter : "");
                        String optionStringSuffix = (isWrappedDelimiter
                                ? answerDelimiter
                                : "").concat(!isNullability ? "%" : "")
                                .concat("') ");
                        if ((ignoreNull || !isNullability)
                                && !selectedChoices.isEmpty())
                        {
                            hql.append(optionStringPrefix)
                                    .append(StringUtils.join(selectedChoices
                                            .stream().map(c -> c.replace("'", "''"))
                                            .collect(Collectors.toList()),
                                            optionStringSuffix
                                                    .concat(isOr != isNot ? "or "
                                                            : "and ")
                                                    .concat(optionStringPrefix)))
                                    .append(optionStringSuffix);
                            if (isEquals && answerDelimiter != null)
                            {
                                hql.append(isNot ? "or " : "and ").append("(len(")
                                        .append(questionKey)
                                        .append(") - len(replace(")
                                        .append(questionKey).append(", '")
                                        .append(answerDelimiter).append("', ''))) ")
                                        .append(isNot ? "<> " : "= ")
                                        .append((isWrappedDelimiter ? 1 : -1)
                                                + selectedChoices.size());
                            }
                        }
                        else if (!isNullability || isNot)
                        {
                            hql.append(" (").append(optionStringPrefix)
                                    .append(optionStringSuffix).append(" or ")
                                    .append(q.getQuestionKey()).append(" is")
                                    .append(isNot ? "" : " not").append(" NULL) ");
                        }
                        else
                        {
                            hql.append(optionStringPrefix)
                                    .append(optionStringSuffix);
                        }
                    }
                    else
                    {
                        hql.append(" (").append(q.getQuestionKey());
                        if (selectedChoices.size() == 1 && !isNullability)
                        {
                            hql.append(negate ? " <> '" : " = '").append(
                                    selectedChoices.get(0).replace("'", "''"))
                                    .append("'");
                        }
                        else if ((negate || ignoreNull
                                || !selectedChoices.isEmpty()) && !isNullability)
                        {
                            hql.append(negate ? " not" : "").append(" in ").append(
                                    DBUtils.buildInClauseWithQuotes(selectedChoices));
                        }
                        else
                        {
                            hql.append(" is").append(negate ? "" : " not")
                                    .append(" NULL");
                        }
                        hql.append(") ");
                    }

                    if (!isNullability && !ignoreNull && negate
                            && !selectedChoices.isEmpty())
                    {
                        hql = new StringBuilder(" (").append(hql).append(" or (")
                                .append(q.getQuestionKey()).append(" is NULL)) ");
                    }

                    if (hql.length() > 0)
                    {
                        hql = new StringBuilder(" and").append(" (")
                                .append(hql.toString().trim()).append(") ");
                    }
                }
                else
                {
                    hql.append(q.getCustomHQLBuilder().getHQL(q, entity));
                }
            }
        }

        return hql.toString();
    }

    private static String buildWhereHQL_Tree(CriteriaQuestion q)

    {
        StringBuilder hql = new StringBuilder("");
        CriteriaAnswer a = q.getAnswer();
        if (q.isAnswered() && !StringUtils.isEmpty(a.getValue())
                && !StringUtils.isEmpty(a.getAndOrValue()))
        {
            if (q.getQuestionHandlerClass() != null && CriteriaQuestionHandler.class
                    .isAssignableFrom(q.getQuestionHandlerClass()))
            {
                try
                {
                    CriteriaQuestionHandler h = q.getQuestionHandlerClass()
                            .newInstance();

                    String s = h.getWhereHQL(q);
                    if (!StringUtils.isEmpty(s))
                    {
                        hql.append(s);
                    }
                }
                catch (Exception e)
                {
                }
            }
            else
            {
                try
                {
                    JSONArray answer = new JSONArray(a.getValue());
                    for (int i = 0; i < answer.length(); i++)
                    {
                        hql.append(a.getAndOrValue()).append(" ")
                                .append(q.getQuestionKey()).append(" like '%\"")
                                .append(answer.get(i)).append("\"%'");
                    }
                }
                catch (JSONException e)
                {
                    e.printStackTrace();
                }
            }
        }

        if (!StringUtils.isEmpty(hql.toString()))
        {
            hql = new StringBuilder(hql.toString().trim());
            if (hql.toString().toUpperCase().startsWith("AND "))
            {
                hql = new StringBuilder(hql.substring(4));
            }
            else if (hql.toString().toUpperCase().startsWith("OR "))
            {
                hql = new StringBuilder(hql.substring(3));
            }

            hql = new StringBuilder(" and (").append(hql).append(")");
        }

        return hql.toString();
    }

    public static void buildWhereRelatedHQL(Entity entity,
            FromWhereClause fromWhereClause)
    {
        if (entity.isMaster())
        {
            if (entity.getRelationships() != null)
            {
                for (Relationship r : entity.getRelationships())
                {
                    r.getRelatedEntity().setSearchModel(entity.getSearchModel());

                    if (r.getRelatedEntity().isCriteriaAnswered())
                    {
                        switch (r.getType())
                        {
                            case JOINED:
                                if (!StringUtils.isEmpty(r.getStaticWhereHql()))
                                {
                                    fromWhereClause.appendToWhereClause(
                                            r.getStaticWhereHql());
                                }
                                buildWhereHQL(r.getRelatedEntity(), true,
                                        fromWhereClause);
                                break;

                            case MANY_TO_ONE:
                            case ONE_TO_ONE:
                            case SAUSAGE:
                                fromWhereClause.appendToWhereClause(
                                        buildRelationshipHQL(entity, r));
                                break;

                            case CUSTOM:
                                fromWhereClause.appendToWhereClause(
                                        getCustomWhereHQL(entity, r));
                                break;

                            case COUNT:
                                fromWhereClause.appendToWhereClause(
                                        getCountWhereHQL(entity, r));
                                break;

                            case DETAIL:
                                break;
                            default:
                                break;
                        }
                    }
                    else if (isRelationQuestionAnswered(r))
                    {
                        fromWhereClause.appendToWhereClause(
                                buildRelationshipHQL(entity, r));
                    }
                }
            }
        }
    }

    private static String getCountWhereHQL(Entity entity, Relationship r)
    {
        StringBuilder whereHql = new StringBuilder("");
        try
        {
            SearchCountRelationshipInterface scri = r
                    .getCountRelationshipInterface().newInstance();

            whereHql = new StringBuilder(scri.getMasterWhereHQL(entity, r));
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }

        return whereHql.toString();
    }

    public static String buildRelatedDescription(Entity entity,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);
        MessageSource messageSource = PortalUtils.getMessageSource();

        StringBuilder description = new StringBuilder("");

        if (entity.getRelationships() != null)
        {
            for (Relationship r : entity.getRelationships())
            {
                r.getRelatedEntity().setSearchModel(entity.getSearchModel());

                final boolean relationQuestionAnswered = isRelationQuestionAnswered(
                        r);

                if (relationQuestionAnswered)
                {
                    description.append("<li>");
                    if (r.getLabel().startsWith("i18n."))
                    {
                        description.append(messageSource.getMessage(r.getLabel(),
                                null, locale));
                    }
                    else
                    {
                        description.append(r.getLabel());
                    }
                    description.append(" ").append(
                            (r.getRelationQuestion().getAnswer().getBooleanValue()
                                    ? messageSource.getMessage(
                                            "i18n.filterDescription.exists", null,
                                            locale)
                                    : messageSource.getMessage(
                                            "i18n.filterDescription.doesNotExist",
                                            null, locale)))
                            .append("</li> ");
                }

                if (r.getRelatedEntity().isCriteriaAnswered()
                        && (!relationQuestionAnswered || r.getRelationQuestion()
                                .getAnswer().getBooleanValue()))
                {
                    String label = "";
                    if (r.getRelatedEntity().getEntityLabel() != null && r
                            .getRelatedEntity().getEntityLabel().startsWith("i18n"))
                    {
                        label = messageSource.getMessage(
                                r.getRelatedEntity().getEntityLabel(), null,
                                PortalUtils.getLocale(request));
                    }
                    else
                    {
                        label = r.getRelatedEntity().getEntityLabel();
                    }
                    description.append("<li><strong>").append(label)
                            .append("</strong><ul>");
                    switch (r.getType())
                    {
                        case JOINED:
                        case MANY_TO_ONE:
                        case ONE_TO_ONE:
                        case SAUSAGE:
                        case CUSTOM:
                            description.append(
                                    buildDescription(r.getRelatedEntity(), locale));
                            break;
                        case DETAIL:
                        default:
                            for (CriteriaQuestion question : r.getRelatedEntity()
                                    .getCriteriaModel().getCriteriaQuestions())
                            {
                                if (question != null && question.isAnswered()
                                        && r.getRelatedEntity() != null)
                                {
                                    description.append(buildDescription(question,
                                            r.getRelatedEntity(), locale));
                                }
                            }
                            break;
                    }
                    description.append("</ul></li>");
                }
            }
        }

        return description.toString();
    }

    private static String getCustomWhereHQL(Entity entity, Relationship r)
    {
        SearchCustomRelationshipInterface scri = getCustomSearchInterface(entity,
                r);

        StringBuilder whereHql = new StringBuilder(
                scri != null ? scri.getMasterWhereHQL(entity, r) : "");

        return whereHql.toString();
    }

    /**
     * In the result-grid, which is supported by the JQGridModel, the user may have
     * changed the column widths or column positions. These changes need to be
     * synchronized in the respective entity questions of the SearchModel.
     *
     * @param gridModel
     * @param searchModel
     */
    public static void syncModelElements(JQGridModel gridModel,
            SearchModel searchModel)
    {
        Entity masterEntity = searchModel.getMasterEntity();
        JQGridSearch masterSearch = gridModel.getMaster();

        syncEntityElements(masterEntity, masterSearch);

        if (LastViewedHelper.SAVED_SEARCH_KEY.equals(masterSearch.getSearchName()))
        {
            for (String key : masterSearch.getDetailSearches().keySet())
            {
                gridModel.addDetail(key, masterSearch.getDetailSearches().get(key));
            }
        }

        for (Relationship r : masterEntity.getDetailRelationships())
        {
            Entity detailEntity = r.getRelatedEntity();

            JQGridSearch deatilSearch = null;

            deatilSearch = gridModel.getDetail(detailEntity.getEnityKey());

            syncEntityElements(detailEntity, deatilSearch);
        }
    }

    private static void syncEntityElements(Entity entity, JQGridSearch jqSearch)
    {
        // Sync colModel stuff....
        try
        {
            JSONArray entityColModel = jqSearch.getColModelJSON();

            for (int i = 0; i < entityColModel.length(); i++)
            {
                JSONObject col = entityColModel.getJSONObject(i);
                for (CriteriaQuestion q : entity.getCriteriaModel()
                        .getCriteriaQuestions())
                {
                    if (col.has("index") && q.getQuestionKey() != null
                            && q.getQuestionKey().equals(col.getString("index")))
                    {
                        q.setColIndex(i);
                        q.setColWidth(col.getInt("width"));
                        q.setVisibleResults(!col.getBoolean("hidden"));
                        break;
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static EmailModel getEmailModel(SearchModel searchModel,
            int emailerIndex, JSONArray selectedIds, HttpServletRequest request)
    {
        EmailModel emailModel = null;

        if (searchModel != null)
        {
            List<EmailRecipient> emailRecipients = getEmailRecipients(
                    searchModel.getMasterEntity(), emailerIndex, selectedIds);

            if (emailRecipients != null && !emailRecipients.isEmpty())
            {
                emailModel = new EmailModel();
                emailModel.setEmailRecipients(emailRecipients);
                emailModel.setFromAddress(
                        PortalUtils.getUserLoggedIn(request).getEmail());
                emailModel.setSubject(null);
                emailModel.setBody(null);
                emailModel.setActionName(null);
            }
        }

        return emailModel;
    }

    /**
     * This will reset the 'gridModel' with the specified 'searchModel'.
     *
     * @param gridModel
     * @param searchModel
     * @param locale
     *            TODO
     */
    public static void resetJQGridModel(JQGridModel gridModel,
            SearchModel searchModel, Locale locale)
    {
        if (gridModel == null || gridModel.getMaster() == null)
        {
            return;
        }

        // Next, synchronize elements of the SearchModel with the JQGridModel...
        syncModelElements(gridModel, searchModel);

        if (searchModel != null)
        {
            JQGridModel newGridModel = createJQGridModel(searchModel, locale);

            JQGridSearch masterSearch = gridModel.getMaster();
            masterSearch.setMasterId(null);
            masterSearch.setMaster(true);
            masterSearch.setEntity(newGridModel.getMaster().getEntity());
            masterSearch.setHql(newGridModel.getMaster().getHql());
            masterSearch.setGridColumns(newGridModel.getMaster().getJqGridColumns(),
                    locale);
            masterSearch.setColModel(newGridModel.getMaster().getColModel());

            for (String key : newGridModel.getDetails().keySet())
            {
                JQGridSearch gmSearch = newGridModel.getDetail(key);
                JQGridSearch detailSearch = gridModel.getDetail(key);

                detailSearch.setMasterId(gmSearch.getMasterId());
                detailSearch.setMaster(false);
                detailSearch.setEntity(gmSearch.getEntity());
                detailSearch.setMasterHqlFkName(gmSearch.getMasterHqlFkName());
                detailSearch.setHql(gmSearch.getHql());
                detailSearch.setGridColumns(gmSearch.getJqGridColumns(), locale);
            }

        }
    }

    public static String getEmailCampaignHql(Entity entity, int emailerIndex,
            JSONArray selectedIds, String sortIndex, String sortOrder)
    {
        String ret = "";
        if (entity != null && entity.isEmailable())
        {
            String hql = buildHQL(entity, true);

            if (null != selectedIds && selectedIds.length() > 0)
            {
                hql += hql.indexOf(" where ") > -1 ? " and " : " where ";
                hql += entity.getEntityHqlAlias() + "." + entity.getEntityHqlId()
                        + " in " + DBUtils.buildInClause(selectedIds);
            }
            if (!StringUtils.isEmpty(hql))
            {
                StringBuilder sb = new StringBuilder();
                sb.append("select ");
                EmailingEmailer ee = entity.getEmailingModel().getEmailers()
                        .get(emailerIndex);
                List<String> selectParams = new ArrayList<>();
                if (ee.getEmailBeanPaths().get(0) != null)
                {
                    selectParams.add(ee.getEmailBeanPaths().get(0));
                }
                if (ee.getFirstNameBeanPath() != null)
                {
                    selectParams.add(ee.getFirstNameBeanPath());
                }
                if (ee.getLastNameBeanPath() != null)
                {
                    selectParams.add(ee.getLastNameBeanPath());
                }
                if (ee.getLocaleBeanPath() != null)
                {
                    selectParams.add(ee.getLocaleBeanPath());
                }
                String params = selectParams.stream()
                        .collect(Collectors.joining(","));
                sb.append(params).append(hql);

                if (ee.getNoEmailCampaignWhereClause() != null)
                {
                    sb.append(hql.indexOf(" where ") > -1 ? " and " : " where ")
                            .append(ee.getNoEmailCampaignWhereClause());
                }
                sb.append(" order by ").append(sortIndex).append(" ")
                        .append(sortOrder);
                ret = sb.toString();
            }
        }
        return ret;

    }

    public static List<EmailRecipient> getEmailRecipients(Entity entity,
            int emailerIndex, JSONArray selectedIds)
    {
        List<EmailRecipient> emailRecipients = null;

        if (entity != null && entity.isEmailable())
        {
            String hql = buildHQL(entity, true);

            if (null != selectedIds && selectedIds.length() > 0)
            {
                hql += hql.indexOf(" where ") > -1 ? " and " : " where ";
                hql += entity.getEntityHqlAlias() + "." + entity.getEntityHqlId()
                        + " in " + DBUtils.buildInClause(selectedIds);
            }
            if (!StringUtils.isEmpty(hql))
            {
                if (entity.getEmailingModel() != null && entity.getEmailingModel()
                        .getEmailers().size() > (emailerIndex))
                {
                    EmailingEmailer ee = entity.getEmailingModel().getEmailers()
                            .get(emailerIndex);

                    StringBuilder sb = new StringBuilder();
                    sb.append("select ");

                    // Modified to work with CoopWtr email where mappings might
                    // not be set, only a single name is mapped, no multiple
                    // emails

                    boolean firstNameEmpty = false;
                    boolean lastNameEmpty = false;

                    if (ee.getFirstNameBeanPath() == null
                            || StringUtils.isEmpty(ee.getFirstNameBeanPath()))
                    {
                        firstNameEmpty = true;
                    }
                    if (ee.getLastNameBeanPath() == null
                            || StringUtils.isEmpty(ee.getLastNameBeanPath()))
                    {
                        lastNameEmpty = true;
                    }

                    int usernameIndex = -1;
                    for (int i = 0; i < ee.getEmailBeanPaths().size(); i++)
                    {
                        sb.append(ee.getEmailBeanPaths().get(i));

                        if (ee.getEmailBeanPaths().get(i).endsWith(".username"))
                        {
                            usernameIndex = i;
                        }
                        if (i + 1 < ee.getEmailBeanPaths().size())
                        {
                            sb.append(", ");
                        }
                        else if (!firstNameEmpty || !lastNameEmpty)
                        {
                            sb.append(", ");
                        }
                        // If only email address is mapped then pull the email
                        // again to have the rest of this routine work and to
                        // show the email as the results in the name
                        else if (firstNameEmpty && lastNameEmpty)
                        {
                            sb.append(", ").append(ee.getEmailBeanPaths().get(i));
                        }
                    }

                    if (!firstNameEmpty)
                    {
                        sb.append(ee.getFirstNameBeanPath());
                        if (!lastNameEmpty)
                        {
                            sb.append(", ");
                        }
                    }
                    if (!lastNameEmpty)
                    {
                        sb.append(ee.getLastNameBeanPath());
                    }

                    sb.append(hql);

                    if (!firstNameEmpty || !lastNameEmpty)
                    {
                        sb.append(" order by ");

                        if (!firstNameEmpty)
                        {
                            sb.append(ee.getFirstNameBeanPath());
                            if (!lastNameEmpty)
                            {
                                sb.append(", ");
                            }
                        }
                        if (!lastNameEmpty)
                        {
                            sb.append(ee.getLastNameBeanPath());
                        }
                    }

                    List results = PortalUtils.getHt().find(sb.toString());

                    emailRecipients = new ArrayList<>();

                    for (Iterator i = results.iterator(); i.hasNext();)
                    {
                        Object[] row = (Object[]) i.next();
                        EmailRecipient r = new EmailRecipient();

                        if (usernameIndex != -1)
                        {
                            r.setUsername((String) row[usernameIndex]);
                        }
                        for (int j = 0; j < ee.getEmailBeanPaths().size(); j++)
                        {
                            if (EmailUtils.isValidEmailAddress((String) row[j]))
                            {
                                r.setEmail((String) row[j]);
                                break;
                            }
                        }

                        int nameIndex = ee.getEmailBeanPaths().size();

                        if (row.length > (nameIndex + 1))
                        {
                            r.setName((String) row[nameIndex] + " "
                                    + (String) row[nameIndex + 1]);
                        }
                        else
                        {
                            r.setName((String) row[nameIndex]);
                        }

                        emailRecipients.add(r);
                    }
                }
            }
        }

        return emailRecipients;
    }

    public static void cleanSearchModelQuestions(SearchModel searchModel)
    {
        cleanQuestionsWithDuplicateKeys(searchModel.getMasterEntity());
        massageQuestions(null, searchModel.getMasterEntity());

        if (searchModel.getMasterEntity().getRelationships() != null)
        {
            for (Relationship relationship : searchModel.getMasterEntity()
                    .getRelationships())
            {
                cleanQuestionsWithDuplicateKeys(relationship.getRelatedEntity());
                massageQuestions(relationship, relationship.getRelatedEntity());
            }
        }
    }

    /**
     * The purpose of this method is to solve a problem that occurs when 2 or more
     * questions have duplicate 'questionKeys' in a SearchModel Entity. When this
     * happens you get buggy behaviour in the "criteria page" and "grid page".
     * Typically these problems are caused by developers who do not keep these keys
     * unique as they add questions to a CriteriaModel (fixable). However in some
     * cases the questions are added dynamically, such as from another question
     * framework, for which there isn't too much control to ensure key-uniqueness
     * (*cough* *AcrmRegistrationQuestion's* *cough*).
     *
     * So the solution is to remove questions from an Entity's CriteriaModel that
     * have a questionKey of a another question that's been already added.
     *
     * That's what this method does...
     */
    private static void cleanQuestionsWithDuplicateKeys(Entity entity)
    {
        Map<CriteriaGroup, List<CriteriaQuestion>> removals = new HashMap<>();
        List<String> keys = new ArrayList<>();

        for (CriteriaGroup group : entity.getCriteriaModel().getCriteriaGroups())
        {
            for (CriteriaQuestion question : group.getQuestions())
            {
                if (keys.contains(question.getQuestionKey()))
                {
                    if (!removals.containsKey(group))
                    {
                        removals.put(group, new ArrayList<CriteriaQuestion>());
                    }

                    removals.get(group).add(question);
                }
                else
                {
                    keys.add(question.getQuestionKey());
                }
            }
        }

        for (Map.Entry<CriteriaGroup, List<CriteriaQuestion>> removal : removals
                .entrySet())
        {
            for (CriteriaQuestion question : removal.getValue())
            {
                removal.getKey().getQuestions().remove(question);
            }
        }
    }

    private static void massageQuestions(Relationship relationship, Entity entity)
    {
        final boolean isMaster = entity.isMaster();
        final SearchCustomRelationshipInterface customSearchInterface = entity
                .isCustomRelationshipEntity() ? entity.getCustomSearchInterface()
                        : null;
        final boolean isOneToOne = !isMaster && relationship != null
                && relationship.getType() == Relationship.TYPE.ONE_TO_ONE;
        for (CriteriaQuestion question : entity.getCriteriaModel()
                .getCriteriaQuestions())
        {
            String questionType = question.getType();
            if (questionType != null)
            {
                switch (questionType)
                {
                    case CriteriaQuestion.TYPE_TEXT:
                        question.setOperation(CriteriaQuestion.OPERATION_CONTAINS);
                        break;
                    case CriteriaQuestion.TYPE_CHOICE:
                        boolean isMulti = false;
                        if (!isMaster)
                        {
                            if (customSearchInterface != null
                                    && customSearchInterface instanceof SearchCustomRelationshipMultiChoiceAbstract)
                            {
                                isMulti = true;
                            }
                            else
                            {
                                CriteriaQuestionHQLBuilder customHQLBuilder = question
                                        .getCustomHQLBuilder();
                                if (customHQLBuilder != null
                                        && customHQLBuilder instanceof CriteriaQuestionMultiChoiceHQLBuilder)
                                {
                                    isMulti = true;
                                }
                            }
                        }
                        if (isMaster || !isMulti)
                        {
                            isMulti = (!isMaster && !isOneToOne)
                                    || CriteriaQuestion.OPERATION_CONTAINS
                                            .equals(question.getOperation());
                        }
                        question.setType(
                                isMulti ? CriteriaQuestion.TYPE_CHOICE_MULTI
                                        : CriteriaQuestion.TYPE_CHOICE_SINGLE);
                        break;
                }
            }
        }
    }

    public static void addRelationQuestions(SearchModel searchModel, Locale locale)
    {
        final Entity masterEntity = searchModel.getMasterEntity();
        if (masterEntity != null && masterEntity.getRelationships() != null)
        {
            int r = 0;
            for (Relationship relationship : masterEntity.getRelationships())
            {
                if (!relationship.isAlwaysPresent() && (relationship
                        .getType() == Relationship.TYPE.MANY_TO_ONE
                        || relationship.getType() == Relationship.TYPE.ONE_TO_ONE))
                {
                    final boolean newQuestion = relationship
                            .getRelationQuestion() == null;
                    final CriteriaQuestion relationQuestion;
                    if (newQuestion)
                    {
                        relationship.initRelationQuestion(null);
                    }
                    relationQuestion = relationship.getRelationQuestion();
                    if (newQuestion)
                    {
                        if (!relationship.getRelatedEntity().getCriteriaModel()
                                .isVisibleCriteria())
                        {
                            relationQuestion
                                    .setQuestionText(relationship.getLabel());
                        }
                    }
                    relationQuestion.setQuestionOrder(r++);
                }
            }
        }
    }

    public static boolean isRelationQuestionAnswered(Relationship relationship)
    {
        final boolean ret;
        if (relationship != null)
        {
            CriteriaQuestion relationQuestion = relationship.getRelationQuestion();
            ret = relationQuestion != null && relationship.getType() != null
                    && relationQuestion.isAnswered()
                    && CriteriaQuestion.TYPE_BOOLEAN
                            .equals(relationQuestion.getType())
                    && (relationship.getType() == TYPE.MANY_TO_ONE
                            || relationship.getType() == TYPE.ONE_TO_ONE);
        }
        else
        {
            ret = false;
        }
        return ret;
    }

    /**
     * Saving the search model to database is a multi-step process involving
     * multiple entities working in concert with each other.
     *
     * 1. We start by persisting the CriteriaModel. Saving this entity requires that
     * we first save the list of CriteriaGroups at has. A CriteriaGroup has a list
     * of CriteriaQuestions which will also have to be persisted.
     *
     * 2. Save the search model's master entity. Entity has two properties,
     * relationships and attributes that are not mapped in the database.
     * Relationship is a join of two Entity entities. Attributes is persisted as a
     * number of EntityAttribute entities.
     *
     * 3. Save the searchModel entity. All fields are mapped except the two Map
     * properties, massAssignButtons and attributes, which will be persisted as
     * SearchMassAssignButton and SearchModelAttribute entities resp.
     *
     * @param searchModel
     */
    public static void saveSearchModel(SearchModel searchModel)
    {
        PortalUtils.getHt().inTransactionalScope(()->{
            // persist criteriaModel
            searchModel.getMasterEntity().getCriteriaModel().setId(null);
            PortalUtils.getHt().saveOrUpdate(searchModel.getMasterEntity().getCriteriaModel());
            saveCriteriaGroups(searchModel.getMasterEntity().getCriteriaModel());

            // save master entity's emailing model
            saveEntityEmailingModel(searchModel.getMasterEntity().getEmailingModel());

            // save searchModel's master entity and corresponding associations
            searchModel.getMasterEntity().setId(null);
            PortalUtils.getHt().saveOrUpdate(searchModel.getMasterEntity());
            saveEntityRelationships(searchModel.getMasterEntity());
            saveEntityAttributes(searchModel.getMasterEntity());

            // persist search model, save mass assign buttons, search model
            // attributes and plugins
            searchModel.setId(null);
            PortalUtils.getHt().saveOrUpdate(searchModel);
            saveSearchModelMassAssignButtons(searchModel);
            saveSearchModelAttributes(searchModel);
            saveSearchModelPlugins(searchModel);
        });
    }

    private static void saveEntityEmailingModel(EmailingModel emailingModel)
    {
        if (emailingModel != null)
        {
            // save emailing model
            emailingModel.setId(null);
            PortalUtils.getHt().saveOrUpdate(emailingModel);

            // save emails. saving emailers requires that we persist beanPaths
            // as
            // several EmailerEmailBeanPath entities
            if (emailingModel.getEmailers() != null)
            {
                for (EmailingEmailer e : emailingModel.getEmailers())
                {
                    // persist emailer
                    e.setId(null);
                    e.setModel(emailingModel);
                    PortalUtils.getHt().saveOrUpdate(e);
                    if (e.getEmailBeanPaths() != null)
                    {
                        for (String path : e.getEmailBeanPaths())
                        {
                            EmailerEmailBeanPath p = new EmailerEmailBeanPath();
                            p.setEmailer(e);
                            p.setBeanPath(path);
                            PortalUtils.getHt().save(p);
                        }
                    }
                }
            }

        }
    }

    /**
     * Saves criteria groups in this criteriaModel.
     *
     * @param criteriaModel
     * @param ht
     */
    private static void saveCriteriaGroups(CriteriaModel criteriaModel)
    {
        // each question has a link to its group. the group must first be
        // persisted
        for (CriteriaGroup group : criteriaModel.getCriteriaGroups())
        {
            group.setId(null);
            group.setModel(criteriaModel);
            PortalUtils.getHt().saveOrUpdate(group);

            // loop through question in the group and persist
            for (CriteriaQuestion question : group.getQuestions())
            {
                question.setCriteriaGroup(group);
                saveCriteriaQuestion(question);
            }
        }
    }

    /**
     * Saves the criteria question and all associated entities.
     *
     * @param question
     * @param ht
     */
    private static void saveCriteriaQuestion(CriteriaQuestion question)
    {

        // criteriaAnswer for this question needs to be persisted first
        saveCriteriaAnswer(question.getAnswer());

        // persist question entity
        question.setId(null);
        PortalUtils.getHt().saveOrUpdate(question);

        if (question.getOptionChoices() != null)
        {
            JSONObject optionObject = new JSONObject();
            for (Entry<String, String> choice : question.getOptionChoices()
                    .entrySet())
            {
                try
                {
                    optionObject.append(choice.getKey(), choice.getValue());
                }
                catch (JSONException e)
                {
                }
            }
            // uses the md5 hash of the optionObject to search for existing
            // option choices
            String hash = PortalUtils.getMd5(optionObject.toString());

            List<Integer> optionChoiceList = PortalUtils.getHt().find(
                    "select c.id from CriteriaQuestionJsonOptionChoices c where c.md5Hash LIKE ?",
                    hash);

            if (optionChoiceList.isEmpty())
            {
                CriteriaQuestionJsonOptionChoices optionChoice = new CriteriaQuestionJsonOptionChoices();
                optionChoice.setJsonOptionChoices(optionObject.toString());
                optionChoice.setMd5Hash(hash);
                PortalUtils.getHt().save(optionChoice);
                PortalUtils.getJt().update(
                        "UPDATE search_criteria_question SET jsonOptionChoices="
                                + optionChoice.getId() + " WHERE id="
                                + question.getId());
            }
            else
            {
                PortalUtils.getJt().update(
                        "UPDATE search_criteria_question SET jsonOptionChoices="
                                + optionChoiceList.get(0) + " WHERE id="
                                + question.getId());
            }

        }

        // save question.matrixColumnLabels as a number of
        // CriteriaQuestionMatrixColumnLabel entities with a reference to the
        // question entity they belong to.
        if (question.getMatrixColumnLabels() != null)
        {
            for (String label : question.getMatrixColumnLabels())
            {
                CriteriaQuestionMatrixColumnLabel l = new CriteriaQuestionMatrixColumnLabel();
                l.setCriteriaQuestion(question);
                l.setLabel(label);
                PortalUtils.getHt().save(l);
            }
        }

        // save question.matrixRowLabels as a number of
        // CriteriaQuestionMatrixRowLabel entities with a reference to the
        // question entity they belong to.
        if (question.getMatrixRowLabels() != null)
        {
            for (String label : question.getMatrixRowLabels())
            {
                CriteriaQuestionMatrixRowLabel l = new CriteriaQuestionMatrixRowLabel();
                l.setCriteriaQuestion(question);
                l.setLabel(label);
                PortalUtils.getHt().save(l);
            }
        }

    }

    /**
     * Saves a criteria question's answer. Saving the answer entity involves saving
     * 'valueChoices', 'matrixUIChoices' and 'matrixHQLClauses' as a number of
     * separate entities
     *
     * @param answer
     * @param ht
     */
    private static void saveCriteriaAnswer(CriteriaAnswer answer)
    {
        if (answer != null)
        {
            // the answer entity needs to be saved first.
            answer.setId(null);
            PortalUtils.getHt().saveOrUpdate(answer);

            // save answer.valueChoices as a number of CriteriaAnswerValueChoice
            // entities with a reference to the answer entity they belong to.
            if (answer.getValueChoices() != null)
            {
                for (Entry<String, Boolean> choice : answer.getValueChoices()
                        .entrySet())
                {
                    CriteriaAnswerValueChoice vc = new CriteriaAnswerValueChoice();
                    vc.setAnswer(answer);
                    vc.setChoiceKey(choice.getKey());
                    vc.setChoiceValue(choice.getValue());
                    PortalUtils.getHt().save(vc);
                }
            }

            // save answer.matrixUIChoices as a number of
            // CriteriaAnswerMatrixUIChoice entities with a reference to the
            // answer
            // entity they belong to.
            if (answer.getMatrixUIChoices() != null)
            {
                for (String choice : answer.getMatrixUIChoices())
                {
                    CriteriaAnswerMatrixUIChoice muc = new CriteriaAnswerMatrixUIChoice();
                    muc.setAnswer(answer);
                    muc.setChoice(choice);
                    PortalUtils.getHt().save(muc);
                }
            }

            // save answer.matrixHQLClause as a number of
            // CriteriaAnswerMatrixHQLClause entities with a reference to the
            // answer entity they belong to.
            if (answer.getMatrixHQLClauses() != null)
            {
                for (String clause : answer.getMatrixHQLClauses())
                {
                    CriteriaAnswerMatrixHQLClause mhc = new CriteriaAnswerMatrixHQLClause();
                    mhc.setAnswer(answer);
                    mhc.setClause(clause);
                    PortalUtils.getHt().save(mhc);
                }
            }
        }
    }

    /**
     * SearchModel.massAssignButtons is stored as a list of SearchMassAssignButton
     * entities with the key stored in the massAssignKey field. additionalItems in
     * SearchMassAssignButton is persisted as a SearchMassAssignAdditional entity
     *
     * @param searchModel
     * @param ht
     */
    private static void saveSearchModelMassAssignButtons(SearchModel searchModel)
    {
        for (Entry<String, SearchMassAssignButton> entry : searchModel
                .getMassAssignButtons().entrySet())
        {
            SearchMassAssignButton samba = entry.getValue();
            samba.setId(null);
            samba.setMassAssignKey(entry.getKey());
            samba.setModel(searchModel);
            PortalUtils.getHt().saveOrUpdate(samba);

            for (Entry<String, String> item : samba.getAdditionalItems().entrySet())
            {
                SearchMassAssignAdditional additional = new SearchMassAssignAdditional();
                additional.setMassAssignButton(samba);
                additional.setItemKey(item.getKey());
                additional.setItemValue(String.valueOf(item.getValue()));
                PortalUtils.getHt().save(additional);
            }
        }
    }

    /**
     * SearchModel.attributes is persisted as a number of SearchModelAttribute
     * entities
     *
     * @param searchModel
     * @param ht
     */
    private static void saveSearchModelAttributes(SearchModel searchModel)
    {
        for (Entry<String, String> entry : searchModel.getAttributes().entrySet())
        {
            SearchModelAttribute attribute = new SearchModelAttribute();
            attribute.setModel(searchModel);
            attribute.setAttributeKey(entry.getKey());
            attribute.setAttributeValue(entry.getValue());
            PortalUtils.getHt().save(attribute);
        }
    }

    /**
     * SearchModel.plugins is persisted as a number of SearchModelPlugin entities
     *
     * @param searchModel
     * @param ht
     */
    private static void saveSearchModelPlugins(SearchModel searchModel)
    {
        for (String plugin : searchModel.getPlugins())
        {
            SearchModelPlugin p = new SearchModelPlugin();
            p.setModel(searchModel);
            p.setPlugin(plugin);
            PortalUtils.getHt().save(p);
        }
    }

    /**
     * Save detail entities of a searchModel's master entity
     *
     * @param entity
     * @param ht
     */
    private static void saveEntityRelationships(Entity entity)
    {
        for (Relationship r : entity.getRelationships())
        {
            r.setId(null);
            r.getRelatedEntity().setId(null);
            r.getRelatedEntity().getCriteriaModel().setId(null);
            r.setMasterEntity(entity);

            // persist related entity's criteriaModel
            PortalUtils.getHt().saveOrUpdate(r.getRelatedEntity().getCriteriaModel());
            saveCriteriaGroups(r.getRelatedEntity().getCriteriaModel());
            // save related entity's emailing model
            saveEntityEmailingModel(r.getRelatedEntity().getEmailingModel());
            // persist related entity
            PortalUtils.getHt().saveOrUpdate(r.getRelatedEntity());

            // save related question
            if (r.getRelationQuestion() != null)
            {
                saveCriteriaQuestion(r.getRelationQuestion());
            }

            PortalUtils.getHt().saveOrUpdate(r);
        }
    }

    /**
     * Save SearchModel masterEntity attributes
     *
     * @param entity
     * @param ht
     */
    private static void saveEntityAttributes(Entity entity)
    {
        for (Entry<String, String> entry : entity.getAttributes().entrySet())
        {
            EntityAttribute attr = new EntityAttribute();
            attr.setEntity(entity);
            attr.setAttributeKey(entry.getKey());
            attr.setAttributeValue(entry.getValue());
            PortalUtils.getHt().save(attr);
        }
    }

    /**
     * Sets the searchModel into a usable state by loading all its associated
     * properties and setting all transient properties.
     *
     * @param searchModel
     * @param locale
     * @param jqGridExcelExportTask
     * @return searchModel
     */
    public static SearchModel loadSearchModel(SearchModel searchModel,
            String locale, JQGridExcelExportTask exportTask)
    {
        // load the searchModel's massAssign buttons, attributes and plugins
        loadSearchModelMassAssignButtons(searchModel);

        loadSearchModelAttributes(searchModel);

        loadSearchModelPlugins(searchModel);

        // the search model's entity has a number of associations and transients
        // that need to be loaded from the database. all these operations are
        // encapsulated in this function.
        loadEntityAssociations(searchModel, locale);

        return searchModel;
    }

    /**
     * Loads associated entities of the search model's master entity
     *
     * @param searchModel
     * @param locale
     * @param ht
     */
    private static void loadEntityAssociations(SearchModel searchModel,
            String locale)
    {
        if (searchModel != null)
        {
            Entity entity = searchModel.getMasterEntity();

            // load master entity relationships
            List<Relationship> relationships = PortalUtils.getHt()
                    .find("from Relationship r where r.masterEntity=?", entity);

            for (Relationship r : relationships)
            {
                // load related entity attributes, emailing model and criteria
                // model
                loadEntityAttributes(r.getRelatedEntity());
                loadEntityEmailingModel(r.getRelatedEntity().getEmailingModel());
                loadEntityCriteriaModel(r.getRelatedEntity().getCriteriaModel(),
                        locale);
                populateEntityChoices(r.getRelatedEntity());
            }

            entity.setRelationships(relationships);

            // load master entity attributes, emailing model and criteria model
            loadEntityAttributes(entity);
            loadEntityEmailingModel(entity.getEmailingModel());
            loadEntityCriteriaModel(entity.getCriteriaModel(), locale);
            populateEntityChoices(entity);
        }
    }

    /**
     * Load and populate criteria model
     *
     * @param criteriaModel
     * @param locale
     * @param ht
     */
    private static void loadEntityCriteriaModel(CriteriaModel criteriaModel,
            String locale)
    {
        // load criteria groups
        List<CriteriaGroup> groups = PortalUtils.getHt()
                .find("from CriteriaGroup g where g.model=?", criteriaModel);
        for (CriteriaGroup g : groups)
        {
            // iterate over groups and load questions and associated entities
            List<CriteriaQuestion> questions = PortalUtils.getHt()
                    .find("from CriteriaQuestion q where q.criteriaGroup=?", g);
            for (CriteriaQuestion q : questions)
            {
                // load question answer associations
                if (CriteriaQuestion.TYPE_CHOICE_MULTI.equals(q.getType())
                        || CriteriaQuestion.TYPE_CHOICE_SINGLE.equals(q.getType())
                        || CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType())
                        || CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType()))
                {
                    SearchHelper.loadCriteriaAnswerAssociations(q.getAnswer());
                }
                if (locale != null)
                {
                    q.setLocale(locale);
                }

                g.getQuestions().add(q);
            }
        }

        criteriaModel.setCriteriaGroups(groups);
    }

    public static void populateEntityChoices(Entity entity)
    {
        for (CriteriaQuestion q : entity.getCriteriaModel().getCriteriaQuestions())
        {
            if (q.getId() != null)
            {
                // load option choices
                if (CriteriaQuestion.TYPE_CHOICE_MULTI.equals(q.getType())
                        || CriteriaQuestion.TYPE_CHOICE_SINGLE.equals(q.getType()))
                {
                    SearchHelper.loadQuestionOptionChoices(q);
                }
                if (CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType())
                        || CriteriaQuestion.TYPE_MATRIX_MULTI.equals(q.getType()))
                {
                    // load column labels
                    SearchHelper.loadQuestionMatrixColumnLabels(q);
                    // load row labels
                    SearchHelper.loadQuestionMatrixRowLabels(q);
                }
            }
        }
    }

    /**
     * Load transients for answer
     *
     * @param answer
     * @param ht
     */
    public static void loadCriteriaAnswerAssociations(CriteriaAnswer answer)
    {
        if (answer != null)
        {
            List<Object[]> choices = PortalUtils.getHt().find(
                    "select c.choiceKey, c.choiceValue from CriteriaAnswerValueChoice c where c.answer=?",
                    answer);
            answer.setValueChoices(new LinkedHashMap<String, Boolean>());
            for (Object[] o : choices)
            {
                answer.getValueChoices().put((String) o[0], (Boolean) o[1]);
            }

            List uiChoices = PortalUtils.getHt().find(
                    "select c.choice from CriteriaAnswerMatrixUIChoice c where c.answer=?",
                    answer);
            if (!uiChoices.isEmpty())
            {
                answer.setMatrixUIChoices(uiChoices);
            }

            List hqlChoices = PortalUtils.getHt().find(
                    "select c.clause from CriteriaAnswerMatrixHQLClause c where c.answer=?",
                    answer);
            if (!hqlChoices.isEmpty())
            {
                answer.setMatrixHQLClauses(hqlChoices);
            }
        }
    }

    /**
     * Load column labels for this question
     *
     * @param q
     * @param ht
     */
    public static void loadQuestionMatrixRowLabels(CriteriaQuestion q)
    {
        q.setMatrixRowLabels(PortalUtils.getHt().find(
                "select l.label from CriteriaQuestionMatrixRowLabel l where l.criteriaQuestion=?",
                q));
    }

    /**
     * Load column labels for this question
     *
     * @param q
     * @param ht
     */
    public static void loadQuestionMatrixColumnLabels(CriteriaQuestion q)
    {
        q.setMatrixColumnLabels((PortalUtils.getHt().find(
                "select l.label from CriteriaQuestionMatrixColumnLabel l where l.criteriaQuestion=?",
                q)));
    }

    /**
     * Load option choices belonging to this question
     *
     * @param q
     * @param ht
     */
    public static void loadQuestionOptionChoices(CriteriaQuestion q)
    {
        if (q.getJsonOptionChoices() != null)
        {
            String optionChoices = q.getJsonOptionChoices().getJsonOptionChoices();
            JSONObject jsonObject = new JSONObject();
            Map<String, Object> optionChoicesMap = new LinkedHashMap<>();
            try
            {
                jsonObject = new JSONObject(optionChoices);
                optionChoicesMap = JSONUtils.deserializeToMap(jsonObject);
            }
            catch (Exception e1)
            {
            }

            optionChoicesMap = CollectionUtils.sortMapByValue(optionChoicesMap);
            q.setOptionChoices(new LinkedHashMap<String, String>());

            for (Map.Entry<String, Object> a : optionChoicesMap.entrySet())
            {
                if (a.getKey() != null && a.getValue() != null)
                {
                    try
                    {
                        q.getOptionChoices().put(a.getKey(), JSONUtils
                                .toStringList((JSONArray) a.getValue()).get(0));
                    }
                    catch (Exception e)
                    {
                    }
                }
            }
        }
    }

    /**
     * Load emailing model belonging to this entity.
     *
     * @param emailingModel
     * @param ht
     */
    private static void loadEntityEmailingModel(EmailingModel emailingModel)
    {
        // load emailing emailer for emailing model. we need to load a list of
        // emailBeanPaths for each emailer.
        List<EmailingEmailer> emailers = PortalUtils.getHt()
                .find("from EmailingEmailer e where e.model=?", emailingModel);
        for (EmailingEmailer e : emailers)
        {
            e.setEmailBeanPaths(new ArrayList<String>());
            List beanPaths = PortalUtils.getHt().find(
                    "select p.beanPath from EmailerEmailBeanPath p where p.emailer=?",
                    e);
            if (!beanPaths.isEmpty())
            {
                e.getEmailBeanPaths().addAll(beanPaths);
            }
            emailingModel.getEmailers().add(e);
        }
    }

    /**
     * Load attributes for this entity
     *
     * @param entity
     * @param ht
     */
    private static void loadEntityAttributes(Entity entity)
    {
        List<Object[]> attr = PortalUtils.getHt().find(
                "select a.attributeKey, a.attributeValue from EntityAttribute a where a.entity=?",
                entity);
        for (Object[] a : attr)
        {
            entity.getAttributes().put((String) a[0],
                    a[1] != null ? (String) a[1] : "");
        }
    }

    /**
     * Load the search model's attributes
     *
     * @param searchModel
     * @param ht
     */
    private static void loadSearchModelAttributes(SearchModel searchModel)
    {
        List<Object[]> attr = PortalUtils.getHt().find(
                "select a.attributeKey, a.attributeValue from SearchModelAttribute a where a.model=?",
                searchModel);
        for (Object[] a : attr)
        {
            searchModel.getAttributes().put(a[0].toString(),
                    a[1] != null ? a[1].toString() : "");

        }
    }

    /**
     * Load the search model's plugins
     *
     * @param searchModel
     * @param ht
     */
    private static void loadSearchModelPlugins(SearchModel searchModel)
    {
        List<String> plugins = PortalUtils.getHt().find(
                "select p.plugin from SearchModelPlugin p where p.model=?",
                searchModel);
        for (String p : plugins)
        {
            searchModel.getPlugins().add(p);
        }
    }

    /**
     * Load a search model's mass assign buttons
     *
     * @param searchModel
     * @param ht
     */
    private static void loadSearchModelMassAssignButtons(SearchModel searchModel)
    {
        List<SearchMassAssignButton> mabs = PortalUtils.getHt().find(
                "from SearchMassAssignButton ma where ma.model=?", searchModel);

        // each mass assign button has additional attribute that have to be
        // loaded as well
        for (SearchMassAssignButton mab : mabs)
        {
            List<Object[]> additional = PortalUtils.getHt().find(
                    "select ma.itemKey, ma.itemValue from SearchMassAssignAdditional ma where ma.massAssignButton=?",
                    mab);
            for (Object[] o : additional)
            {
                mab.getAdditionalItems().put(o[0].toString(), o[1].toString());
            }

            searchModel.getMassAssignButtons().put(mab.getMassAssignKey(), mab);
        }
    }

    public static String getCriteriaQuestionType(int dfQuestionType)
    {
        String ct = CriteriaQuestion.TYPE_TEXT;

        if (DFQuestion.TYPE_BOOLEAN == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_BOOLEAN;
        }
        else if (DFQuestion.TYPE_DATE == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_DATE;
        }
        else if (DFQuestion.TYPE_INTEGER == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_NUMBER;
        }
        else if (DFQuestion.TYPE_FLOAT == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_FLOAT;
        }
        else if (DFQuestion.TYPE_SINGLE_CHOICE == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_CHOICE_SINGLE;
        }
        else if (DFQuestion.TYPE_MULTI_CHOICE == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_CHOICE_MULTI;
        }
        else if (DFQuestion.TYPE_MATRIX_SINGLE == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_MATRIX_SINGLE;
        }
        else if (DFQuestion.TYPE_MATRIX_MULTI == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_MATRIX_MULTI;
        }
        else if (DFQuestion.TYPE_FILE_UPLOAD == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_FILE_UPLOAD;
        }
        else if (DFQuestion.TYPE_TREE == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_TREE;
        }
        else if (DFQuestion.TYPE_RATING == dfQuestionType)
        {
            ct = CriteriaQuestion.TYPE_RATING_RANGE;
        }

        return ct;
    }

    public static Map<String, CriteriaPlugin> getCriteriaPluginsMap(
            SearchModel searchModel)
    {
        Map<String, CriteriaPlugin> criteriaPlugins = new HashMap<>();

        for (CriteriaGroup cg : searchModel.getMasterEntity().getCriteriaModel()
                .getCriteriaGroups())
        {
            if (null != cg.getCriteriaPluginClass())
            {
                CriteriaPlugin plugin = createCriteriaPlugin(
                        cg.getCriteriaPluginClass(), searchModel);

                if (plugin != null)
                {
                    criteriaPlugins.put(plugin.getCriteriaPluginKey(), plugin);
                }
            }
        }

        for (Relationship r : searchModel.getMasterEntity().getRelationships())
        {
            for (CriteriaGroup cg : r.getRelatedEntity().getCriteriaModel()
                    .getCriteriaGroups())
            {
                if (null != cg.getCriteriaPluginClass())
                {
                    CriteriaPlugin plugin = createCriteriaPlugin(
                            cg.getCriteriaPluginClass(), searchModel);

                    if (plugin != null)
                    {
                        criteriaPlugins.put(plugin.getCriteriaPluginKey(), plugin);
                    }
                }
            }
        }

        return criteriaPlugins;
    }

    private static CriteriaPlugin createCriteriaPlugin(
            Class<? extends CriteriaPlugin> criteriaPluginClass,
            SearchModel searchModel)
    {
        CriteriaPlugin plugin = null;

        try
        {
            plugin = criteriaPluginClass.newInstance();
            plugin.setSearchModel(searchModel);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return plugin;
    }

    public static CriteriaQuestion getCriteriaQuestion(CriteriaModel criteriaModel,
            String questionKey)
    {
        CriteriaQuestion ret = null;

        for (CriteriaQuestion q : criteriaModel.getCriteriaQuestions())
        {
            if (q.getQuestionKey().equals(questionKey))
            {
                ret = q;
                break;
            }
        }
        return ret;
    }

    public static String buildWhereHQL_Rating_Range_SkillsProfile(
            CriteriaQuestion q, String tablePrefix)
    {
        StringBuilder hql = new StringBuilder();

        if (q.isAnswered())
        {
            final String ANSWER_FIELD = "cast(" + tablePrefix + ".answer as int)";
            final String RATING_CAST = "cast(%1s as int)";
            CriteriaAnswer a = q.getAnswer();

            if (!StringUtils.isEmpty(a.getRatingFromValue().trim())
                    && !StringUtils.isEmpty(a.getRatingToValue().trim()))
            {
                hql.append(" and (");
                hql.append(ANSWER_FIELD).append(" >= ").append(
                        String.format(RATING_CAST, a.getRatingFromValue().trim()));
                hql.append(" and ");
                hql.append(ANSWER_FIELD).append(" <= ").append(
                        String.format(RATING_CAST, a.getRatingToValue().trim()));
                hql.append(") ");
            }
            else if (!StringUtils.isEmpty(a.getRatingFromValue().trim()))
            {
                hql.append(" and (");
                hql.append(ANSWER_FIELD).append(" >= ").append(
                        String.format(RATING_CAST, a.getRatingFromValue().trim()));
                hql.append(") ");
            }
            else if (!StringUtils.isEmpty(a.getRatingToValue()))
            {
                hql.append(" and (");
                hql.append(ANSWER_FIELD).append(" <= ").append(
                        String.format(RATING_CAST, a.getRatingToValue().trim()));
                hql.append(") ");
            }
        }

        return hql.toString();
    }

    public static String buildWhereHQL_Choice_SkillsProfile(CriteriaQuestion q,
            String tablePrefix, int questionFormat)
    {
        StringBuilder hql = new StringBuilder("");
        CriteriaAnswer a = q.getAnswer();
        String oneLang = " %s.answer like '%%%s%%'";
        String twoLang = "    (${alias}.answer like '%${l1}%' OR ${alias}.answer like '%${l2}%') ";
        if (SkillsQuestion.Format.RATED_NAMED_VALUES.getValue() == questionFormat)
        {
            oneLang = " %s.answer like '%s'";
            twoLang = "    (${alias}.answer like '${l1}' OR ${alias}.answer like '${l2}') ";

        }

        if (q.isAnswered() && a.getValueChoices() != null
                && !a.getValueChoices().isEmpty()
                && !StringUtils.isEmpty(a.getAndOrValue()))
        {

            List<String> clauses = new ArrayList<>();
            for (Entry<String, Boolean> entry : a.getValueChoices().entrySet())
            {
                if (entry.getValue())
                {
                    if (entry.getKey().contains("|"))
                    {

                        String[] searchTerms = entry.getKey().split("\\|");
                        Map<String, String> map = ImmutableMap.of("alias",
                                tablePrefix, "l1", searchTerms[0], "l2",
                                searchTerms[1]);

                        clauses.add(new StrSubstitutor(map).replace(twoLang));
                    }
                    else
                    {
                        clauses.add(String.format(oneLang, tablePrefix,
                                entry.getKey()));
                    }
                }

            }
            hql.append(" AND ");
            String andOrNotValue = (a.isOrOperation() && !a.isOrNotOperation())
                    || a.isAndNotOperation() ? " OR " : " AND ";
            hql.append("(").append(Joiner.on(andOrNotValue).join(clauses))
                    .append(")");
        }
        return hql.toString();
    }

    public static List<NameValuePair> getShareLevelOptions(Locale locale)
    {
        List<NameValuePair> shareLevelOptions = new ArrayList<NameValuePair>();
        shareLevelOptions.add(new NameValuePair(PortalUtils
                .getI18nMessage("i18n.jqGridSearch.shareLevel.0", locale), "0"));
        shareLevelOptions.add(new NameValuePair(PortalUtils
                .getI18nMessage("i18n.jqGridSearch.shareLevel.1", locale), "1"));
        return shareLevelOptions;
    }

    public static String translateSearchType(NameConversionParameters params)
    {
        String ret = "Unknown";

        if (params.getKey() != null)
        {
            ret = params.getKey().toString();
            if (ret.startsWith("i18n"))
            {
                ret = new I18nLabel(ret, params.getLocale()).getTranslation();
            }
        }

        return ret;
    }

    public static String translateShareLevel(NameConversionParameters params)
    {
        Object key = params.getKey();
        Locale locale = params.getLocale();

        String ret = "Unknown";
        if (key instanceof Integer shareLevel && locale != null)
        {
            I18nLabel label = new I18nLabel();
            label.setLocale(locale);
            if (shareLevel == JQGridSearch.SHARE_LEVEL_MY_SEARCH)
            {
                label.setI18nCode("i18n.jqGridSearch.shareLevel.0");
            }
            else if (shareLevel == JQGridSearch.SHARE_LEVEL_COMMON_SEARCH)
            {
                label.setI18nCode("i18n.jqGridSearch.shareLevel.1");
            }
            ret = label.getTranslation();
        }

        return ret;
    }

    public static String translateNumbersToWords(JQGridSearch jqSearch,
            String hqlKey, Object rawPropertyValue, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String cellValue = null;

        if (!(jqSearch == null || jqSearch.getEntity() == null || hqlKey == null))
        {
            if (hqlKey.equals("ssp.answer"))
            {
                if (rawPropertyValue.toString().equals(""))
                {
                    cellValue = messageSource.getMessage(
                            "i18n.SearchHelper.NothingSelected", null, locale);
                }
                else
                {
                    cellValue = rawPropertyValue.toString().replace("\r\n", " | ");
                }
            }
            if (hqlKey.equals("ssp.question.format"))
            {
                int format = (Integer.valueOf(rawPropertyValue.toString())).intValue();
                if (format == SkillsQuestion.Format.RATING.getValue())
                {
                    cellValue = messageSource.getMessage("i18n.SearchHelper.Rating",
                            null, locale);
                }
                else if (format == SkillsQuestion.Format.NAMED_VALUES.getValue())
                {
                    cellValue = messageSource.getMessage(
                            "i18n.SearchHelper.SingleSelectList", null, locale);
                }
                else if (format == SkillsQuestion.Format.PICK_LIST.getValue())
                {
                    cellValue = messageSource.getMessage(
                            "i18n.SearchHelper.MultiSelectList", null, locale);
                }
                else if (format == SkillsQuestion.Format.RATED_NAMED_VALUES
                        .getValue())
                {
                    cellValue = messageSource.getMessage(
                            "i18n.SearchHelper.RatedNamedValues", null, locale);
                }
            }
        }

        return cellValue;
    }

    public static Map<String, String> getSkillsQuestionChoices(String choices)
    {
        return getSkillsQuestionChoices(choices, null);
    }

    public static Map<String, String> getSkillsQuestionChoices(String choices,
            Integer questionFormat)
    {
        List<String> choicesList = StringUtils.listify(choices, "\r\n");
        List<String> choicesListValues = new ArrayList<>();
        List<String> choicesListKeys = new ArrayList<>();
        if ((questionFormat != null) && (questionFormat
                .intValue() == SkillsQuestion.Format.RATED_NAMED_VALUES.getValue()))
        {
            for (String choice : choicesList)
            {
                String[] split = choice.split("\\|");
                if (split != null && split.length == 3)
                {

                    choicesListKeys.add(split[2]);
                    choicesListValues.add(split[0]);
                }
                else if (split != null)
                {
                    choicesListKeys.add(split[1]);
                    choicesListValues.add(split[0]);

                }
            }
        }
        else
        {
            choicesListValues = choicesList;
            choicesListKeys = choicesList;
        }

        return StringUtils.mapify(choicesListKeys, choicesListValues);
    }

    public static void validateAnswers(SearchModel searchModel, Locale locale)
    {
        List<CriteriaQuestion> qq = getAnsweredQuestions(searchModel);

        searchModel.setErrorMessages(new I18nMessageList());
        validateAnswerByQuestionType(searchModel, locale, qq);
    }

    public static void validateAnswerByQuestionType(SearchModel searchModel,
            Locale locale, List<CriteriaQuestion> qq)
    {
        for (CriteriaQuestion criteriaQuestion : qq)
        {
            switch (criteriaQuestion.getType())
            {
                case CriteriaQuestion.TYPE_DATE:
                    validateDate(criteriaQuestion, searchModel, locale);
                    break;

                default:
                    break;
            }
        }
    }

    public static List<CriteriaQuestion> getAnsweredQuestions(
            SearchModel searchModel)
    {
        List<CriteriaQuestion> qq = new ArrayList<>();
        if (searchModel != null && searchModel.getMasterEntity() != null
                && searchModel.getMasterEntity().getCriteriaModel() != null)
        {
            qq = searchModel.getMasterEntity().getCriteriaModel()
                    .getCriteriaQuestions().stream().filter(Objects::nonNull)
                    .filter(q -> q.isAnswered()).collect(Collectors.toList());
            for (Relationship r : searchModel.getMasterEntity().getRelationships())
            {
                if (r != null && r.getRelatedEntity() != null
                        && r.getRelatedEntity().getCriteriaModel() != null)
                {
                    List<CriteriaQuestion> q2 = r.getRelatedEntity()
                            .getCriteriaModel().getCriteriaQuestions().stream()
                            .filter(Objects::nonNull).filter(q -> q.isAnswered())
                            .collect(Collectors.toList());
                    qq.addAll(q2);
                    Collectors.toList();
                }
            }
        }
        return qq;
    }

    public static void validateDate(CriteriaQuestion criteriaQuestion,
            SearchModel searchModel, Locale locale)
    {
        Date from = null;
        Date to = null;

        String fromValue = criteriaQuestion.getAnswer().getFromValue();
        String toValue = criteriaQuestion.getAnswer().getToValue();
        String format = criteriaQuestion.getDateFormatForCriteria();
        if (!StringUtils.isEmpty(fromValue) && !StringUtils.isEmpty(toValue)
                && !StringUtils.isEmpty(format))
        {
            from = DateUtils.parseDate(fromValue, format, null);
            to = DateUtils.parseDate(toValue, format, null);

            if (from != null && to != null && to.before(from))
            {
                searchModel.getErrorMessages()
                        .add(new I18nLabel(
                                "i18n.SearchSubController.toDateBeforeFromDate",
                                criteriaQuestion.getQuestionText()));
            }

        }

    }

    /**
     * Converts the question and answer into a where fragment that can be used to
     * filter records. The answer defaults to 0 if the value isn't a number.
     *
     * @param q
     *            The count question
     * @param alias
     *            The property that is being counted
     * @return A query fragment that can be appended to a query to filter the
     *         results
     *
     * @see PCHelper#getPCProjectCriteria
     */
    public static String getCountQueryFragment(CriteriaQuestion q, String alias)
    {
        alias = StringUtils.isEmpty(alias) ? " count(*)" : " count(" + alias + ")";

        QueryBuilder qb = new QueryBuilder();

        String fromValue = StringUtils.trim(q.getAnswer().getFromValue());

        String toValue = StringUtils.trim(q.getAnswer().getToValue());

        if (StringUtils.isEmpty(fromValue, toValue))
        {
        }
        else if (StringUtils.isEmpty(toValue))
        {
            qb.append(alias);

            qb.appendIf(CriteriaQuestion.NUMBER_OPERATION_EQUALS
                    .equals(q.getNumberOperation()), " = ");

            qb.elseAppendIf(CriteriaQuestion.NUMBER_OPERATION_LESS_THAN
                    .equals(q.getNumberOperation()), " < ");

            qb.elseAppendIf(CriteriaQuestion.NUMBER_OPERATION_LESS_THAN_EQUALS
                    .equals(q.getNumberOperation()), " <= ");

            qb.elseAppendIf(CriteriaQuestion.NUMBER_OPERATION_GREATER_THAN
                    .equals(q.getNumberOperation()), " > ");

            qb.elseAppend(" >= ");

            qb.append(NumberUtils.asInteger(fromValue, 0));
        }
        else if (StringUtils.isEmpty(fromValue))
        {
            qb.append(alias).append(" <= ")
                    .append(NumberUtils.asInteger(toValue, 0));
        }
        else
        {
            qb.append(alias).append(" >= ")
                    .append(NumberUtils.asInteger(fromValue, 0));
            qb.append(" and ").append(alias).append(" <= ")
                    .append(NumberUtils.asInteger(toValue, 0));
        }

        return qb.getStr();
    }

    public static void populateReturnForm(SearchModel searchModel, SiteElement se,
            String action, String subAction, UserDetailsImpl userLoggedIn,
            String... additionalInputHtml)
    {
        CsrfToken csrfToken = CsrfTokenHolder.getCsrfToken();
        String csrfInput = CsrfUtils.renderHiddenInput(csrfToken);
        searchModel.setAttribute("returnForm", String.format(
                "<form id='returnForm'%s method='POST'>%s%s%s</form>", csrfInput,
                se != null ? String.format(" action='%s.htm'", se.getFullPath())
                        : "",
                ActionHelper.getEncryptedActionInputHtml("displayHome", null,
                        userLoggedIn),
                additionalInputHtml != null
                        ? StringUtils.join(additionalInputHtml, "")
                        : ""));
    }

    public static void populateReturnForm(SearchModel searchModel, SiteElement se,
            String action, UserDetailsImpl userLoggedIn,
            String... additionalInputHtml)
    {
        populateReturnForm(searchModel, se, action, null, userLoggedIn);
    }

    public static void populateReturnForm(SearchModel searchModel, SiteElement se,
            UserDetailsImpl userLoggedIn, String... additionalInputHtml)
    {
        populateReturnForm(searchModel, se, "displayHome", null, userLoggedIn);
    }

    public static List<Integer> getSelectedIds(HttpServletRequest request)
    {
        List<Integer> selectedIds = null;
        try
        {
            selectedIds = CollectionUtils
                    .convertListStringsToIntegers(ArrayUtils.JSONArrayToList(
                            new JSONArray(request.getParameter("selectedIds"))));
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        return selectedIds;
    }

    public static String getDateClause(String dateString, String dateFormat)
    {
        String ret = null;
        if (StringUtils.isNotEmpty(dateString))
        {
            Date date = DateUtils.parseDate(dateString, dateFormat);
            ret = DateUtils.getDateClause(date);
        }
        return ret;
    }

    public static JQGridModel prepareJQGridModelForDisplay(SearchModel searchModel, HttpServletRequest request){
        Locale locale = PortalUtils.getLocale(request);
        JQGridModel gridModel = JQGridHelper.getJQGridModelFromSession(request);

        if (gridModel == null || gridModel.getMaster() == null
                || StringUtils.isEmpty(gridModel.getMaster().getSearchModelFile()))
        {
            gridModel = SearchHelper.createJQGridModel(searchModel, locale);
        }
        else
        {
            SearchHelper.resetJQGridModel(gridModel, searchModel, locale);
        }
        return gridModel;
    }
}
