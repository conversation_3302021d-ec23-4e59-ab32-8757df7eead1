package com.orbis.search;

import com.orbis.test.ApplicationContextTestCaseWithStaticSetup;
import com.orbis.web.content.acrm.AcrmSearchInterfaceTest;
import com.orbis.web.content.cc.CCSearchInterfaceTest;
import com.orbis.web.content.coop.CoopSearchInterfaceTest;
import com.orbis.web.content.cr.CRSearchInterfaceTest;
import com.orbis.web.content.doc.DocControllerSearchInterfaceTest;
import com.orbis.web.content.ecommerce.EcommerceSearchInterfaceTest;
import com.orbis.web.content.education.EducationSearchInterfaceTest;
import com.orbis.web.content.efr.EFRAdminSearchInterfaceTest;
import com.orbis.web.content.ev.web.globalevents.GlobalEventsSearchInterfaceTest;
import com.orbis.web.content.fp.FPControllerSearchInterfaceTest;
import com.orbis.web.content.grad.GradControllerSearchInterfaceTest;
import com.orbis.web.content.integration.IntegrationImportSearchInterfaceTest;
import com.orbis.web.content.interaction.InteractionControllerSearchInterfaceTest;
import com.orbis.web.content.interview.InterviewSearchInterfaceTest;
import com.orbis.web.content.is.InfoSessionSearchInterfaceTest;
import com.orbis.web.content.mentorship2.Mentorship2SearchInterfaceTest;
import com.orbis.web.content.na.NAControllerSearchInterfaceTest;
import com.orbis.web.content.nes.admin.NESAdminSearchInterfaceTest;
import com.orbis.web.content.os.OSControllerSearchInterfaceTest;
import com.orbis.web.content.pm.PMSearchInterfaceTest;
import com.orbis.web.content.portal.PortalToolsSearchInterfaceTest;
import com.orbis.web.content.pt.PTControllerSearchInterfaceTest;
import com.orbis.web.content.rb.ResumeBookControllerSearchInterfaceTest;
import com.orbis.web.content.res.ResControllerSearchInterfaceTest;
import com.orbis.web.content.sa.SASearchInterfaceTest;
import com.orbis.web.content.st.STSearchInterfaceTest;
import com.orbis.web.content.sur.SurveySearchInterfaceTest;

public class SearchInterfaceTestExecutor
{
    public static void main(String[] args)
    {
        new ApplicationContextTestCaseWithStaticSetup(AcrmSearchInterfaceTest.class,
                CCSearchInterfaceTest.class, CRSearchInterfaceTest.class,
                CoopSearchInterfaceTest.class,
                DocControllerSearchInterfaceTest.class,
                EFRAdminSearchInterfaceTest.class,
                EcommerceSearchInterfaceTest.class,
                EducationSearchInterfaceTest.class,
                FPControllerSearchInterfaceTest.class,
                GlobalEventsSearchInterfaceTest.class,
                GradControllerSearchInterfaceTest.class,
                InfoSessionSearchInterfaceTest.class,
                IntegrationImportSearchInterfaceTest.class,
                InteractionControllerSearchInterfaceTest.class,
                InterviewSearchInterfaceTest.class,
                Mentorship2SearchInterfaceTest.class,
                NAControllerSearchInterfaceTest.class,
                NESAdminSearchInterfaceTest.class,
                NPostingControllerSearchInterfaceTest.class,
                OSControllerSearchInterfaceTest.class, PMSearchInterfaceTest.class,
                PTControllerSearchInterfaceTest.class,
                PortalToolsSearchInterfaceTest.class,
                ResControllerSearchInterfaceTest.class,
                ResumeBookControllerSearchInterfaceTest.class,
                SASearchInterfaceTest.class, STSearchInterfaceTest.class,
                SurveySearchInterfaceTest.class).runTests();
    }

}
