package com.orbis.web.content.dashboard;

import java.util.Map;

/**
 * Data structure representing a parsed user intent from AI processing
 */
public class ParsedIntent
{
    private final String intent;
    private final Map<String, Object> arguments;

    /**
     * Constructor for ParsedIntent
     * @param intent The identified intent name
     * @param arguments Map of extracted arguments
     */
    public ParsedIntent(String intent, Map<String, Object> arguments)
    {
        this.intent = intent;
        this.arguments = arguments;
    }

    /**
     * Gets the intent name
     * @return The intent name
     */
    public String getIntent()
    {
        return intent;
    }

    /**
     * Gets the extracted arguments
     * @return Map of arguments
     */
    public Map<String, Object> getArguments()
    {
        return arguments;
    }
}
