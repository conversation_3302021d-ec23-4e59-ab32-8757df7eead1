package com.orbis.web.content.dashboard;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.orbis.utils.StringUtils;

/**
 * Parser for AI-generated intent responses with security validation
 */
public class IntentParser
{
    private static final Logger log = LoggerFactory.getLogger(IntentParser.class);

    // Whitelist of allowed intents for security
    private static final Set<String> ALLOWED_INTENTS = new HashSet<>();
    static {
        ALLOWED_INTENTS.add("getUpcomingEvents");
        // Add more allowed intents here as needed
    }

    // Maximum length for parsed values to prevent memory issues
    private static final int MAX_VALUE_LENGTH = 100;

    /**
     * Parses AI response into structured intent data with security validation
     * @param response The AI response to parse
     * @return ParsedIntent object with validated data
     * @throws IllegalArgumentException if parsing fails or intent is invalid
     */
    public static ParsedIntent parse(String response) throws IllegalArgumentException
    {
        if (StringUtils.isEmpty(response))
        {
            throw new IllegalArgumentException("Response cannot be empty");
        }

        Map<String, Object> arguments = new HashMap<>();
        String intent = null;

        // Parse the response to extract the intent and arguments
        String[] parts = response.split("\n");

        for (String part : parts)
        {
            if (StringUtils.isEmpty(part))
            {
                continue;
            }

            String[] keyValue = part.trim().split(": ", 2);
            if (keyValue.length == 2)
            {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();

                // Validate value length
                if (value.length() > MAX_VALUE_LENGTH)
                {
                    log.warn("Value too long for key {}, truncating", key);
                    value = value.substring(0, MAX_VALUE_LENGTH);
                }

                if (key.equalsIgnoreCase("Intent Name"))
                {
                    intent = sanitizeIntent(value);
                }
                else
                {
                    // Sanitize argument keys and values
                    String sanitizedKey = sanitizeArgumentKey(key);
                    String sanitizedValue = sanitizeArgumentValue(value);
                    arguments.put(sanitizedKey, sanitizedValue);
                }
            }
        }

        if (StringUtils.isEmpty(intent))
        {
            throw new IllegalArgumentException("No intent found in response");
        }

        // Validate intent against whitelist
        if (!ALLOWED_INTENTS.contains(intent))
        {
            log.warn("Attempted to use unauthorized intent: {}", intent);
            throw new IllegalArgumentException("Intent not authorized: " + intent);
        }

        log.info("Successfully parsed intent: {} with {} arguments", intent, arguments.size());
        return new ParsedIntent(intent, arguments);
    }

    /**
     * Sanitizes intent name
     */
    private static String sanitizeIntent(String intent)
    {
        if (StringUtils.isEmpty(intent))
        {
            return null;
        }

        // Remove any non-alphanumeric characters except underscores
        return intent.replaceAll("[^a-zA-Z0-9_]", "").trim();
    }

    /**
     * Sanitizes argument keys
     */
    private static String sanitizeArgumentKey(String key)
    {
        if (StringUtils.isEmpty(key))
        {
            return "unknown";
        }

        // Remove any non-alphanumeric characters except underscores
        return key.replaceAll("[^a-zA-Z0-9_]", "").trim();
    }

    /**
     * Sanitizes argument values
     */
    private static String sanitizeArgumentValue(String value)
    {
        if (StringUtils.isEmpty(value) || "null".equalsIgnoreCase(value))
        {
            return null;
        }

        // Remove control characters and trim
        return value.replaceAll("[\u0000-\u001F\u007F]", "").trim();
    }
}
