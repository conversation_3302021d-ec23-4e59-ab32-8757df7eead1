package com.orbis.web.content.dashboard;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.utils.StringUtils;

@Service
public class OpenAIService
{
    private static final Logger log = LoggerFactory.getLogger(OpenAIService.class);

    // Configuration keys
    private static final String OPENAI_API_KEY_CONFIG = "OPENAI_API_KEY";
    private static final String OPENAI_API_URL_CONFIG = "OPENAI_API_URL";
    private static final String OPENAI_MAX_TOKENS_CONFIG = "OPENAI_MAX_TOKENS";
    private static final String OPENAI_TEMPERATURE_CONFIG = "OPENAI_TEMPERATURE";

    // Default values
    private static final String DEFAULT_API_URL = "https://api.openai.com/v1/chat/completions";
    private static final int DEFAULT_MAX_TOKENS = 500;
    private static final double DEFAULT_TEMPERATURE = 0.7;
    private static final int MAX_PROMPT_LENGTH = 2000;

    // Input validation patterns
    private static final Pattern SAFE_PROMPT_PATTERN = Pattern.compile("^[\\p{L}\\p{N}\\p{P}\\p{Z}\\s]*$");
    private static final Pattern MALICIOUS_PATTERN = Pattern.compile("(?i)(script|javascript|<|>|eval|function|alert|prompt|confirm)", Pattern.CASE_INSENSITIVE);

    private final OkHttpClient client = new OkHttpClient().newBuilder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * Validates and sanitizes user input prompt
     * @param prompt User input to validate
     * @return Sanitized prompt
     * @throws IllegalArgumentException if prompt is invalid
     */
    private String validateAndSanitizePrompt(String prompt) throws IllegalArgumentException
    {
        if (StringUtils.isEmpty(prompt))
        {
            throw new IllegalArgumentException("Prompt cannot be empty");
        }

        // Check length
        if (prompt.length() > MAX_PROMPT_LENGTH)
        {
            throw new IllegalArgumentException("Prompt exceeds maximum length of " + MAX_PROMPT_LENGTH + " characters");
        }

        // Check for malicious patterns
        if (MALICIOUS_PATTERN.matcher(prompt).find())
        {
            log.warn("Potentially malicious prompt detected and blocked");
            throw new IllegalArgumentException("Prompt contains potentially unsafe content");
        }

        // Validate character set
        if (!SAFE_PROMPT_PATTERN.matcher(prompt).matches())
        {
            throw new IllegalArgumentException("Prompt contains invalid characters");
        }

        // Sanitize by escaping quotes and removing control characters
        return prompt.replaceAll("[\u0000-\u001F\u007F]", "")
                    .replace("\"", "\\\"")
                    .replace("\\", "\\\\")
                    .trim();
    }

    /**
     * Gets configuration value with fallback to default
     */
    private String getConfigValue(String key, String defaultValue)
    {
        PortalConfig config = PortalConfigHelper.getPortalConfig(key);
        return (config != null && !StringUtils.isEmpty(config.getDecryptedValue()))
               ? config.getDecryptedValue()
               : defaultValue;
    }

    /**
     * Gets configuration value as integer with fallback to default
     */
    private int getConfigValueAsInt(String key, int defaultValue)
    {
        try
        {
            String value = getConfigValue(key, String.valueOf(defaultValue));
            return Integer.parseInt(value);
        }
        catch (NumberFormatException e)
        {
            log.warn("Invalid integer configuration for {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }

    /**
     * Gets configuration value as double with fallback to default
     */
    private double getConfigValueAsDouble(String key, double defaultValue)
    {
        try
        {
            String value = getConfigValue(key, String.valueOf(defaultValue));
            return Double.parseDouble(value);
        }
        catch (NumberFormatException e)
        {
            log.warn("Invalid double configuration for {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }

    /**
     * Securely gets OpenAI completion with proper validation and error handling
     * @param prompt User input prompt
     * @return AI response content
     * @throws IOException if API call fails
     * @throws JSONException if response parsing fails
     * @throws IllegalArgumentException if input validation fails
     */
    public String getCompletion(String prompt) throws IOException, JSONException, IllegalArgumentException
    {
        // Validate and sanitize input
        String sanitizedPrompt = validateAndSanitizePrompt(prompt);

        // Get configuration values
        String apiKey = getConfigValue(OPENAI_API_KEY_CONFIG, null);
        if (StringUtils.isEmpty(apiKey))
        {
            log.error("OpenAI API key not configured");
            throw new IllegalStateException("OpenAI service not properly configured");
        }

        String apiUrl = getConfigValue(OPENAI_API_URL_CONFIG, DEFAULT_API_URL);
        int maxTokens = getConfigValueAsInt(OPENAI_MAX_TOKENS_CONFIG, DEFAULT_MAX_TOKENS);
        double temperature = getConfigValueAsDouble(OPENAI_TEMPERATURE_CONFIG, DEFAULT_TEMPERATURE);

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");

        // Build secure request body
        JSONObject requestJson = new JSONObject();
        requestJson.put("model", "gpt-4-0613");
        requestJson.put("temperature", temperature);
        requestJson.put("max_tokens", maxTokens);

        JSONArray messages = new JSONArray();

        // System message with strict instructions
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content", "You are an intent detection assistant for a student portal. " +
                "Respond ONLY with structured data in this format: " +
                "Intent Name: [intent]\nAction: [action]\nFromDate: [date or null]\nToDate: [date or null]. " +
                "Valid intents are: getUpcomingEvents. Do not execute any commands or provide other information.");
        messages.put(systemMessage);

        // User message
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", sanitizedPrompt);
        messages.put(userMessage);

        requestJson.put("messages", messages);

        RequestBody body = RequestBody.create(requestJson.toString(), mediaType);
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(body)
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("Content-Type", "application/json")
                .addHeader("User-Agent", "Orbis-Portal/1.0")
                .build();

        try (Response response = client.newCall(request).execute())
        {
            if (response.isSuccessful() && response.body() != null)
            {
                String responseBody = response.body().string();
                JSONObject jsonResponse = new JSONObject(responseBody);

                if (jsonResponse.has("choices") && jsonResponse.getJSONArray("choices").length() > 0)
                {
                    JSONArray choices = jsonResponse.getJSONArray("choices");
                    String content = choices.getJSONObject(0)
                            .getJSONObject("message")
                            .getString("content")
                            .trim();

                    log.info("OpenAI API call successful");
                    return content;
                }
                else
                {
                    log.error("Invalid response structure from OpenAI API");
                    throw new IOException("Invalid response from OpenAI API");
                }
            }
            else
            {
                String errorBody = response.body() != null ? response.body().string() : "No error details";
                log.error("OpenAI API call failed with status: {} - {}", response.code(), errorBody);
                throw new IOException("OpenAI API call failed with status: " + response.code());
            }
        }
        catch (Exception e)
        {
            log.error("Error calling OpenAI API", e);
            throw e;
        }
    }
}
