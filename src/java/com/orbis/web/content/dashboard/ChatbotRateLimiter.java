package com.orbis.web.content.dashboard;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.utils.StringUtils;

/**
 * Rate limiter for chatbot requests to prevent abuse
 */
@Component
public class ChatbotRateLimiter
{
    private static final Logger log = LoggerFactory.getLogger(ChatbotRateLimiter.class);
    
    // Configuration keys
    private static final String CHATBOT_RATE_LIMIT_CONFIG = "CHATBOT_RATE_LIMIT_PER_HOUR";
    private static final String CHATBOT_RATE_LIMIT_WINDOW_CONFIG = "CHATBOT_RATE_LIMIT_WINDOW_MINUTES";
    
    // Default values
    private static final int DEFAULT_REQUESTS_PER_HOUR = 20;
    private static final int DEFAULT_WINDOW_MINUTES = 60;
    
    // Storage for rate limiting data
    private final ConcurrentHashMap<String, UserRateData> userRateData = new ConcurrentHashMap<>();
    
    /**
     * Data structure to track user request rates
     */
    private static class UserRateData
    {
        private final AtomicInteger requestCount = new AtomicInteger(0);
        private final AtomicLong windowStartTime = new AtomicLong(System.currentTimeMillis());
        
        public int getRequestCount()
        {
            return requestCount.get();
        }
        
        public void incrementRequests()
        {
            requestCount.incrementAndGet();
        }
        
        public long getWindowStartTime()
        {
            return windowStartTime.get();
        }
        
        public void resetWindow()
        {
            requestCount.set(0);
            windowStartTime.set(System.currentTimeMillis());
        }
        
        public boolean isWindowExpired(long windowDurationMs)
        {
            return (System.currentTimeMillis() - windowStartTime.get()) > windowDurationMs;
        }
    }
    
    /**
     * Checks if a user is allowed to make a request based on rate limiting
     * @param userId The user identifier
     * @return true if request is allowed, false if rate limited
     */
    public boolean isRequestAllowed(String userId)
    {
        if (StringUtils.isEmpty(userId))
        {
            log.warn("Rate limit check attempted with empty user ID");
            return false;
        }
        
        // Get configuration values
        int maxRequestsPerWindow = getConfigValueAsInt(CHATBOT_RATE_LIMIT_CONFIG, DEFAULT_REQUESTS_PER_HOUR);
        int windowMinutes = getConfigValueAsInt(CHATBOT_RATE_LIMIT_WINDOW_CONFIG, DEFAULT_WINDOW_MINUTES);
        long windowDurationMs = windowMinutes * 60 * 1000L;
        
        // Get or create user rate data
        UserRateData rateData = userRateData.computeIfAbsent(userId, k -> new UserRateData());
        
        synchronized (rateData)
        {
            // Check if window has expired and reset if needed
            if (rateData.isWindowExpired(windowDurationMs))
            {
                rateData.resetWindow();
            }
            
            // Check if user has exceeded rate limit
            if (rateData.getRequestCount() >= maxRequestsPerWindow)
            {
                log.warn("Rate limit exceeded for user: {} ({} requests in {} minutes)", 
                        userId, rateData.getRequestCount(), windowMinutes);
                return false;
            }
            
            // Increment request count
            rateData.incrementRequests();
            
            log.debug("Rate limit check passed for user: {} ({}/{} requests)", 
                    userId, rateData.getRequestCount(), maxRequestsPerWindow);
            return true;
        }
    }
    
    /**
     * Gets remaining requests for a user in current window
     * @param userId The user identifier
     * @return Number of remaining requests
     */
    public int getRemainingRequests(String userId)
    {
        if (StringUtils.isEmpty(userId))
        {
            return 0;
        }
        
        int maxRequestsPerWindow = getConfigValueAsInt(CHATBOT_RATE_LIMIT_CONFIG, DEFAULT_REQUESTS_PER_HOUR);
        UserRateData rateData = userRateData.get(userId);
        
        if (rateData == null)
        {
            return maxRequestsPerWindow;
        }
        
        synchronized (rateData)
        {
            int windowMinutes = getConfigValueAsInt(CHATBOT_RATE_LIMIT_WINDOW_CONFIG, DEFAULT_WINDOW_MINUTES);
            long windowDurationMs = windowMinutes * 60 * 1000L;
            
            if (rateData.isWindowExpired(windowDurationMs))
            {
                return maxRequestsPerWindow;
            }
            
            return Math.max(0, maxRequestsPerWindow - rateData.getRequestCount());
        }
    }
    
    /**
     * Cleans up expired rate limit data to prevent memory leaks
     */
    public void cleanupExpiredData()
    {
        int windowMinutes = getConfigValueAsInt(CHATBOT_RATE_LIMIT_WINDOW_CONFIG, DEFAULT_WINDOW_MINUTES);
        long windowDurationMs = windowMinutes * 60 * 1000L;
        long cleanupThreshold = windowDurationMs * 2; // Clean up data older than 2 windows
        
        userRateData.entrySet().removeIf(entry -> {
            UserRateData rateData = entry.getValue();
            return (System.currentTimeMillis() - rateData.getWindowStartTime()) > cleanupThreshold;
        });
        
        log.debug("Cleaned up expired rate limit data. Current entries: {}", userRateData.size());
    }
    
    /**
     * Gets configuration value as integer with fallback to default
     */
    private int getConfigValueAsInt(String key, int defaultValue)
    {
        try
        {
            PortalConfig config = PortalConfigHelper.getPortalConfig(key);
            if (config != null && !StringUtils.isEmpty(config.getOrbisValue()))
            {
                return Integer.parseInt(config.getOrbisValue());
            }
        }
        catch (NumberFormatException e)
        {
            log.warn("Invalid integer configuration for {}, using default: {}", key, defaultValue);
        }
        return defaultValue;
    }
}
