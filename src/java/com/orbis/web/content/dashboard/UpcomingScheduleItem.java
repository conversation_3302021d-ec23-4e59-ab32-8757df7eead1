package com.orbis.web.content.dashboard;

import java.time.LocalDateTime;
import java.util.Date;

import com.orbis.web.content.ContentItem;

/**
 * An item in an upcoming schedule
 *
 * <AUTHOR>
 *
 */
public abstract class UpcomingScheduleItem
        implements Comparable<UpcomingScheduleItem>
{
    private int id;

    private Date originalStartDate;

    private Date originalEndDate;

    private LocalDateTime startDate;

    private LocalDateTime endDate;

    private String name;

    private ContentItem module;

    private String status;

    private int conflicts;

    public UpcomingScheduleItem()
    {

    }

    public UpcomingScheduleItem(AbstractBuilder builder)
    {
        id = builder.getId();
        startDate = builder.getStartDate();
        endDate = builder.getEndDate();
        originalStartDate = builder.getOriginalStartDate();
        originalEndDate = builder.getOriginalEndDate();
        name = builder.getName();
        module = builder.getModule();
        status = builder.getStatus();
    }

    public boolean intersects(UpcomingScheduleItem item)
    {
        return startDate.isBefore(item.getEndDate())
                && item.getStartDate().isBefore(endDate);
    }

    public boolean isEvent()
    {
        return this instanceof UpcomingEvent;
    }

    public boolean isAppointment()
    {
        return this instanceof UpcomingAppointment;
    }

    public boolean isInterview()
    {
        return this instanceof UpcomingInterview;
    }

    @Override
    public int compareTo(UpcomingScheduleItem other)
    {
        int ret = 0;
        if (startDate == null && endDate == null)
        {
            ret = -1;
        }
        else if (other.startDate == null && other.endDate == null)
        {
            ret = 1;
        }
        else if (startDate != null && other.startDate != null)
        {
            ret = startDate.compareTo(other.startDate);
        }
        else if (startDate != null)
        {
            ret = 1;
        }
        else if (other.startDate != null)
        {
            ret = -1;
        }

        if (ret == 0)
        {
            if (endDate != null && other.endDate != null)
            {
                ret = endDate.compareTo(other.endDate);
            }
            else if (endDate != null)
            {
                ret = -1;
            }
            else if (other.endDate != null)
            {
                ret = 1;
            }
            else
            {
                ret = 0;
            }
        }
        return ret;

    }

    public int getId()
    {
        return id;
    }

    public LocalDateTime getStartDate()
    {
        return startDate;
    }

    public Date getOriginalStartDate()
    {
        return originalStartDate;
    }

    public void setOriginalStartDate(Date originalStartDate)
    {
        this.originalStartDate = originalStartDate;
    }

    public Date getOriginalEndDate()
    {
        return originalEndDate;
    }

    public void setOriginalEndDate(Date originalEndDate)
    {
        this.originalEndDate = originalEndDate;
    }

    public String getName()
    {
        return name;
    }

    public ContentItem getModule()
    {
        return module;
    }

    public String getStatus()
    {
        return status;
    }

    public LocalDateTime getEndDate()
    {
        return endDate;
    }

    public int getConflicts()
    {
        return conflicts;
    }

    public void setConflicts(int conflicts)
    {
        this.conflicts = conflicts;
    }

    public void setId(int id)
    {
        this.id = id;
    }

    public void setStartDate(LocalDateTime startDate)
    {
        this.startDate = startDate;
    }

    public void setEndDate(LocalDateTime endDate)
    {
        this.endDate = endDate;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setModule(ContentItem module)
    {
        this.module = module;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    /**
     * Abstract builder for {@link UpcomingScheduleItem}
     *
     * @param <B>
     *            Builder type
     */
    public static abstract class AbstractBuilder<B extends AbstractBuilder>
            extends com.orbis.utils.AbstractBuilder<UpcomingScheduleItem, B>
    {
        private int id;

        private Date originalStartDate;

        private Date originalEndDate;

        private LocalDateTime startDate;

        private LocalDateTime endDate;

        private String name;

        private ContentItem module;

        private String status;

        public int getId()
        {
            return id;
        }

        public B id(int id)
        {
            this.id = id;
            return self();
        }

        public LocalDateTime getStartDate()
        {
            return startDate;
        }

        public Date getOriginalStartDate()
        {
            return originalStartDate;
        }

        public Date getOriginalEndDate()
        {
            return originalEndDate;
        }

        public B originalStartDate(Date originalStartDate)
        {
            this.originalStartDate = originalStartDate;
            return self();
        }

        public B originalEndDate(Date originalEndDate)
        {
            this.originalEndDate = originalEndDate;
            return self();
        }

        public B startDate(LocalDateTime startDate)
        {
            this.startDate = startDate;
            return self();
        }

        public LocalDateTime getEndDate()
        {
            return endDate;
        }

        public B endDate(LocalDateTime endDate)
        {
            this.endDate = endDate;
            return self();
        }

        public String getName()
        {
            return name;
        }

        public B name(String name)
        {
            this.name = name;
            return self();
        }

        public ContentItem getModule()
        {
            return module;
        }

        public B module(ContentItem module)
        {
            this.module = module;
            return self();
        }

        public String getStatus()
        {
            return status;
        }

        public B status(String status)
        {
            this.status = status;
            return self();
        }
    }
}
