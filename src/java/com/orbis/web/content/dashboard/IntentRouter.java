package com.orbis.web.content.dashboard;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.orbis.utils.StringUtils;

/**
 * Router for directing parsed intents to appropriate function handlers
 * Provides secure routing with validation and logging
 */
public class IntentRouter
{
    private static final Logger log = LoggerFactory.getLogger(IntentRouter.class);

    private final Map<String, FunctionHandler> registry = new HashMap<>();

    /**
     * Registers a function handler for a specific intent
     * @param functionName The name of the function/intent
     * @param handler The handler implementation
     * @throws IllegalArgumentException if parameters are invalid
     */
    public void registerFunction(String functionName, FunctionHandler handler)
    {
        if (StringUtils.isEmpty(functionName))
        {
            throw new IllegalArgumentException("Function name cannot be empty");
        }

        if (handler == null)
        {
            throw new IllegalArgumentException("Handler cannot be null");
        }

        if (registry.containsKey(functionName))
        {
            log.warn("Overriding existing handler for function: {}", functionName);
        }

        registry.put(functionName, handler);
        log.info("Registered function handler: {}", functionName);
    }

    /**
     * Routes a function call to the appropriate handler
     * @param functionName The name of the function to call
     * @param arguments Arguments to pass to the function
     * @return Result from the function handler
     * @throws IllegalArgumentException if function is not registered or parameters are invalid
     */
    public Object route(String functionName, Map<String, Object> arguments)
    {
        if (StringUtils.isEmpty(functionName))
        {
            throw new IllegalArgumentException("Function name cannot be empty");
        }

        if (arguments == null)
        {
            throw new IllegalArgumentException("Arguments cannot be null");
        }

        FunctionHandler handler = registry.get(functionName);
        if (handler == null)
        {
            log.error("No handler registered for function: {}", functionName);
            throw new IllegalArgumentException("No handler registered for function: " + functionName);
        }

        try
        {
            log.debug("Routing function call: {} with {} arguments", functionName, arguments.size());
            Object result = handler.handleFunctionCall(arguments);
            log.debug("Function call completed successfully: {}", functionName);
            return result;
        }
        catch (Exception e)
        {
            log.error("Error executing function {}: {}", functionName, e.getMessage(), e);
            throw new RuntimeException("Error executing function: " + functionName, e);
        }
    }

    /**
     * Gets the set of registered function names
     * @return Set of registered function names
     */
    public Set<String> getRegisteredFunctions()
    {
        return registry.keySet();
    }

    /**
     * Checks if a function is registered
     * @param functionName The function name to check
     * @return true if function is registered, false otherwise
     */
    public boolean isRegistered(String functionName)
    {
        return !StringUtils.isEmpty(functionName) && registry.containsKey(functionName);
    }
}
