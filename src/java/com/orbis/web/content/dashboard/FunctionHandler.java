package com.orbis.web.content.dashboard;

import java.util.Map;

/**
 * Interface for handling chatbot function calls
 * Implementations should provide specific functionality for different intents
 */
public interface FunctionHandler
{
    /**
     * Handles a function call with the provided arguments
     * @param arguments Map of arguments passed to the function
     * @return String result of the function execution
     * @throws RuntimeException if function execution fails
     */
    String handleFunctionCall(Map<String, Object> arguments);
}
