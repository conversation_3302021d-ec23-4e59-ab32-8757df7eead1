package com.orbis.utils.tags.ui;

@UITagName("expTranscriptCards")
public class ExpTranscriptCardsTag extends UITag
{
    private String id;

    private String classes;

    private String css;

    @Override
    protected void doUITag()
    {

    }

    @Override
    protected String getJspName()
    {
        return "ui_expTranscriptCards";
    }

    public String getId()
    {
        return id;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getClasses()
    {
        return classes;
    }

    public void setClasses(String classes)
    {
        this.classes = classes;
    }

    public String getCss()
    {
        return css;
    }

    public void setCss(String css)
    {
        this.css = css;
    }
}