package com.orbis.dataViewer.filters;

import java.util.Date;

import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.utils.DateUtils;

public class DataViewerDateFilterValue implements IDataViewerFilterValue
{
    public static final String TYPE = DataViewerFilter.TYPE_DATE;

    public static final int OP_BETWEEN = 0;

    public static final int OP_BEFORE = 1;

    public static final int OP_AFTER = 2;

    private String key;

    private int op;

    private Date value;

    private Date end;

    public DataViewerDateFilterValue(JSONObject submittedVal, String key)
            throws JSONException
    {
        this.key = key;
        this.op = submittedVal.getInt("op");

        this.value = DateUtils.parseDate(submittedVal.getString("value"),
                DateUtils.DF_ISO_8601_WITH_MILI);

        if (this.op == OP_BETWEEN)
        {
            this.end = DateUtils.parseDate(submittedVal.getString("end"),
                    DateUtils.DF_ISO_8601_WITH_MILI);
        }
    }

    public int getOp()
    {
        return op;
    }

    public void setOp(int op)
    {
        this.op = op;
    }

    public Date getValue()
    {
        return value;
    }

    public void setValue(Date value)
    {
        this.value = value;
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public Date getEnd()
    {
        return end;
    }

    public void setEnd(Date end)
    {
        this.end = end;
    }

    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = new JSONObject();

        ret.put("op", this.op);
        ret.put("value", DateUtils.formatDate(this.value, DateUtils.DF_ISO_8601));

        if (this.op == OP_BETWEEN)
        {
            ret.put("end", DateUtils.formatDate(this.end, DateUtils.DF_ISO_8601));
        }

        return ret;
    }
}
