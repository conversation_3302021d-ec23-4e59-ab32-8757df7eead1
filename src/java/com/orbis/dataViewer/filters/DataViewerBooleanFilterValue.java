package com.orbis.dataViewer.filters;

import org.json.JSONException;
import org.json.JSONObject;

public class DataViewerBooleanFilterValue implements IDataViewerFilterValue
{
    public static final String TYPE = DataViewerFilter.TYPE_BOOLEAN;

    private String key;

    private boolean value;

    public DataViewerBooleanFilterValue(JSONObject submittedVal, String key)
            throws J<PERSON>NException
    {
        this.key = key;
        this.value = submittedVal.getBoolean("value");
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public boolean isValue()
    {
        return value;
    }

    public void setValue(boolean value)
    {
        this.value = value;
    }

    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = new JSONObject();

        ret.put("value", this.value);

        return ret;
    }

}
