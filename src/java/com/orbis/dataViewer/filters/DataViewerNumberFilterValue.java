package com.orbis.dataViewer.filters;

import org.json.JSONException;
import org.json.JSONObject;

public class DataViewerNumberFilterValue implements IDataViewerFilterValue
{
    public static final String TYPE = DataViewerFilter.TYPE_NUMBER;

    public static final int OP_EQUALS = 0;

    public static final int OP_NOT_EQUALS = 1;

    public static final int OP_GREATER_THAN = 2;

    public static final int OP_GREATER_THAN_OR_EQUAL = 3;

    public static final int OP_LESS_THAN = 4;

    public static final int OP_LESS_THAN_OR_EQUAL = 5;

    public static final int OP_BETWEEN = 6;

    private String key;

    private int op;

    private double value;

    private double max;

    public DataViewerNumberFilterValue(JSONObject submittedVal, String key)
            throws JSONException
    {
        this.key = key;
        this.op = submittedVal.getInt("op");
        this.value = submittedVal.getDouble("value");

        if (this.op == OP_BETWEEN)
        {
            this.max = submittedVal.getDouble("max");
        }
    }

    public JSONObject toJson() throws J<PERSON>NException
    {
        JSONObject ret = new JSONObject();

        ret.put("op", this.op);
        ret.put("value", this.value);

        if (this.op == OP_BETWEEN)
        {
            ret.put("max", this.max);
        }

        return ret;
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public int getOp()
    {
        return op;
    }

    public void setOp(int op)
    {
        this.op = op;
    }

    public double getValue()
    {
        return value;
    }

    public void setValue(int value)
    {
        this.value = value;
    }

    public double getMax()
    {
        return max;
    }

    public void setMax(double max)
    {
        this.max = max;
    }

}
