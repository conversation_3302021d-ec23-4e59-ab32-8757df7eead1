package com.orbis.dataViewer.filters;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.utils.HtmlUtils;
import com.orbis.utils.JSONBuilderUtils;
import com.orbis.utils.JsonParameter;
import com.orbis.utils.JsonValue;
import com.orbis.utils.StringUtils;

public class DataViewerOptionsFilter extends DataViewerFilter
{
    private Map<String, String> options;

    private String optionsVueVar;

    private boolean optionsFilter = false;

    private boolean singleValue = false;

    public DataViewerOptionsFilter(String label, String filterKey)
    {
        super(label, filterKey, TYPE_OPTIONS);
    }

    @Override
    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = super.toJson();

        ret.put("options", getOptionsJson());
        ret.put("optionsFilter", optionsFilter);
        ret.put("singleValue", singleValue);

        return ret;
    }

    @Override
    public Map<String, Object> getTagAttributes()
    {
        Map<String, Object> tagAttributes = super.getTagAttributes();

        tagAttributes.put(":options", isVueOptionsFromData() ? optionsVueVar
                : JSONBuilderUtils.createArrayJSONString(getOptionsJsonValues()));
        tagAttributes.put(":options-filter", optionsFilter);
        tagAttributes.put(":single-value", singleValue);

        return tagAttributes;
    }

    public JSONArray getOptionsJson() throws JSONException
    {
        JSONArray optionsJson = new JSONArray();

        if (options != null)
        {
            for (Entry<String, String> entry : options.entrySet())
            {
                JSONObject option = new JSONObject();
                option.put("value", entry.getKey());
                option.put("label", entry.getValue());
                optionsJson.put(option);
            }
        }

        return optionsJson;
    }

    public List<JsonValue> getOptionsJsonValues()
    {
        List<JsonValue> optionsJsonValues = new LinkedList<JsonValue>();
        if (options != null)
        {
            for (Entry<String, String> entry : options.entrySet())
            {
                List<JsonParameter> params = new ArrayList<JsonParameter>();
                params.add(new JsonParameter("value", entry.getKey()));
                params.add(new JsonParameter("label",
                        HtmlUtils.escape(entry.getValue(), true, true)));

                optionsJsonValues.add(new JsonValue(
                        JSONBuilderUtils.createParameterJSONString(params), false));
            }
        }
        return optionsJsonValues;
    }

    public boolean isOptionsEmpty()
    {
        return StringUtils.isEmpty(optionsVueVar)
                && (options == null || options.isEmpty());
    }

    public Map<String, String> getOptions()
    {
        return options;
    }

    public void setOptions(Map<String, String> options)
    {
        this.options = options;
    }

    public void setOptions(List<String> options)
    {
        this.options = options.stream().collect(
                Collectors.toMap(Function.identity(), Function.identity()));
    }

    public void addOption(String label, String value)
    {
        if (options == null)
        {
            options = new LinkedHashMap<String, String>();
        }

        options.put(value, label);
    }

    public void addOption(String label)
    {
        addOption(label, label);
    }

    public boolean isOptionsFilter()
    {
        return optionsFilter;
    }

    public void setOptionsFilter(boolean optionsFilter)
    {
        this.optionsFilter = optionsFilter;
    }

    public boolean isSingleValue()
    {
        return singleValue;
    }

    public void setSingleValue(boolean singleValue)
    {
        this.singleValue = singleValue;
    }

    public String getOptionsVueVar()
    {
        return optionsVueVar;
    }

    public void setOptionsVueVar(String optionsVueVar)
    {
        this.optionsVueVar = optionsVueVar;
    }

    private boolean isVueOptionsFromData()
    {
        return !StringUtils.isEmpty(optionsVueVar)
                && (options == null || options.isEmpty());
    }
}
