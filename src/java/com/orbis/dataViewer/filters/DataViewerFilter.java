package com.orbis.dataViewer.filters;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.dataViewer.config.DataViewerConfigTag;
import com.orbis.utils.HtmlUtils;
import com.orbis.web.content.grid.NameValuePair;

/**
 * Represents DataViewer filter tag.
 */
public abstract class DataViewerFilter implements Cloneable, DataViewerConfigTag
{
    public static final String TYPE_BOOLEAN = "boolean";

    public static final String TYPE_TEXT = "text";

    public static final String TYPE_OPTIONS = "options";

    public static final String TYPE_NUMBER = "number";

    public static final String TYPE_DATE = "date";

    public static final String TYPE_CUSTOM = "custom";

    private String label;

    private String filterKey;

    private String type;

    private boolean quickFilter = false;

    private boolean advancedFilter = false;

    public DataViewerFilter(String label, String filterKey, String type)
    {
        this.label = label;
        this.filterKey = filterKey;
        this.type = type;
    }

    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = new JSONObject();

        ret.put("label", label);
        ret.put("filterKey", filterKey);
        ret.put("type", type);
        ret.put("isQuickFilter", quickFilter);
        ret.put("isAdvancedFilter", advancedFilter);

        return ret;
    }

    @Override
    public String toTag()
    {
        String tagName = "data-filter";

        List<NameValuePair> tagAttributes = getTagAttributes().entrySet().stream()
                .filter(e -> e.getValue() != null)
                .map(e -> NameValuePair.of(e.getKey(), e.getValue().toString()))
                .collect(Collectors.toList());

        String startTag = HtmlUtils.startTag(tagName, tagAttributes);
        String endTag = HtmlUtils.endTag(tagName);

        return startTag + endTag;
    }

    public String getTag()
    {
        return toTag();
    }

    public Map<String, Object> getTagAttributes()
    {
        Map<String, Object> tagAttributes = new HashMap<String, Object>();
        tagAttributes.put("label", HtmlUtils.escape(label, true, true));
        tagAttributes.put("filter-key", filterKey);
        tagAttributes.put("type", type);
        tagAttributes.put(":is-quick-filter", quickFilter);
        tagAttributes.put(":is-advanced-filter", advancedFilter);
        return tagAttributes;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public String getFilterKey()
    {
        return filterKey;
    }

    public void setFilterKey(String filterKey)
    {
        this.filterKey = filterKey;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public boolean isQuickFilter()
    {
        return quickFilter;
    }

    public void setQuickFilter(boolean quickFilter)
    {
        this.quickFilter = quickFilter;
    }

    public boolean isAdvancedFilter()
    {
        return advancedFilter;
    }

    public void setAdvancedFilter(boolean advancedFilter)
    {
        this.advancedFilter = advancedFilter;
    }

    @Override
    public Object clone() throws CloneNotSupportedException
    {
        return super.clone();
    }
}
