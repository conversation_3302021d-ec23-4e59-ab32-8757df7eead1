package com.orbis.dataViewer.filters;

import org.json.JSONException;
import org.json.JSONObject;

public class DataViewerTextFilterValue implements IDataViewerFilterValue
{
    public static final String TYPE = DataViewerFilter.TYPE_TEXT;

    public static final int OP_CONTAINS = 0;

    public static final int OP_DOES_NOT_CONTAIN = 1;

    public static final int OP_STARTS_WITH = 2;

    public static final int OP_ENDS_WITH = 3;

    public static final int OP_EXACT_MATCH = 4;

    public static final int OP_NOT_EXACT_MATCH = 5;

    public static final int OP_EMPTY = 6;

    public static final int OP_NOT_EMPTY = 7;

    private String key;

    private int op;

    private String value;

    public DataViewerTextFilterValue(String keyword, String key)
    {
        this.key = key;
        this.op = OP_CONTAINS;
        this.value = keyword;
    }

    public DataViewerTextFilterValue(JSONObject submittedVal, String key)
            throws JSONException
    {
        this.key = key;
        this.op = submittedVal.getInt("op");
        if (this.op != OP_EMPTY && this.op != OP_NOT_EMPTY)
        {
            this.value = submittedVal.getString("value");
        }
    }

    public int getOp()
    {
        return op;
    }

    public void setOp(int op)
    {
        this.op = op;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = new JSONObject();

        ret.put("op", this.op);

        if (this.op != OP_EMPTY && this.op != OP_NOT_EMPTY)
        {
            ret.put("value", this.value);
        }

        return ret;
    }

}
