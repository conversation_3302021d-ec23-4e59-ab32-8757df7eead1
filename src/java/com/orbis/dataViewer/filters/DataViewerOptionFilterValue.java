package com.orbis.dataViewer.filters;

import java.util.ArrayList;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.utils.JSONUtils;

public class DataViewerOptionFilterValue implements IDataViewerFilterValue
{
    public static final String TYPE = DataViewerFilter.TYPE_OPTIONS;

    private String key;

    private List<String> value;

    public DataViewerOptionFilterValue(JSONObject submittedVal, String key)
            throws JSONException
    {
        this.setKey(key);

        JSONArray valueJson = submittedVal.getJSONArray("value");
        this.value = new ArrayList<String>();

        for (int i = 0; i < valueJson.length(); i++)
        {
            this.value.add(valueJson.getString(i));
        }
    }

    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = new JSONObject();

        ret.put("value", JSONUtils.toJSON(this.value));

        return ret;
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public List<String> getValue()
    {
        return value;
    }

    public void setValue(List<String> value)
    {
        this.value = value;
    }

}
