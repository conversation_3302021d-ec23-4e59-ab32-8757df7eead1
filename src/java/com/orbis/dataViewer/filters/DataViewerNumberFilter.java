package com.orbis.dataViewer.filters;

import java.util.Map;

import org.json.JSONException;
import org.json.JSONObject;

public class DataViewerNumberFilter extends DataViewerFilter
{
    // Constants for this can be found in DataViewerNumberFilterValue
    private Integer forceOp;

    public DataViewerNumberFilter(String label, String filterKey)
    {
        super(label, filterKey, TYPE_NUMBER);
    }

    @Override
    public JSONObject toJson() throws JSONException
    {
        JSONObject ret = super.toJson();

        if (getForceOp() != null)
        {
            ret.put("forceOp", getForceOp());
        }

        return ret;
    }

    @Override
    public Map<String, Object> getTagAttributes()
    {
        Map<String, Object> tagAttributes = super.getTagAttributes();

        if (getForceOp() != null)
        {
            tagAttributes.put(":force-op", getForceOp());
        }

        return tagAttributes;
    }

    public Integer getForceOp()
    {
        return forceOp;
    }

    public void setForceOp(Integer forceOp)
    {
        this.forceOp = forceOp;
    }

}
