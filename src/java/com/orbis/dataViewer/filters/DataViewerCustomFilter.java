package com.orbis.dataViewer.filters;

import java.util.Map;

public class DataViewerCustomFilter extends DataViewerFilter
{
    private String vBind;

    public DataViewerCustomFilter(String label, String filterKey, String vBind)
    {
        super(label, filterKey, TYPE_CUSTOM);
        this.vBind = vBind;
    }

    @Override
    public Map<String, Object> getTagAttributes()
    {
        Map<String, Object> tagAttributes = super.getTagAttributes();
        tagAttributes.put("v-bind", vBind);
        return tagAttributes;
    }

    public String getvBind()
    {
        return vBind;
    }

    public void setvBind(String vBind)
    {
        this.vBind = vBind;
    }

}
