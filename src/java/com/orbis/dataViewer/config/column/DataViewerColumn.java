package com.orbis.dataViewer.config.column;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.orbis.dataViewer.config.DataViewerConfigTag;
import com.orbis.dataViewer.filters.DataViewerFilter;
import com.orbis.utils.HtmlUtils;
import com.orbis.web.content.grid.NameValuePair;

/**
 * Represents DataViewer column tag
 */
public non-sealed class DataViewerColumn
        implements DataViewerColumnGroupable, DataViewerConfigTag
{

    /**
     * Required
     * <p>
     * Will be shown in the table header.
     */
    private final String title;

    /**
     * Required
     * <p>
     * The column can be referenced by this key.
     */
    private final String key;

    /**
     * Required
     */
    private final Type type;

    /**
     * Optional
     * <p>
     * Whether this column is an identifier of the row. Duplicates idColKey property
     * of the config object. If both specified, idColKey has priority.
     */
    private final Boolean id;

    /**
     * Optional
     * <p>
     * This filter will be rendered in the column tag slot. After processing by
     * DataViewer the filter will be associated with the column.
     */
    private final DataViewerFilter filter;

    /**
     * Optional
     * <p>
     * Vue.js component object. It will be passed to the dataVisualizer prop of the
     * column component. Can be either the object itself or the name of the variable
     * available in the Vue component.
     * 
     * <pre>{@code 
     * <template id="dataViewer">
     * 	<data-viewer v-bind="dataViewerProps">
     * 		<data-column col-key="id" 
     * 		             title="Id"
     * 		             :is-id="true"
     * 		             :visible="false">
     * 	    </data-column>
     * 		<data-column col-key="jobDescription"
     * 		             title="Description"
     * 		             :data-visualizer="descriptionDataVisualizer">
     * 		</data-column>
     * 	</data-viewer>
     * </template>
     * }</pre>
     * 
     * The cell content will be rendered by this Vue.js component instead of the
     * default visualizer.
     * {@link com.orbis.search.shell.dataviewer.DataViewerSearchIntegrationHelper#getShowSubgridDocumentAction}
     */
    private final String dataVisualizer;

    /**
     * Optional, true by default
     * <p>
     * Whether the column will be shown
     */
    private final Boolean visible;

    /**
     * Optional, "MM/DD/YYYY" by default
     * <p>
     * If specified, momentjs will use this data format instead of default one
     */
    private final String dateFormat;

    /**
     * Optional, true by default
     */
    private final Boolean sortable;

    public DataViewerColumn(Builder builder)
    {
        this.title = builder.title;
        this.key = builder.key;
        this.type = builder.type;
        this.id = builder.id;
        this.filter = builder.filter;
        this.dataVisualizer = builder.dataVisualizer;
        this.visible = builder.visible;
        this.dateFormat = builder.dateFormat;
        this.sortable = builder.sortable;
    }

    public String getTitle()
    {
        return title;
    }

    public String getKey()
    {
        return key;
    }

    public Boolean isId()
    {
        return id;
    }

    public DataViewerFilter getFilter()
    {
        return filter;
    }

    public String getDataVisualizer()
    {
        return dataVisualizer;
    }

    public String getDateFormat()
    {
        return dateFormat;
    }

    public Boolean isVisible()
    {
        return visible;
    }

    public Type getType()
    {
        return type;
    }

    public Boolean isSortable()
    {
        return sortable;
    }

    public String toTag()
    {
        String tagName = "data-column";
        List<NameValuePair> tagAttributes = getTagAttributes().entrySet().stream()
                .filter(e -> e.getValue() != null)
                .map(e -> NameValuePair.of(e.getKey(), e.getValue().toString()))
                .collect(Collectors.toList());
        String startTag = HtmlUtils.startTag(tagName, tagAttributes);
        String body = filter != null ? filter.toTag() : "";
        String endTag = HtmlUtils.endTag(tagName);
        return startTag + body + endTag;
    }

    public String getTag()
    {
        return toTag();
    }

    public Map<String, Object> getTagAttributes()
    {
        Map<String, Object> tagAttributes = new HashMap<String, Object>();
        tagAttributes.put("title", title);
        tagAttributes.put("col-key", key);
        tagAttributes.put("is-id", id);
        tagAttributes.put(":data-visualizer", dataVisualizer);
        tagAttributes.put("date-format", dateFormat);
        tagAttributes.put("type", type.toTagValue());
        tagAttributes.put("visible", visible);
        tagAttributes.put("sortable", sortable);
        return tagAttributes;

    }

    public static Builder builder(String title, String key, Type type)
    {
        return new Builder(title, key, type);
    }

    public enum Type
    {
        TEXT,
        NUMBER,
        BOOLEAN,
        OPTIONS,
        DATE,
        CUSTOM;

        public String toTagValue()
        {
            return name().toLowerCase();
        }
    }

    public static class Builder implements com.orbis.utils.Builder<DataViewerColumn>
    {

        private final String title;

        private final String key;

        private final Type type;

        private Boolean id;

        private DataViewerFilter filter;

        private String dataVisualizer;

        private Boolean visible;

        private String dateFormat;

        private Boolean sortable;

        public Builder(String title, String key, Type type)
        {
            this.title = title;
            this.key = key;
            this.type = type;
        }

        public Builder id(boolean id)
        {
            this.id = id;
            return this;
        }

        public Builder filter(DataViewerFilter filter)
        {
            this.filter = filter;
            return this;
        }

        public Builder dataVisualizer(String dataVisualizer)
        {
            this.dataVisualizer = dataVisualizer;
            return this;
        }

        public Builder visible(boolean visible)
        {
            this.visible = visible;
            return this;
        }

        public Builder dateFormat(String dateFormat)
        {
            this.dateFormat = dateFormat;
            return this;
        }

        public Builder sortable(boolean sortable)
        {
            this.sortable = sortable;
            return this;
        }

        @Override
        public DataViewerColumn build()
        {
            return new DataViewerColumn(this);
        }
    }

}
