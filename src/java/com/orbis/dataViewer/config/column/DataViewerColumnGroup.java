package com.orbis.dataViewer.config.column;

import java.util.List;

import com.orbis.dataViewer.config.DataViewerConfigTag;
import com.orbis.utils.HtmlUtils;
import com.orbis.web.content.grid.NameValuePair;

/**
 * Represents grouping config tag that can include either column or other
 * groups. Ex:
 * 
 * <pre>{@code
 * ModelAndView mv = new ModelAndView("exp/redesign/exp_testDataViewer");
 * var config = DataViewerConfig
 *         .builder(DataViewerAjaxActionData
 *                 .builder(PortalUtils.getUserLoggedIn(request),
 *                         "ajaxTestDataViewer")
 *                 .build())//
 *         .addColumnGroup(new DataViewerColumnGroup("Id",
 *                 List.of(DataViewerColumn
 *                         .builder("Id", "id", DataViewerColumn.Type.NUMBER)
 *                         .id(true).build())))
 *         .addColumnGroup(
 *                 new DataViewerColumnGroup(
 *                         "Data", List.of(
 *                                 DataViewerColumn
 *                                         .builder("Name", "name",
 *                                                 DataViewerColumn.Type.TEXT)
 *                                         .build(),
 *                                 DataViewerColumn
 *                                         .builder("Birthdate", "birthdate",
 *                                                 DataViewerColumn.Type.DATE)
 *                                         .build())))
 *         .singleSortMode(true)//
 *         .disableKeywordSearch(true).build();
 * mv.addObject("dataViewerConfig", config);
 * return mv;
 * }</pre>
 * 
 * This config will be transformed into next html:
 * 
 * <pre>{@code 
 * <div id="dataViewerApp">
 *     <data-viewer v-bind="dataViewerProps">
 *         <column-group title="Id">
 *             <data-column is-id="true" col-key="id" title="Id" type=
"number"></data-column>
 *         </column-group>
 *         <column-group title="Data">
 *             <data-column col-key="name" title="Name" type=
"text"></data-column>
 *             <data-column col-key="birthdate" title="Birthdate" type=
"date"></data-column>
 *         </column-group>
 *     </data-viewer>
 * </div>
 * }</pre>
 */
public final class DataViewerColumnGroup
        implements DataViewerColumnGroupable, DataViewerConfigTag
{
    private final String title;

    private final List<DataViewerColumnGroupable> groupables;

    public DataViewerColumnGroup(String title,
            List<DataViewerColumnGroupable> groupables)
    {
        this.title = title;
        this.groupables = groupables;
    }

    @Override
    public String toTag()
    {
        String tagName = "column-group";
        StringBuilder sb = new StringBuilder(
                HtmlUtils.startTag(tagName, NameValuePair.of("title", this.title)));
        groupables.stream().map(DataViewerColumnGroupable::toTag)
                .forEach(sb::append);
        sb.append(HtmlUtils.endTag(tagName));
        return sb.toString();
    }
}
