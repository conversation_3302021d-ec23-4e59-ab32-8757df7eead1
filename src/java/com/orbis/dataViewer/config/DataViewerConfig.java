package com.orbis.dataViewer.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

import org.json.JSONArray;
import org.json.JSONObject;

import com.orbis.dataViewer.config.column.DataViewerColumn;
import com.orbis.dataViewer.config.column.DataViewerColumnGroup;
import com.orbis.dataViewer.config.data.DataViewerData;
import com.orbis.dataViewer.config.sort.DataViewerSort;
import com.orbis.dataViewer.filters.DataViewerFilter;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.JSONUtils;

/**
 * This class represents the configuration for a Data Viewer Vue.js plugin. It
 * helps to conveniently assemble Data Viewer configuration in Java.
 * <p>
 * <b>Note that it doesn't currently include all the Data Viewer configs.</b>
 * The actual list of the available configs can be found in
 * <a href="https://github.com/OrbisInc/spiral-robot">spiral-robot</a> project
 * (see props of DataViewer.vue component and config tags in components/config
 * package). If you need to use some option that isn't represented here, feel
 * free to add it here.
 * <p>
 * Almost all the options are optional (except data) since they have their
 * default values, so DataViewerConfig is implemented using builder pattern:
 * 
 * <pre>{@code
 * ModelAndView mv = new ModelAndView("exp/redesign/exp_testDataViewer");
 * var config = DataViewerConfig
 *         .builder(DataViewerAjaxActionData
 *                 .builder(PortalUtils.getUserLoggedIn(request),
 *                         "ajaxTestDataViewer")
 *                 .build())//
 *         .addColumn(DataViewerColumn
 *                 .builder("Name", "name", DataViewerColumn.Type.TEXT).build())
 *         .addColumn(DataViewerColumn
 *                 .builder("Birthdate", "birthdate", DataViewerColumn.Type.DATE)
 *                 .build())
 *         .singleSortMode(true)//
 *         .disableKeywordSearch(true)//
 *         .build();
 * mv.addObject("dataViewerConfig", config);
 * return mv;
 * }</pre>
 * 
 * In this case the data is being fetched by ajax from the corresponding
 * controller method which should return appropriate json:
 * 
 * <pre>{@code
 * public ModelAndView ajaxTestDataViewer(HttpServletRequest request,
 *         HttpServletResponse response) throws JSONException
 * {
 *     var ret = new JSONObject();
 *     var data = Map.of("Sam", "01/02/1992", "John", "02/03/1990");
 *     JSONArray jsonData = new JSONArray();
 *     int counter = 0;
 *     for (Map.Entry<String, String> dataEntry : data.entrySet())
 *     {
 *         var dataBuilder = new DataViewerDataBuilder().id(counter++)
 *                 .entry("name", dataEntry.getKey())
 *                 .entry("birthdate", dataEntry.getValue());
 *         jsonData.put(dataBuilder.build());
 *     }
 *     ret.put("page", 1);
 *     ret.put("data", jsonData);
 *     ret.put("totalResults", data.size());
 *     return ajaxResponse(ret.toString());
 * }
 * }</pre>
 * <p>
 * DataViewer should be properly initialized in jsp. The plugin config consists
 * from 2 parts:
 * <ul>
 * <li>Tags part including columns and filters</li>
 * <li>Props part including all the rest options</li>
 * </ul>
 * DataViewerConfig class has corresponding getters ({@link #getTags()}
 * {@link #getProps()}) for the plugin configs which should be put in the
 * appropriate place of jsp:
 * 
 * <pre>{@code
 * <%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
 *
 * <orbis:addComponent component="dataViewer"/>
 *
 * <div id="dataViewerApp">
 *     <data-viewer v-bind="dataViewerProps">
 *         ${dataViewerConfig.tags}
 *     </data-viewer>
 * </div>
 *
 * <script>
 *     const dataViewerRootProps = {
 *         data() {
 *             return {
 *                 dataViewerProps: ${dataViewerConfig.props}
 *             }
 *         }
 *     }
 *     const dataViewerApp = Vue.createApp(dataViewerRootProps)
 *         .use(DataViewer.default)
 *         .mount("#dataViewerApp")
 * </script>
 * }</pre>
 * 
 * JSP will generate further html/js:
 * 
 * <pre>{@code 
 * <div id="dataViewerApp">
 *     <data-viewer v-bind="dataViewerProps">
 *         <data-column col-key="name" title="Name" type="text"></data-column>
 *         <data-column col-key="birthdate" title="Birthdate" type=
"date"></data-column>
 *     </data-viewer>
 * </div>
 *
 * <script>
 *     const dataViewerRootProps = {
 *         data() {
 *             return {
 *                 dataViewerProps: {
 *                     "singleSortMode": true,
 *                     "dataParams": {"action": "%7B%22parameters%22%3A%7B%22subAction%22%3A%22%22%2C%22action%22%3A%22ajaxTestDataViewer%22%2C%22ENCRYPT_DATE%22%3A%222024-02-08T13%3A25%3A21%2B0200%22%7D%7D"},
 *                     "disableKeywordSearch": true,
 *                     "data": ""
 *                 }
 *             }
 *         }
 *     }
 *     const dataViewerApp = Vue.createApp(dataViewerRootProps)
 *         .use(DataViewer.default)
 *         .mount("#dataViewerApp")
 * </script>
 * }</pre>
 */
public class DataViewerConfig
{
    /**
     * Represents the data part of DataViewer config. This can be:
     * <ul>
     * <li>Prepared data array (not implemented in Java yet)</li>
     * <li>Data supplying js function (not implemented in Java yet)</li>
     * <li>Controller ajax method settings (implemented in
     * {@link com.orbis.dataViewer.config.data.DataViewerAjaxActionData})</li>
     * </ul>
     */
    private final DataViewerData data;

    /**
     * List of actions available in DataViewer plugin
     */
    private final List<DataViewerAction> actions;

    /**
     * Tags part of DataViewer config. This will be converted to tags.
     */
    private final List<DataViewerConfigTag> configTags;

    /**
     * Represents sorting part of DataViewer config. This can be:
     * <ul>
     * <li>String referencing to column. Sorting direction will be "asc".
     * (implemented in
     * {@link com.orbis.dataViewer.config.sort.DataViewerSimpleSort})</li>
     * <li>Object with key, direction properties. (implemented in
     * {@link com.orbis.dataViewer.config.sort.DataViewerDirectedSort}</li>
     * <li>Array of objects representing multiple fields sorting. (not implemented
     * in Java yet)</li>
     * </ul>
     */
    private final DataViewerSort sort;

    /**
     * Whether the user cannot sort data by multiple columns.
     * <p>
     * false by default
     */
    private final Boolean singleSortMode;

    /**
     * Whether user can turn on doc viewer mode
     * <p>
     * false by default
     */
    private final Boolean enableDocViewer;

    /**
     * This should be a Vue.js component javascript object, so this property won't
     * be surrounded with quotes when adding to the config object. Can be either the
     * object itself or the name of the js variable declared in the reachable scope.
     * Will be rendered either in doc viewer mode {@link #enableDocViewer} or in a
     * modal using appropriate action callback
     * {@link com.orbis.search.shell.dataviewer.DataViewerSearchIntegrationHelper#getShowSubgridDocumentAction(Locale)}
     */
    private final String docViewerDocument;

    /**
     * false by default
     */
    private final Boolean disableKeywordSearch;

    private DataViewerConfig(Builder builder)
    {
        this.data = builder.data;
        this.actions = builder.actions;
        this.configTags = builder.configTags;
        this.sort = builder.sort;
        this.singleSortMode = builder.singleSortMode;
        this.enableDocViewer = builder.enableDocViewer;
        this.docViewerDocument = builder.docViewerDocument;
        this.disableKeywordSearch = builder.disableKeywordSearch;
    }

    public DataViewerData getData()
    {
        return this.data;
    }

    public List<DataViewerAction> getActions()
    {
        return actions;
    }

    public static Builder builder(DataViewerData data)
    {
        return new Builder(data);
    }

    /**
     * This method assembles and returns json part of dataViewer config according to
     * DataViewer API
     * 
     * @return json object including props part of DataViewer config
     */
    public JSONObject getProps()
    {
        record ConfigItemsSerializer(JSONUtils.JSONBuilder builder) {
            ConfigItemsSerializer serialize(DataViewerConfigItems items)
            {
                if (items != null)
                {
                    JSONUtils.IterableJson json = JSONUtils
                            .iterable(items.toJSON());
                    for (String key : json)
                    {
                        builder.putOpt(key, json.getObject(key));
                    }
                }
                return this;
            }
        }
        JSONUtils.JSONBuilder builder = new JSONUtils.JSONBuilder()
                .putOpt("enableDocViewer", enableDocViewer)
                .putOptJS("docViewerDocument", docViewerDocument)
                .putOpt("disableKeywordSearch", disableKeywordSearch)
                .putOpt("singleSortMode", singleSortMode);
        serializeActions(builder);
        var itemsSerializer = new ConfigItemsSerializer(builder);
        itemsSerializer.serialize(data)//
                .serialize(sort);
        return builder.build();
    }

    private void serializeActions(JSONUtils.JSONBuilder builder)
    {
        if (CollectionUtils.isNotEmpty(actions))
        {
            JSONArray actionsArray = new JSONArray();
            for (DataViewerAction action : actions)
            {
                actionsArray.put(action.toJSONObject());
            }
            builder.put("actions", actionsArray);
        }
    }

    /**
     * It should be put in the default slot of DataViewer vue component:
     * 
     * <pre>{@code
     * <div id="dataViewerApp">
     *     <data-viewer v-bind="dataViewerProps">
     *         ${dataViewerConfig.tags}
     *     </data-viewer>
     * </div>
     * }</pre>
     * 
     * @return string containing config tags for DataViewer plugin
     */
    public String getTags()
    {
        StringBuilder sb = new StringBuilder();
        configTags.stream()//
                .map(DataViewerConfigTag::toTag)//
                .forEach(sb::append);
        return sb.toString();
    }

    public static class Builder implements com.orbis.utils.Builder<DataViewerConfig>
    {

        private final DataViewerData data;

        private final List<DataViewerAction> actions = new ArrayList<>();

        private final List<DataViewerConfigTag> configTags = new ArrayList<>();

        private DataViewerSort sort;

        private Boolean singleSortMode;

        private Boolean enableDocViewer;

        private String docViewerDocument;

        private Boolean disableKeywordSearch;

        public Builder(DataViewerData data)
        {
            this.data = data;
        }

        public Builder addActions(DataViewerAction... actions)
        {
            return addActions(List.of(actions));
        }

        public Builder addActions(Collection<DataViewerAction> actions)
        {
            this.actions.addAll(actions);
            return this;
        }

        public Builder addAction(DataViewerAction action)
        {
            this.actions.add(action);
            return this;
        }

        public Builder columns(DataViewerColumn... columns)
        {
            return columns(Arrays.asList(columns));
        }

        public Builder columns(Collection<DataViewerColumn> columns)
        {
            this.configTags.addAll(columns);
            return this;
        }

        public Builder addColumn(DataViewerColumn column)
        {
            this.configTags.add(column);
            return this;
        }

        public Builder columnGroups(DataViewerColumnGroup... groups)
        {
            this.configTags.addAll(List.of(groups));
            return this;
        }

        public Builder addColumnGroup(DataViewerColumnGroup group)
        {
            this.configTags.add(group);
            return this;
        }

        public Builder filters(DataViewerFilter... filters)
        {
            this.configTags.addAll(Arrays.asList(filters));
            return this;
        }

        public Builder addFilter(DataViewerFilter filter)
        {
            this.configTags.add(filter);
            return this;
        }

        public Builder sort(DataViewerSort sort)
        {
            this.sort = sort;
            return this;
        }

        public Builder singleSortMode(boolean singleSortMode)
        {
            this.singleSortMode = singleSortMode;
            return this;
        }

        public Builder enableDocViewer(Boolean enableDocViewer)
        {
            this.enableDocViewer = enableDocViewer;
            return this;
        }

        public Builder docViewerDocument(String docViewerDocument)
        {
            this.docViewerDocument = docViewerDocument;
            return this;
        }

        public Builder disableKeywordSearch(Boolean disableKeywordSearch)
        {
            this.disableKeywordSearch = disableKeywordSearch;
            return this;
        }

        @Override
        public DataViewerConfig build()
        {
            return new DataViewerConfig(this);
        }
    }

}
