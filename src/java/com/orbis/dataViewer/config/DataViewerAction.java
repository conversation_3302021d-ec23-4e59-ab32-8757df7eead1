package com.orbis.dataViewer.config;

import org.json.JSONObject;

import com.orbis.utils.JSONSerializable;
import com.orbis.utils.JSONUtils;

public class DataViewerAction implements JSONSerializable
{

    private final String label;

    /**
     * function(row) - predicate that decides if the action should be visible or not
     * based off the row data
     */
    private final String isVisible;

    /**
     * classes to be applied to the clickable element
     */
    private final String classes;

    /**
     * css rules to be applied to the clickable element
     */
    private final String css;

    /**
     * function(rowIdsSelected)
     */
    private final String callback;

    /**
     * defaults to true
     */
    private final Boolean isBulkAction;

    /**
     * defaults to true
     */
    private final Boolean isRowAction;

    /**
     * defaults to false
     */
    private final Boolean isGlobalAction;

    /**
     * defaults to false
     */
    private final Boolean isTitleAction;

    /**
     * defaults to false
     */
    private final Boolean isFilterBarAction;

    /**
     * defaults to true
     */
    private final Boolean showInTable;

    /**
     * defaults to true
     */
    private final Boolean showInDocViewerCard;

    /**
     * defaults to true
     */
    private final Boolean showInDocViewerDocument;

    /**
     * material icon name, if populated and is row action, the action will show up
     * in the row instead of the row dropdown
     */
    private final String icon;

    protected DataViewerAction(Builder builder)
    {
        this.label = builder.label;
        this.isVisible = builder.isVisible;
        this.classes = builder.classes;
        this.css = builder.css;
        this.callback = builder.callback;
        this.isBulkAction = builder.isBulkAction;
        this.isRowAction = builder.isRowAction;
        this.isGlobalAction = builder.isGlobalAction;
        this.isTitleAction = builder.isTitleAction;
        this.isFilterBarAction = builder.isFilterBarAction;
        this.showInTable = builder.showInTable;
        this.showInDocViewerCard = builder.showInDocViewerCard;
        this.showInDocViewerDocument = builder.showInDocViewerDocument;
        this.icon = builder.icon;
    }

    public String getLabel()
    {
        return label;
    }

    public String getIsVisible()
    {
        return isVisible;
    }

    public String getClasses()
    {
        return classes;
    }

    public String getCss()
    {
        return css;
    }

    public String getCallback()
    {
        return callback;
    }

    public boolean isBulkAction()
    {
        return isBulkAction;
    }

    public boolean isRowAction()
    {
        return isRowAction;
    }

    public boolean isGlobalAction()
    {
        return isGlobalAction;
    }

    public boolean isTitleAction()
    {
        return isTitleAction;
    }

    public boolean isFilterBarAction()
    {
        return isFilterBarAction;
    }

    public Boolean isShowInTable()
    {
        return showInTable;
    }

    public Boolean isShowInDocViewerCard()
    {
        return showInDocViewerCard;
    }

    public Boolean isShowInDocViewerDocument()
    {
        return showInDocViewerDocument;
    }

    public String getIcon()
    {
        return icon;
    }

    public static Builder builder()
    {
        return new Builder();
    }

    @Override
    public void fromJSONString(String jsonString)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public void fromJSONObject(JSONObject jsonObject)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public JSONObject toJSONObject()
    {
        return new JSONUtils.JSONBuilder().putOpt("label", label)
                .putOptJS("isVisible", isVisible)//
                .putOpt("class", classes)//
                .putOpt("css", css)//
                .putOptJS("callback", callback)//
                .putOpt("isBulkAction", isBulkAction)
                .putOpt("isRowAction", isRowAction)
                .putOpt("isGlobalAction", isGlobalAction)
                .putOpt("isTitleAction", isTitleAction)
                .putOpt("isFilterBarAction", isFilterBarAction)
                .putOpt("showInTable", showInTable)
                .putOpt("showInDocViewerCard", showInDocViewerCard)
                .putOpt("showInDocViewerDocument", showInDocViewerDocument)
                .putOpt("icon", icon)//
                .build();
    }

    @Override
    public String toJSONString()
    {
        return toJSONObject().toString();
    }

    public static class Builder implements com.orbis.utils.Builder<DataViewerAction>
    {

        private String label;

        private String isVisible;

        private String classes;

        private String css;

        private String callback;

        private Boolean isBulkAction;

        private Boolean isRowAction;

        private Boolean isGlobalAction;

        private Boolean isTitleAction;

        private Boolean isFilterBarAction;

        private Boolean showInTable;

        private Boolean showInDocViewerCard;

        private Boolean showInDocViewerDocument;

        private String icon;

        public Builder label(String label)
        {
            this.label = label;
            return this;
        }

        public Builder isVisible(String isVisible)
        {
            this.isVisible = isVisible;
            return this;
        }

        public Builder classes(String classes)
        {
            this.classes = classes;
            return this;
        }

        public Builder css(String css)
        {
            this.css = css;
            return this;
        }

        public Builder callback(String callback)
        {
            this.callback = callback;
            return this;
        }

        public Builder isBulkAction(boolean isBulkAction)
        {
            this.isBulkAction = isBulkAction;
            return this;
        }

        public Builder isRowAction(boolean isRowAction)
        {
            this.isRowAction = isRowAction;
            return this;
        }

        public Builder isGlobalAction(boolean isGlobalAction)
        {
            this.isGlobalAction = isGlobalAction;
            return this;
        }

        public Builder isTitleAction(boolean isTitleAction)
        {
            this.isTitleAction = isTitleAction;
            return this;
        }

        public Builder isFilterBarAction(boolean isFilterBarAction)
        {
            this.isFilterBarAction = isFilterBarAction;
            return this;
        }

        public Builder showInTable(boolean showInTable)
        {
            this.showInTable = showInTable;
            return this;
        }

        public Builder showInDocViewerCard(boolean showInDocViewerCard)
        {
            this.showInDocViewerCard = showInDocViewerCard;
            return this;
        }

        public Builder showInDocViewerDocument(boolean showInDocViewerDocument)
        {
            this.showInDocViewerDocument = showInDocViewerDocument;
            return this;
        }

        public Builder icon(String icon)
        {
            this.icon = icon;
            return this;
        }

        @Override
        public DataViewerAction build()
        {
            return new DataViewerAction(this);
        }
    }

}
