package com.orbis.dataViewer.config.data;

import java.util.HashMap;
import java.util.Map;

import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.utils.JSONUtils;

/**
 * Represents ajax source for DataViewer datasource config
 */
public class DataViewerAjaxData implements DataViewerData
{

    /**
     * Required
     * <p>
     * URL to fetch data from
     */
    private final String url;

    /**
     * Optional, "post" by default
     * <p>
     * HTTP request method
     */
    private final DataMethod dataMethod;

    /**
     * Optional, "json" by default
     * <p>
     * Expected response type for HTTP request
     */
    private final DataType dataType;

    /**
     * Request params
     */
    private final Map<String, String> params;

    /**
     * Optional
     * <p>
     * JS function which should transform the response into a valid DataViewer data
     * format. Isn't necessary if response format matches DataViewer API
     */
    private final String dataTransformResponse;

    protected DataViewerAjaxData(Builder builder)
    {
        this.url = builder.url;
        this.dataMethod = builder.dataMethod;
        this.dataType = builder.dataType;
        this.params = builder.params;
        this.dataTransformResponse = builder.dataTransformResponse;
    }

    /**
     * @return
     * @throws JSONException
     */
    @Override
    public JSONObject toJSON()
    {
        return JSONUtils.builder()//
                .put("data", url)//
                .putOpt("dataMethod",
                        dataMethod == null ? null : dataMethod.name().toLowerCase())
                .putOpt("dataType",
                        dataType == null ? null : dataType.name().toLowerCase())
                .putOpt("dataParams", params)//
                .putOptJS("dataTransformResponse", dataTransformResponse)//
                .build();
    }

    /**
     * Valid response type for HTTP request in DataViewer
     */
    public enum DataType
    {
        TEXT,
        JSON
    }

    /**
     * Valid HTTP request methods in DataViewer
     */
    public enum DataMethod
    {
        POST,
        GET
    }

    public Builder builder()
    {
        return new Builder();
    }

    public static class Builder
            implements com.orbis.utils.Builder<DataViewerAjaxData>
    {

        private String url = "";

        private DataMethod dataMethod;

        private DataType dataType;

        private final Map<String, String> params = new HashMap<>();

        private String dataTransformResponse;

        public Builder url(String url)
        {
            this.url = url;
            return this;
        }

        public Builder dataMethod(DataMethod dataMethod)
        {
            this.dataMethod = dataMethod;
            return this;
        }

        public Builder dataType(DataType dataType)
        {
            this.dataType = dataType;
            return this;
        }

        public Builder params(Map<String, String> params)
        {
            this.params.putAll(params);
            return this;
        }

        public Builder addParam(String key, String value)
        {
            this.params.put(key, value);
            return this;
        }

        public Builder dataTransformResponse(String dataTransformResponse)
        {
            this.dataTransformResponse = dataTransformResponse;
            return this;
        }

        @Override
        public DataViewerAjaxData build()
        {
            return new DataViewerAjaxData(this);
        }
    }
}
