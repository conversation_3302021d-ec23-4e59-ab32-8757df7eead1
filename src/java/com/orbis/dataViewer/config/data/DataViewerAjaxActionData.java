package com.orbis.dataViewer.config.data;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.utils.ActionHelper;

/**
 * This class can be used to configure DataViewer for fetching data from the
 * Outcome action methods
 */
public class DataViewerAjaxActionData extends DataViewerAjaxData
{

    public DataViewerAjaxActionData(Builder builder)
    {
        super(builder);
    }

    public static Builder builder(UserDetailsImpl userDetails, String action)
    {
        return new Builder(userDetails, action);
    }

    public static class Builder extends DataViewerAjaxData.Builder
    {

        public Builder(UserDetailsImpl userDetails, String action)
        {
            this.userDetails = userDetails;
            this.action = action;
        }

        private final UserDetailsImpl userDetails;

        private final String action;

        private String subAction;

        public Builder subAction(String subAction)
        {
            this.subAction = subAction;
            return this;
        }

        @Override
        public DataViewerAjaxActionData build()
        {
            addParam("action",
                    ActionHelper.encryptAction(userDetails, action, subAction));
            return new DataViewerAjaxActionData(this);
        }
    }
}
