package com.orbis.dataViewer.config.sort;

import org.json.JSONArray;
import org.json.JSONObject;

import com.orbis.utils.JSONUtils;

/**
 * Represents sort option of DataViewer config. Table will be sorted by one
 * column using specified direction
 */
public class DataViewerDirectedSort extends DataViewerSimpleSort
{

    private final Direction direction;

    public DataViewerDirectedSort(String column, Direction direction)
    {
        super(column);
        this.direction = direction;
    }

    @Override
    protected Object getObject()
    {
        JSONArray jsonArray = new JSONArray();
        JSONObject sort = JSONUtils.newJSONObject("key", column, "direction",
                direction);
        jsonArray.put(sort);
        return jsonArray;
    }
}
