package com.orbis.dataViewer.config.sort;

import java.util.Arrays;
import java.util.Optional;

import com.orbis.dataViewer.config.DataViewerConfigItems;
import com.orbis.utils.JSONUtils;

import org.json.JSONObject;

public abstract class DataViewerSort implements DataViewerConfigItems
{

    @Override
    public JSONObject toJSON()
    {
        return JSONUtils.builder().put("sort", getObject()).build();
    }

    protected abstract Object getObject();

    public enum Direction
    {
        ASC,
        DESC;

        public static Optional<Direction> of(String string)
        {
            return Arrays.stream(values())
                    .filter(m -> m.toString().equalsIgnoreCase(string)).findFirst();
        }

        public static Direction ofOrDefault(String string)
        {
            return of(string).orElse(ASC);
        }
    }
}
