package com.orbis.dataViewer.config.sort;

/**
 * Represents sort option of DataViewer config. Table will be sorted by one
 * column using ascending direction
 */
public class DataViewerSimpleSort extends DataViewerSort
{

    protected final String column;

    public DataViewerSimpleSort(String column)
    {
        this.column = column;
    }

    /**
     * @return
     */
    @Override
    protected Object getObject()
    {
        return column;
    }
}
