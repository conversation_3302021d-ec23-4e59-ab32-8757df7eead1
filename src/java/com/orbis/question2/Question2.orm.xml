<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.question2.Question2" name="Question2">
        <table name="question2"/>
        <attributes>


            <basic name="type">
                <column name="type" nullable="false" />
            </basic>
            <basic name="typeLocked">
                <column name="typeLocked" nullable="false" />
            </basic>

            <basic name="questionText"/>
            <basic name="questionTextLocked">
                <column name="questionTextLocked" nullable="false" />
            </basic>

            <basic name="position">
                <column name="position" nullable="false" />
            </basic>
            <basic name="adminOnly">
                <column name="adminOnly" nullable="false" />
            </basic>
            <basic name="required">
                <column name="required" nullable="false" />
            </basic>
            <basic name="min">
                <column name="min" nullable="false" />
            </basic>
            <basic name="max">
                <column name="max" nullable="false" />
            </basic>
            <basic name="usingNA">
                <column name="usingNA" nullable="false" />
            </basic>
            <basic name="displayType">
                <column name="displayType" nullable="false" />
            </basic>
            <basic name="choices">
                <column column-definition="TEXT"/>
            </basic>
            <basic name="includeOther">
                <column name="includeOther" nullable="false" />
            </basic>
            <basic name="description">
                <column column-definition="TEXT"/>
            </basic>
            <basic name="maxUploadSize">
                <column  name="maxUploadSize" nullable="false" column-definition="numeric(19)"/>
            </basic>
            <basic name="treeNodes">
                <column column-definition="TEXT"/>
            </basic>
            <basic name="singleTreePath">
                <column name="singleTreePath" nullable="false" />
            </basic>
            <basic name="limitAnswer">
                <column name="limitAnswer" nullable="false" />
            </basic>
            <basic name="matrixColumns">
                <column column-definition="TEXT"/>
            </basic>
            <basic name="matrixRows">
                <column column-definition="TEXT"/>
            </basic>

            <basic name="answerField1"/>
            <basic name="answerField2"/>
            <basic name="answerField3"/>
            <basic name="answerField4"/>
            <basic name="answerField5"/>

            <basic name="jsonUserModel">
                <column column-definition="TEXT"/>
            </basic>
            <basic name="mappingKey"/>

        </attributes>
    </entity>
</entity-mappings>