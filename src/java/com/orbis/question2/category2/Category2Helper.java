package com.orbis.question2.category2;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.orbis.portal.CommandQueryTemplate;

import com.orbis.question2.QuestionInterface;
import com.orbis.portal.PortalUtils;

public class Category2Helper
{

    /**
     * Returns a Category containing the List of provided questions.
     */
    public static Category2 getCategoryQuestion(String categoryName,
            List<? extends QuestionInterface> questions)
    {
        List<Integer> questionIds = new ArrayList<Integer>();
        for (QuestionInterface q : questions)
        {
            questionIds.add(q.getId());
        }

        Category2 cat = new Category2();
        cat.setName(categoryName);
        cat.setPosition(0);
        // cat.setQuestionIds(questionIds);

        return cat;
    }

    public static Category2 getCategory(int id)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<Category2> categorys = ht.find("from Category2 c where c.id=?", id);
        return categorys.get(0);
    }

    /**
     * Returns a Map representing the "Category Model". This map is used as a
     * 'helper' to render the categories & questions in the JPSs.
     */
    public static Map<Category2, List<? extends QuestionInterface>> getCategoryMap(
            Category2Model model)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Map<Category2, List<? extends QuestionInterface>> ret = new LinkedHashMap<Category2, List<? extends QuestionInterface>>();

        if (model != null)
        {
            List<Category2> category2s = ht.find(
                    "from Category2 c where c.model=? order by c.position", model);

            for (Category2 category : category2s)
            {
                List<Category2Question> category2Questions = ht.find(
                        "from Category2Question cq where cq.category=? order by cq.position",
                        category);

                List<QuestionInterface> catQuestions = new LinkedList<QuestionInterface>();

                for (QuestionInterface q : category2Questions)
                {
                    catQuestions.add(q);
                }

                ret.put(category, catQuestions);
            }

        }

        return ret;
    }

    public static void main(String[] args)
    {
        Category2Model model = new Category2Model();

        Category2 cat = new Category2();
        cat.setName("Category A");
        cat.setPosition(0);
        // cat.setQuestionIds(Arrays.asList(new Integer[] { 1, 2, 3 }));
        // model.add(cat);

        cat = new Category2();
        cat.setName("Category B");
        cat.setPosition(1);
        // cat.setQuestionIds(Arrays.asList(new Integer[] { 4, 5, 6 }));
        // model.add(cat);

        cat = new Category2();
        cat.setName("Category C");
        cat.setAdminOnly(true);
        cat.setPosition(2);
        // cat.setQuestionIds(Arrays.asList(new Integer[] { 7, 8, 9 }));
        // model.add(cat);

        // System.out.println(model);

        String jsonModel = model.toJSONString();

        // System.out.println(jsonModel);

        Category2Model model2 = new Category2Model().fromJSONString(jsonModel);

        // System.out.println(model2);
    }

}
