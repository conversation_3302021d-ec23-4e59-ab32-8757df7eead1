package com.orbis.question2.category2;

import org.hibernate.annotations.ColumnDefault;

import com.orbis.question2.Question2;
import com.orbis.question2.QuestionInterface;
import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public abstract class Category2Question extends ContentItem
        implements QuestionInterface
{
    private Category2 category;

    private Question2 question;

    @ColumnDefault("0")
    private int position;

    public int getPosition()
    {
        return position;
    }

    public void setPosition(int position)
    {
        this.position = position;
    }

    public Category2 getCategory()
    {
        return category;
    }

    public void setCategory(Category2 category)
    {
        this.category = category;
    }

    public Question2 getQuestion()
    {
        return question;
    }

    public void setQuestion(Question2 question)
    {
        this.question = question;
    }

}
