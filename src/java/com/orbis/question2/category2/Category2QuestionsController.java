package com.orbis.question2.category2;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.orbis.web.site.SiteManager;
import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.question2.MockQuestionEntity;
import com.orbis.question2.Question2;
import com.orbis.question2.Question2Helper;
import com.orbis.question2.QuestionInterface;
import com.orbis.question2.QuestionUserType;
import com.orbis.utils.DBUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisInteractionController;
import com.orbis.web.OrbisModule;

public abstract class Category2QuestionsController<Module extends OrbisModule>
        extends OrbisInteractionController<Module>
{

    public Category2QuestionsController() {
    }

    public Category2QuestionsController(SiteManager siteManager) {
        super(siteManager);
    }

    public Category2QuestionsController(String homePage, SiteManager siteManager) {
        super(homePage, siteManager);
    }

    /**
     * Return an instance of QuestionInterface, based on request parameters.
     */
    public abstract QuestionInterface getQuestion(HttpServletRequest request);

    /**
     * Return a List of QuestionInterface questions that belong to a "group of
     * questions", based on request parameters.
     */
    public abstract List<? extends QuestionInterface> getCategoryQuestions(
            HttpServletRequest request);

    /**
     * Return a json category model, based on request parameters.
     */
    public abstract Category2Model getCategoryModel(HttpServletRequest request);

    /**
     * Store a Category2Model, based on request parameters.
     */
    public abstract void saveCategoryModel(Category2Model categoryModel,
            HttpServletRequest request);

    /**
     * Return a *new* QuestionInterface instance for the newly created Question2
     * instance, based on request parameters.
     */
    public abstract Category2Question onQuestionCreated(Question2 q,
            Category2 category, HttpServletRequest request);

    /**
     * Return a default List of QuestionUserType instances, based on request
     * parameters.
     */
    public abstract List<QuestionUserType> getDefaultQuestionUserTypes(
            HttpServletRequest request);

    /**
     * Return a Map of "field mappings" that will be presented in the question
     * editor.
     */
    public abstract Map<String, String> getFieldMappings(
            HttpServletRequest request);

    /**
     * LOAD CATEGORIES UI
     * 
     * @param request
     * @param response
     * @return
     */

    @RequestMapping("ajaxLoadCategoryEditor")
    public ModelAndView ajaxLoadCategoryEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        // dumpRequestParameters(request);
        ModelAndView mv = getShortCircuitView(
                "question2/category2/category2_categories");

        mv.addObject("categoryMap",
                Category2Helper.getCategoryMap(getCategoryModel(request)));

        return mv;
    }

    /**
     * SAVE CATEGORY MODEL
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxSaveCategoryModel")
    public ModelAndView ajaxSaveCategoryModel(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        try
        {
            // dumpRequestParameters(request);

            Category2Model currentModel = getCategoryModel(request);
            String changedModel = request.getParameter("model");

            _syncModel(currentModel, request, changedModel);

            saveCategoryModel(currentModel, request);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return jsonBooleanResponse(SUCCESS_KEY, true);
    }

    private void _syncModel(Category2Model currentModel, HttpServletRequest request,
            String changedModel)
    {
        try
        {
            JSONArray jsonChangedModel = new JSONArray(changedModel);
            addCategories(jsonChangedModel, currentModel);
            moveQuestions(jsonChangedModel, currentModel);
            deleteRemovedQuestions(jsonChangedModel, currentModel);
            deleteRemovedCategory(jsonChangedModel, currentModel);
            calculatePositions(jsonChangedModel, currentModel);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void calculatePositions(JSONArray jsonChangedModel,
            Category2Model currentModel) throws JSONException
    {

        List<Category2> categories = getHt()
                .find("from Category2 c2 where c2.model=?", currentModel);
        List<Category2Question> questions = getHt().find(
                "from Category2Question c2q where c2q.category.model=?",
                currentModel);
        for (Category2 category : categories)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                if (!jsonChangedModel.getJSONObject(i).isNull("id")
                        && jsonChangedModel.getJSONObject(i)
                                .getInt("id") == category.getId())
                {
                    category.setPosition(i);
                    getHt().update(category);
                }
            }
        }
        for (Category2Question question : questions)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                for (int j = 0; j < jsonChangedModel.getJSONObject(i)
                        .getJSONArray("questionIds").length(); j++)
                {
                    if (!jsonChangedModel.getJSONObject(i).isNull("questionIds")
                            && jsonChangedModel.getJSONObject(i)
                                    .getJSONArray("questionIds")
                                    .getInt(j) == question.getId())
                    {
                        question.setPosition(j);
                        getHt().update(question);
                    }
                }
            }
        }

    }

    private void moveQuestions(JSONArray jsonChangedModel,
            Category2Model currentModel) throws JSONException
    {
        List<Category2Question> questions = getHt().find(
                "from Category2Question c2q where c2q.category.model=?",
                currentModel);
        for (Category2Question question : questions)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                if (!jsonChangedModel.getJSONObject(i).isNull("id"))
                {
                    for (int j = 0; j < jsonChangedModel.getJSONObject(i)
                            .getJSONArray("questionIds").length(); j++)
                    {
                        if (question.getCategory() != null)
                        {
                            if (question.getId() == jsonChangedModel
                                    .getJSONObject(i).getJSONArray("questionIds")
                                    .getInt(j)
                                    && question.getCategory()
                                            .getId() != jsonChangedModel
                                                    .getJSONObject(i).getInt("id"))
                            {
                                question.setCategory(
                                        Category2Helper.getCategory(jsonChangedModel
                                                .getJSONObject(i).getInt("id")));
                                getHt().update(question);
                            }
                        }
                    }
                }
            }
        }

    }

    private void addCategories(JSONArray jsonChangedModel,
            Category2Model currentModel) throws JSONException
    {
        List<Category2> categories = getHt()
                .find("from Category2 c2 where c2.model=?", currentModel);
        for (int i = 0; i < jsonChangedModel.length(); i++)
        {
            if (jsonChangedModel.getJSONObject(i).isNull("id"))
            {
                Category2 newCategory = new Category2();
                newCategory.setModel(currentModel);
                newCategory.setName(
                        jsonChangedModel.getJSONObject(i).get("name").toString());
                newCategory.setPosition(jsonChangedModel.length() - 1);
                newCategory.setAdminOnly(false);
                getHt().save(newCategory);
            }
            else
            {
                for (Category2 category : categories)
                {
                    if (jsonChangedModel.getJSONObject(i).getInt("id") == category
                            .getId()
                            && !jsonChangedModel.getJSONObject(i).getString("name")
                                    .equalsIgnoreCase(category.getName()))
                    {
                        category.setName(jsonChangedModel.getJSONObject(i)
                                .getString("name"));
                        getHt().update(category);
                    }
                }

            }

        }
    }

    private void deleteRemovedCategory(JSONArray jsonChangedModel,
            Category2Model currentModel) throws JSONException
    {
        List<Integer> deletedCategoryIds = new ArrayList();
        List<Category2> categories = getHt()
                .find("from Category2 c2 where c2.model=?", currentModel);

        if (jsonChangedModel.length() == 0 && !categories.isEmpty())
        {
            for (Category2 category : categories)
            {
                deletedCategoryIds.add(category.getId());
            }
        }
        else
        {
            for (Category2 category : categories)
            {
                for (int i = 0; i < jsonChangedModel.length(); i++)
                {
                    if (!jsonChangedModel.getJSONObject(i).isNull("id"))
                    {
                        if (category.getId() == jsonChangedModel.getJSONObject(i)
                                .getInt("id"))
                        {
                            break;
                        }
                        else if (i == jsonChangedModel.length() - 1)
                        {
                            deletedCategoryIds.add(category.getId());
                        }
                    }
                }
            }
        }

        if (!deletedCategoryIds.isEmpty())
        {
            deleteCategories(deletedCategoryIds, currentModel);
        }

    }

    private void deleteCategories(List<Integer> deletedCategoryIds,
            Category2Model currentModel)
    {
        String buildInClause = DBUtils.buildInClause(deletedCategoryIds);

        List<Category2Question> categoryQuestions = getHt()
                .find("from Category2Question c where c.category.id in"
                        + buildInClause + " and c.category.model=?", currentModel);

        if (!categoryQuestions.isEmpty())
        {
            Category2 categoryToDelete = categoryQuestions.get(0).getCategory();
            for (Category2Question category2Question : categoryQuestions)
            {
                Question2 questionToDelete = category2Question.getQuestion();

                getHt().delete(category2Question);
                getHt().delete(questionToDelete);

            }
            getHt().delete(categoryToDelete);
        }
        else
        {
            List<Category2> categories = getHt()
                    .find("from Category2 c where c.id in" + buildInClause
                            + " and c.model=?", currentModel);
            for (Category2 category : categories)
            {
                getHt().delete(category);
            }
        }

    }

    private void deleteRemovedQuestions(JSONArray jsonChangedModel,
            Category2Model currentModel) throws JSONException
    {
        List<Integer> deletedQuestionIds = new ArrayList();
        for (int i = 0; i < jsonChangedModel.length(); i++)
        {
            if (!jsonChangedModel.getJSONObject(i).isNull("id"))
            {
                List<Category2> categories = getHt()
                        .find("from Category2 c2 where c2.model=?", currentModel);
                if (categories.isEmpty() != true)
                {
                    for (Category2 category : categories)
                    {
                        List<Category2Question> questions = getHt().find(
                                "from Category2Question c2q where c2q.category=? and c2q.category.model=?",
                                new Object[] { category, currentModel });

                        if (!questions.isEmpty() && jsonChangedModel
                                .getJSONObject(i).getInt("id") == questions.get(0)
                                        .getCategory().getId())
                        {
                            if (questions.size() > jsonChangedModel.getJSONObject(i)
                                    .getJSONArray("questionIds").length())
                            {
                                for (Category2Question question : questions)
                                {
                                    for (int j = 0; j <= jsonChangedModel
                                            .getJSONObject(i)
                                            .getJSONArray("questionIds")
                                            .length(); j++)
                                    {
                                        if (j == jsonChangedModel.getJSONObject(i)
                                                .getJSONArray("questionIds")
                                                .length())
                                        {
                                            deletedQuestionIds.add(
                                                    question.getQuestion().getId());
                                            getHt().delete(question);

                                        }
                                        else if (question
                                                .getId() == jsonChangedModel
                                                        .getJSONObject(i)
                                                        .getJSONArray("questionIds")
                                                        .getInt(j))
                                        {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * LOAD QUESTION EDITOR
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxLoadQuestionDialog")
    public ModelAndView ajaxLoadQuestionDialog(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = getShortCircuitView(
                "question2/category2/category2_question_editor");
        QuestionInterface question = getQuestion(request);

        if (question == null)
        {
            question = new MockQuestionEntity();
            List<? extends QuestionInterface> questions = getCategoryQuestions(
                    request);

            Question2 q = Question2Helper.createQuestion(questions, false);

            q.setUserTypes(getDefaultQuestionUserTypes(request));

            question.setQuestion(q);
        }
        else if (StringUtils.isEmpty(question.getQuestion().getJsonUserModel()))
        {
            question.getQuestion()
                    .setUserTypes(getDefaultQuestionUserTypes(request));
        }

        question.getQuestion().setFieldMappings(getFieldMappings(request));

        mv.addObject("iQuestion", question);

        if (request.getParameter("bootstrapUI") != null)
        {
            mv.addObject("bootstrapUI", true);
        }

        return mv;
    }

    /**
     * SAVE QUESTION
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxSaveQuestion")
    public ModelAndView ajaxSaveQuestion(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        // dumpRequestParameters(request);

        QuestionInterface question = getQuestion(request);
        Category2 category = Category2Helper
                .getCategory(Integer.parseInt(request.getParameter("categoryId")));
        List<? extends QuestionInterface> otherQuestions = getCategoryQuestions(
                request);

        ModelAndView mv = null;

        if (question == null)
        {
            // ADD QUESTION...

            Question2 q = Question2Helper.createQuestion(otherQuestions, true);

            q.setUserTypes(getDefaultQuestionUserTypes(request));

            question = onQuestionCreated(q, category, request);

            Question2Helper.saveQuestion(question, otherQuestions, request);

            mv = getShortCircuitView("question2/category2/category2_questionRow");
            mv.addObject("q", question);
        }
        else
        {
            if (StringUtils.isEmpty(question.getQuestion().getJsonUserModel()))
            {
                question.getQuestion()
                        .setUserTypes(getDefaultQuestionUserTypes(request));
            }

            // UPDATE QUESTION...
            Question2Helper.saveQuestion(question, otherQuestions, request);
            mv = ajaxResponse("questionUpdated");
        }

        return mv;
    }
}
