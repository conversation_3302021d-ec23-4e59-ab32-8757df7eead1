<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.question2.category2.Category2" name="Category2">
        <table name="category2_category"/>
        <attributes>


            <many-to-one name="model"/>

            <basic name="name"/>
            <basic name="position">
                <column name="position" nullable="false" />
            </basic>
            <basic name="adminOnly">
                <column name="adminOnly" nullable="false" />
            </basic>

        </attributes>
    </entity>
</entity-mappings>