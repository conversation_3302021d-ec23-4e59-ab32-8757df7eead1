<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.question2.category2.Category2Question" name="Category2Question">
        <table name="category2_category_question"/>
        <inheritance strategy="SINGLE_TABLE"/>
        <discriminator-value>com.orbis.question2.category2.Category2Question</discriminator-value>
        <discriminator-column name="questionType"/>
        <attributes>
            <basic name="position">
                <column name="position" nullable="false" />
            </basic>
            <many-to-one name="category"/>
            <many-to-one name="question"/>
        </attributes>
    </entity>
    <entity class="com.orbis.test2.Test2Question">

        <discriminator-value>com.orbis.test2.Test2Question</discriminator-value>
        <attributes>
        </attributes>
    </entity>
    <entity class="com.orbis.web.content.mentorship2.MentorQuestion2">

        <discriminator-value>com.orbis.web.content.mentorship2.MentorQuestion2</discriminator-value>
        <attributes>
        </attributes>
    </entity>
    <entity class="com.orbis.web.content.mentorship2.MenteeQuestion2">

        <discriminator-value>com.orbis.web.content.mentorship2.MenteeQuestion2</discriminator-value>
        <attributes>
        </attributes>
    </entity>
    <entity class="com.orbis.web.content.nes.NESEventQuestion">

        <discriminator-value>com.orbis.web.content.nes.NESEventQuestion</discriminator-value>
        <attributes>
            <many-to-one name="attendeeType"/>
            <basic name="deleted"/>
            <basic name="locked"/>
        </attributes>

    </entity>
</entity-mappings>