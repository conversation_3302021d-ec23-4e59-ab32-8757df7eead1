package com.orbis.question2.category2;

import org.hibernate.annotations.ColumnDefault;

import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class Category2 extends ContentItem
{
    private Category2Model model;

    private String name;

    @ColumnDefault("0")
    private int position;

    @ColumnDefault("0")
    private boolean adminOnly;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public int getPosition()
    {
        return position;
    }

    public void setPosition(int position)
    {
        this.position = position;
    }

    public boolean isAdminOnly()
    {
        return adminOnly;
    }

    public void setAdminOnly(boolean adminOnly)
    {
        this.adminOnly = adminOnly;
    }

    public String toJSONString()
    {
        return com.orbis.utils.GsonUtils.toJsonString(this, true);
    }

    public Category2Model getModel()
    {
        return model;
    }

    public void setModel(Category2Model model)
    {
        this.model = model;
    }
}
