package com.orbis.question2.category;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.orbis.utils.JSONable;

public class CategoryModel extends LinkedList<Category>
        implements JSONable<CategoryModel>
{
    public List<Integer> getQuestionIds()
    {
        List<Integer> questionIds = new LinkedList<Integer>();

        for (Category c : this)
        {
            questionIds.addAll(c.getQuestionIds());
        }

        return questionIds;
    }

    /**
     * assists in updating a questionId (originally created for dbMigrate
     * upgradePortalTo444)
     */
    public void updateQuestionIds(Map<Integer, Integer> old2new)
    {
        for (Map.Entry<Integer, Integer> old2newEntry : old2new.entrySet())
        {
            Integer oldId = old2newEntry.getKey();
            Integer newId = old2newEntry.getValue();
            for (Category c : this)
            {
                List<Integer> questionIdsToFix = c.getQuestionIds();
                Collections.replaceAll(questionIdsToFix, oldId, newId);
                c.setQuestionIds(questionIdsToFix);
            }
        }
    }
}
