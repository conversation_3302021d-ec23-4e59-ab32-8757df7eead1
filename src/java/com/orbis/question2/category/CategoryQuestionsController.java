package com.orbis.question2.category;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.orbis.web.site.SiteManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.question2.MockQuestionEntity;
import com.orbis.question2.Question2;
import com.orbis.question2.Question2Helper;
import com.orbis.question2.QuestionInterface;
import com.orbis.question2.QuestionUserType;
import com.orbis.web.OrbisInteractionController;
import com.orbis.web.OrbisModule;

public abstract class CategoryQuestionsController<Module extends OrbisModule>
        extends OrbisInteractionController<Module>
{

    public CategoryQuestionsController() {
    }

    public CategoryQuestionsController(SiteManager siteManager) {
        super(siteManager);
    }

    public CategoryQuestionsController(String homePage, SiteManager siteManager) {
        super(homePage, siteManager);
    }

    /**
     * Return an instance of QuestionInterface, based on request parameters.
     */
    public abstract QuestionInterface getQuestion(HttpServletRequest request);

    /**
     * Return a List of QuestionInterface questions that belong to a "group of
     * questions", based on request parameters.
     */
    public abstract List<? extends QuestionInterface> getCategoryQuestions(
            HttpServletRequest request);

    /**
     * Return a json category model, based on request parameters.
     */
    public abstract String getCategoryModel(HttpServletRequest request);

    /**
     * Store a json category model, based on request parameters.
     */
    public abstract void saveCategoryModel(String categoryModel,
            HttpServletRequest request);

    /**
     * Return a *new* QuestionInterface instance for the newly created Question2
     * instance, based on request parameters.
     */
    public abstract QuestionInterface onQuestionCreated(Question2 q,
            HttpServletRequest request);

    /**
     * Delete a bunch of QuestionInterface instance, based on the provided
     * 'iQuestionIds'
     */
    public abstract void deleteQuestions(List<Integer> iQuestionIds,
            HttpServletRequest request);

    /**
     * Return a default List of QuestionUserType instances, based on request
     * parameters.
     */
    public abstract List<QuestionUserType> getDefaultQuestionUserTypes(
            HttpServletRequest request);

    /**
     * Return a Map of "field mappings" that will be presented in the question
     * editor.
     */
    public abstract Map<String, String> getFieldMappings(
            HttpServletRequest request);

    /**
     * LOAD CATEGORIES UI
     * 
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("ajaxLoadCategoryEditor")
    public ModelAndView ajaxLoadCategoryEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "question2/category/category_categories");

        List<? extends QuestionInterface> questions = getCategoryQuestions(request);
        Map<String, String> fieldMappings = getFieldMappings(request);

        if (questions != null && fieldMappings != null)
        {
            for (QuestionInterface q : questions)
            {
                q.getQuestion().setFieldMappings(fieldMappings);
            }
        }

        mv.addObject("categoryMap", CategoryHelper
                .getCategoryMap(getCategoryModel(request), questions));

        return mv;
    }

    /**
     * SAVE CATEGORY MODEL
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxSaveCategoryModel")
    public ModelAndView ajaxSaveCategoryModel(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        try
        {
            String currentModel = getCategoryModel(request);
            String changedModel = request.getParameter("model");

            _deleteRemovedQuestions(currentModel, changedModel,
                    getCategoryQuestions(request), request);

            _syncQuestionPositions(changedModel, getCategoryQuestions(request));

            saveCategoryModel(changedModel, request);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return jsonBooleanResponse(SUCCESS_KEY, true);
    }

    private void _deleteRemovedQuestions(String currentModel, String changedModel,
            List<? extends QuestionInterface> categoryQuestions,
            HttpServletRequest request)
    {
        CategoryModel oldModel = new CategoryModel().fromJSONString(currentModel);

        CategoryModel newModel = new CategoryModel().fromJSONString(changedModel);

        if (oldModel != null && newModel != null)
        {
            List<Integer> oldIds = oldModel.getQuestionIds();
            List<Integer> newIds = newModel.getQuestionIds();
            List<Integer> delIds = new ArrayList<Integer>();

            for (Integer oldId : oldIds)
            {
                if (!newIds.contains(oldId))
                {
                    delIds.add(oldId);
                }
            }

            if (!delIds.isEmpty())
            {
                deleteQuestions(delIds, request);
            }
        }
    }

    private void _syncQuestionPositions(String changedModel,
            List<? extends QuestionInterface> questions)
    {
        CategoryModel model = new CategoryModel().fromJSONString(changedModel);

        int position = 1;
        for (Integer id : model.getQuestionIds())
        {
            for (QuestionInterface q : questions)
            {
                if (id.equals(q.getId()))
                {
                    Question2 question = q.getQuestion();
                    question.setPosition(position++);
                    getHt().update(question);
                    break;
                }
            }
        }
    }

    /**
     * LOAD QUESTION EDITOR
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxLoadQuestionDialog")
    public ModelAndView ajaxLoadQuestionDialog(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = getShortCircuitView(
                "question2/category/category_question_editor");
        QuestionInterface question = getQuestion(request);

        if (question == null)
        {
            question = new MockQuestionEntity();

            Question2 q = Question2Helper
                    .createQuestion(getCategoryQuestions(request), false);

            q.setUserTypes(getDefaultQuestionUserTypes(request));

            question.setQuestion(q);
        }
        else if (question.getQuestion().getJsonUserModel() == null)
        {
            question.getQuestion()
                    .setUserTypes(getDefaultQuestionUserTypes(request));
        }

        question.getQuestion().setFieldMappings(getFieldMappings(request));

        mv.addObject("iQuestion", question);
        return mv;
    }

    /**
     * SAVE QUESTION
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("ajaxSaveQuestion")
    public ModelAndView ajaxSaveQuestion(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        QuestionInterface question = getQuestion(request);
        List<? extends QuestionInterface> otherQuestions = getCategoryQuestions(
                request);

        ModelAndView mv = null;

        if (question == null)
        {
            // ADD QUESTION...

            Question2 q = Question2Helper.createQuestion(otherQuestions, true);

            q.setUserTypes(getDefaultQuestionUserTypes(request));

            question = onQuestionCreated(q, request);

            Question2Helper.saveQuestion(question, otherQuestions, request);

            mv = getShortCircuitView("question2/category/category_questionRow");
            mv.addObject("q", question);
        }
        else
        {
            if (question.getQuestion().getJsonUserModel() == null)
            {
                question.getQuestion()
                        .setUserTypes(getDefaultQuestionUserTypes(request));
            }

            // UPDATE QUESTION...
            Question2Helper.saveQuestion(question, otherQuestions, request);
            mv = ajaxResponse("questionUpdated");
        }

        return mv;
    }

}
