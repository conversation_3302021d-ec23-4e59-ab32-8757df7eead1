package com.orbis.question2.category;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.orbis.question2.QuestionInterface;

public class CategoryHelper
{
    /**
     * Returns a Category containing the List of provided questions.
     */
    public static Category getCategory(String categoryName,
            List<? extends QuestionInterface> questions)
    {
        List<Integer> questionIds = new ArrayList<Integer>();
        for (QuestionInterface q : questions)
        {
            questionIds.add(q.getId());
        }

        Category cat = new Category();
        cat.setName(categoryName);
        cat.setPosition(0);
        cat.setQuestionIds(questionIds);

        return cat;
    }

    /**
     * Returns a Map representing the "Category Model". This map is used as a
     * 'helper' to render the categories & questions in the JPSs.
     */
    public static Map<Category, List<? extends QuestionInterface>> getCategoryMap(
            String jsonModel, List<? extends QuestionInterface> questions)
    {
        Map<Category, List<? extends QuestionInterface>> ret = new LinkedHashMap<Category, List<? extends QuestionInterface>>();

        if (jsonModel != null)
        {
            CategoryModel model = new CategoryModel().fromJSONString(jsonModel);

            if (model != null)
            {
                for (Category category : model)
                {
                    List<QuestionInterface> catQuestions = new LinkedList<QuestionInterface>();

                    for (Integer qId : category.getQuestionIds())
                    {
                        for (QuestionInterface q : questions)
                        {
                            if (q.getId().equals(qId))
                            {
                                catQuestions.add(q);
                                break;
                            }
                        }
                    }

                    ret.put(category, catQuestions);
                }
            }
        }

        return ret;
    }

    public static void main(String[] args)
    {
        CategoryModel model = new CategoryModel();

        Category cat = new Category();
        cat.setName("Category A");
        cat.setPosition(0);
        cat.setQuestionIds(Arrays.asList(new Integer[] { 1, 2, 3 }));
        model.add(cat);

        cat = new Category();
        cat.setName("Category B");
        cat.setPosition(1);
        cat.setQuestionIds(Arrays.asList(new Integer[] { 4, 5, 6 }));
        model.add(cat);

        cat = new Category();
        cat.setName("Category C");
        cat.setAdminOnly(true);
        cat.setPosition(2);
        cat.setQuestionIds(Arrays.asList(new Integer[] { 7, 8, 9 }));
        model.add(cat);

        // System.out.println(model);

        String jsonModel = model.toJSONString();

        // System.out.println(jsonModel);

        CategoryModel model2 = new CategoryModel().fromJSONString(jsonModel);

        // System.out.println(model2);
    }

}
