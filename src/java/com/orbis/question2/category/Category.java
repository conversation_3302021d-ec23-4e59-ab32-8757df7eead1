package com.orbis.question2.category;

import java.util.List;

import com.orbis.utils.JSONable;

public class Category implements JSONable<Category>
{
    private String name;

    private int position;

    private boolean adminOnly;

    private List<Integer> questionIds;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public int getPosition()
    {
        return position;
    }

    public void setPosition(int position)
    {
        this.position = position;
    }

    public boolean isAdminOnly()
    {
        return adminOnly;
    }

    public void setAdminOnly(boolean adminOnly)
    {
        this.adminOnly = adminOnly;
    }

    public List<Integer> getQuestionIds()
    {
        return questionIds;
    }

    public void setQuestionIds(List<Integer> questionIds)
    {
        this.questionIds = questionIds;
    }
}
