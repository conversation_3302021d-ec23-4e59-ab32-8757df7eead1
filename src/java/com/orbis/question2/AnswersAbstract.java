package com.orbis.question2;

import com.orbis.importExport.EntityImportExportAnswer;
import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;

/**
 * Entities that have a relationship to QuestionAnswers should (typically)
 * extend this class.
 * 
 * <AUTHOR>
 */

@Access(value = AccessType.FIELD)
@MappedSuperclass
public abstract class AnswersAbstract extends ContentItem
        implements AnswersInterface, EntityImportExportAnswer
{
    // *** ANSWERS STORED HERE ***
    @ManyToOne
    private QuestionAnswers questionAnswers;

    public QuestionAnswers getQuestionAnswers()
    {
        return questionAnswers;
    }

    public void setQuestionAnswers(QuestionAnswers questionAnswers)
    {
        this.questionAnswers = questionAnswers;
    }
}
