package com.orbis.question2;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.annotations.ColumnDefault;
import org.json.JSONArray;

import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.orbis.utils.GsonUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.ContentItem;

import jakarta.persistence.Transient;

public class Question2 extends ContentItem
{
    public static final int TYPE_TEXT = 0;

    public static final int TYPE_INTEGER = 1;

    public static final int TYPE_MULTI_CHOICE = 2;

    public static final int TYPE_BOOLEAN = 3;

    public static final int TYPE_DATE = 4;

    public static final int TYPE_LARGE_TEXT = 5;

    public static final int TYPE_RATING = 6;

    public static final int TYPE_FILE_UPLOAD = 7;

    public static final int TYPE_SINGLE_CHOICE = 8;

    public static final int TYPE_TREE = 9;

    public static final int TYPE_MATRIX_SINGLE = 10;

    public static final int TYPE_MATRIX_MULTI = 11;

    public static final int NUMBER_DISPLAY_DROPDOWN = 0;

    public static final int NUMBER_DISPLAY_TEXTBOX = 1;

    /**
     * the value NA is stored as in the db, used in queries for rating type
     * questions
     */
    public static final int RATING_NA_DB_VALUE = -1;

    /**
     * The "question type" code (eg: TYPE_TEXT, TYPE_MULTI_CHOICE, etc)
     */
    @ColumnDefault("0")
    private int type;

    /**
     * When TRUE, the "question type" cannot be changed in the question-editor
     * (it is read-only).
     */
    @ColumnDefault("0")
    private boolean typeLocked = false;

    /**
     * The "text of the question that is asked" from which the user will answer
     * (eg: What is your name?)
     */
    private String questionText;

    /**
     * When TRUE, the "question text" cannot be changed in the question-editor
     * (it is read-only).
     */
    @ColumnDefault("0")
    private boolean questionTextLocked = false;

    /**
     * Additional text that will be displayed next to the "question text". This
     * can be used to describe/instruct the user on how to answer the question.
     */
    private String description;

    /**
     * When listing questions in a UI, this property should be used to control
     * the question's "position" in that list.
     */
    @ColumnDefault("0")
    private int position = 0;

    /**
     * NOTE: 'adminOnly' is not processed by the core Question Framework. It is
     * up to the client code to regard/enforce this property.
     */
    @ColumnDefault("0")
    private boolean adminOnly = true;

    @ColumnDefault("0")
    private boolean required = false;

    @ColumnDefault("0")
    private int min;

    @ColumnDefault("0")
    private int max;

    @ColumnDefault("0")
    private int displayType = NUMBER_DISPLAY_DROPDOWN;

    @ColumnDefault("0")
    private boolean usingNA = false;

    /**
     * Used to store the "choices" (one per line) in TYPE_MULTI_CHOICE
     * questions.
     */
    private String choices;

    @ColumnDefault("0")
    private boolean includeOther = false;

    /**
     * Limits how many multiple choice answers a user can select
     */
    @ColumnDefault("0")
    private int limitAnswer = 0;

    /**
     * Stores the "json tree structure" in TYPE_TREE questions.
     */
    private String treeNodes;

    /**
     * When TRUE the answerer may only select a single tree node. When FALSE the
     * answerer may select multiple tree nodes.
     */
    @ColumnDefault("0")
    private boolean singleTreePath;

    /**
     * A "\r\n" delimited list of "matrix column labels"
     */
    private String matrixColumns;

    /**
     * A "\r\n" delimited list of "matrix row labels"
     */
    private String matrixRows;

    /**
     * Max file-size (in bytes) of the file that the answerer uploads in the
     * TYPE_FILE_UPLOAD question.
     */
    @ColumnDefault("-1")
    private long maxUploadSize = -1L;

    private String answerField1;

    private String answerField2;

    private String answerField3;

    private String answerField4;

    private String answerField5;

    private String jsonUserModel;

    private String mappingKey;

    /** @transient used for validation */
    @Transient
    private boolean validated = false;

    /** @transient used to display "field mappings" in question-editor */
    @Transient
    private Map<String, String> fieldMappings;

    @Transient
    public boolean isAnswerFieldsSet()
    {
        boolean ret = false;

        switch (type)
        {
            case TYPE_BOOLEAN:
            case TYPE_DATE:
            case TYPE_INTEGER:
            case TYPE_RATING:
            case TYPE_LARGE_TEXT:
            case TYPE_MULTI_CHOICE:
            case TYPE_SINGLE_CHOICE:
            case TYPE_TEXT:
            case TYPE_TREE:
            case TYPE_MATRIX_MULTI:
            case TYPE_MATRIX_SINGLE:
                ret = answerField1 != null;
                break;

            case TYPE_FILE_UPLOAD:
                ret = answerField1 != null && answerField2 != null;
                break;

            default:
                throw new RuntimeException("Unknown question type");
        }

        return ret;
    }

    public String toString()
    {
        return this.questionText;
    }

    @Transient
    public List<String> getChoiceList()
    {
        return choices != null ? Arrays.asList(choices.split("\r\n"))
                : new ArrayList<String>();
    }

    @Transient
    public String getChoiceJSON()
    {
        return new JSONArray(getChoiceList()).toString();
    }

    public String getQuestionText()
    {
        return questionText;
    }

    @Transient
    public String getTypeLabel()
    {
        return Question2Helper.getQuestionTypeLabel(this.type);
    }

    public void setQuestionText(String questionText)
    {
        this.questionText = questionText;
    }

    public boolean isAdminOnly()
    {
        return adminOnly;
    }

    public void setAdminOnly(boolean adminOnly)
    {
        this.adminOnly = adminOnly;
    }

    public boolean isRequired()
    {
        return required;
    }

    public void setRequired(boolean required)
    {
        this.required = required;
    }

    public int getPosition()
    {
        return position;
    }

    public void setPosition(int position)
    {
        this.position = position;
    }

    @Transient
    public boolean isMatrixSingle()
    {
        return type == TYPE_MATRIX_SINGLE;
    }

    @Transient
    public boolean isMatrixMulti()
    {
        return type == TYPE_MATRIX_MULTI;
    }

    @Transient
    public boolean isSingleChoice()
    {
        return type == TYPE_SINGLE_CHOICE;
    }

    @Transient
    public boolean isMultiChoice()
    {
        return type == TYPE_MULTI_CHOICE;
    }

    @Transient
    public boolean isBoolean()
    {
        return type == TYPE_BOOLEAN;
    }

    @Transient
    public boolean isLargeText()
    {
        return type == TYPE_LARGE_TEXT;
    }

    @Transient
    public boolean isText()
    {
        return type == TYPE_TEXT;
    }

    @Transient
    public boolean isTree()
    {
        return type == TYPE_TREE;
    }

    @Transient
    public boolean isFileUpload()
    {
        return type == TYPE_FILE_UPLOAD;
    }

    @Transient
    public boolean isRating()
    {
        return type == TYPE_RATING;
    }

    @Transient
    public boolean isInteger()
    {
        return type == TYPE_INTEGER;
    }

    @Transient
    public boolean isDate()
    {
        return type == TYPE_DATE;
    }

    public int getType()
    {
        return type;
    }

    public void setType(int type)
    {
        this.type = type;
    }

    public int getMin()
    {
        return min;
    }

    public void setMin(int min)
    {
        this.min = min;
    }

    public int getMax()
    {
        return max;
    }

    public void setMax(int max)
    {
        this.max = max;
    }

    public String getChoices()
    {
        return choices;
    }

    public void setChoices(String choices)
    {
        this.choices = choices;
    }

    public String getDescription()
    {
        return description;
    }

    @Transient
    public String getDescriptionAsHtml()
    {
        if (description == null)
        {
            return "";
        }

        return description.replaceAll("\n", "<BR>");
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public long getMaxUploadSize()
    {
        return maxUploadSize;
    }

    public void setMaxUploadSize(long maxUploadSize)
    {
        this.maxUploadSize = maxUploadSize;
    }

    public String getAnswerField1()
    {
        return answerField1;
    }

    public void setAnswerField1(String answerField1)
    {
        this.answerField1 = answerField1;
    }

    public String getAnswerField2()
    {
        return answerField2;
    }

    public void setAnswerField2(String answerField2)
    {
        this.answerField2 = answerField2;
    }

    public String getAnswerField3()
    {
        return answerField3;
    }

    public void setAnswerField3(String answerField3)
    {
        this.answerField3 = answerField3;
    }

    public String getAnswerField4()
    {
        return answerField4;
    }

    public void setAnswerField4(String answerField4)
    {
        this.answerField4 = answerField4;
    }

    public String getAnswerField5()
    {
        return answerField5;
    }

    public void setAnswerField5(String answerField5)
    {
        this.answerField5 = answerField5;
    }

    public boolean isUsingNA()
    {
        return usingNA;
    }

    public void setUsingNA(boolean usingNA)
    {
        this.usingNA = usingNA;
    }

    /** jsp helper */
    @Transient
    public int getRatingNaDbValue()
    {
        return RATING_NA_DB_VALUE;
    }

    /** validation helper */
    @Transient
    public boolean isValidated()
    {
        return this.validated;
    }

    /**
     * validation helper: used to mark this questions Answer as valid, and thus
     * skip the normal checking procedures. (originally used for
     * TYPE_FILE_UPLOAD)
     */
    public void setValidated(boolean bool)
    {
        this.validated = bool;
    }

    public boolean isSingleTreePath()
    {
        return singleTreePath;
    }

    public void setSingleTreePath(boolean singleTreePath)
    {
        this.singleTreePath = singleTreePath;
    }

    public String getTreeNodes()
    {
        return treeNodes;
    }

    public void setTreeNodes(String treeNodes)
    {
        this.treeNodes = treeNodes;
    }

    /**
     * Returns a List<QuestionUserType>, which is a deserialized representation
     * of 'jsonUserModel'
     */
    @Transient
    public List<QuestionUserType> getUserTypes()
    {
        List<QuestionUserType> ret = null;

        if (this.jsonUserModel != null)
        {
            try
            {
                Type collectionType = new TypeToken<List<QuestionUserType>>()
                {
                }.getType();

                ret = new GsonBuilder().create().fromJson(this.jsonUserModel,
                        collectionType);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        return ret;
    }

    /**
     * Stores the provided List<QuestionUserType> as a json serialization (aka:
     * jsonUserModel)
     */
    public void setUserTypes(List<QuestionUserType> userTypes)
    {
        this.jsonUserModel = GsonUtils.toJsonString(userTypes);
    }

    public String getJsonUserModel()
    {
        return jsonUserModel;
    }

    public void setJsonUserModel(String jsonUserModel)
    {
        this.jsonUserModel = jsonUserModel;
    }

    @Transient
    public Map<String, QuestionUserType> getUserPermissions()
    {
        Map<String, QuestionUserType> map = new HashMap<String, QuestionUserType>();

        List<QuestionUserType> userTypes = getUserTypes();

        if (userTypes != null)
        {
            for (QuestionUserType userType : getUserTypes())
            {
                map.put(userType.getUserTypeKey(), userType);
            }
        }

        return map;
    }

    public String getMappingKey()
    {
        return mappingKey;
    }

    public void setMappingKey(String mappingKey)
    {
        this.mappingKey = mappingKey;
    }

    public void setFieldMappings(Map<String, String> fieldMappings)
    {
        this.fieldMappings = fieldMappings;
    }

    @Transient
    public Map<String, String> getFieldMappings()
    {
        return fieldMappings;
    }

    public boolean isIncludeOther()
    {
        return includeOther;
    }

    public void setIncludeOther(boolean includeOther)
    {
        this.includeOther = includeOther;
    }

    public int getLimitAnswer()
    {
        return limitAnswer;
    }

    public void setLimitAnswer(int limitAnswer)
    {
        this.limitAnswer = limitAnswer;
    }

    public String getMatrixColumns()
    {
        return matrixColumns;
    }

    public void setMatrixColumns(String matrixColumns)
    {
        this.matrixColumns = matrixColumns;
    }

    public String getMatrixRows()
    {
        return matrixRows;
    }

    public void setMatrixRows(String matrixRows)
    {
        this.matrixRows = matrixRows;
    }

    /**
     * A JSP helper method for matrix-type questions...
     */
    @Transient
    public List<String> getMatrixRowLabels()
    {
        List<String> matrixRowLabels = new ArrayList<String>();

        if ((this.type == TYPE_MATRIX_MULTI || this.type == TYPE_MATRIX_SINGLE)
                && this.matrixRows != null)
        {
            matrixRowLabels = StringUtils.listify(this.matrixRows, "\r\n");
        }

        return matrixRowLabels;
    }

    /**
     * A JSP helper method for matrix-type questions...
     */
    @Transient
    public List<String> getMatrixColumnLabels()
    {
        List<String> matrixColumnLabels = new ArrayList<String>();

        if ((this.type == TYPE_MATRIX_MULTI || this.type == TYPE_MATRIX_SINGLE)
                && this.matrixColumns != null)
        {
            matrixColumnLabels = StringUtils.listify(this.matrixColumns, "\r\n");
        }

        return matrixColumnLabels;
    }

    public boolean isTypeLocked()
    {
        return typeLocked;
    }

    public void setTypeLocked(boolean typeLocked)
    {
        this.typeLocked = typeLocked;
    }

    public boolean isQuestionTextLocked()
    {
        return questionTextLocked;
    }

    public void setQuestionTextLocked(boolean questionTextLocked)
    {
        this.questionTextLocked = questionTextLocked;
    }

    public int getDisplayType()
    {
        return displayType;
    }

    public void setDisplayType(int displayType)
    {
        this.displayType = displayType;
    }
}
