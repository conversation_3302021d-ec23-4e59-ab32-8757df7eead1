package com.orbis.question2;

import java.util.ArrayList;
import java.util.List;

import com.orbis.utils.I18nLabel;

public class Question2Exception extends Exception
{
    private List<I18nLabel> errors;

    public Question2Exception(List<I18nLabel> errors)
    {
        super();
        this.errors = errors;
    }

    public Question2Exception(I18nLabel error)
    {
        super();
        this.errors = new ArrayList<I18nLabel>();
        this.errors.add(error);
    }

    public List<I18nLabel> getErrors()
    {
        return errors;
    }

}
