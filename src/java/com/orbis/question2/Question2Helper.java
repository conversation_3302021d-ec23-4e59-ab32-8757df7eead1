package com.orbis.question2;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONArray;
import com.orbis.portal.CommandQueryTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.question2.category2.Category2;
import com.orbis.question2.category2.Category2Helper;
import com.orbis.question2.category2.Category2Model;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.utils.DateUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.OrbisModule;
import com.orbis.web.content.fp.FPModule;

public class Question2Helper
{
    private static final Log logger = LogFactory.getLog(Question2Helper.class);

    public static final String UPLOAD_FOLDER = "/content/documents/question_uploads/";

    public static Question2 createQuestion(
            List<? extends QuestionInterface> otherQuestions, boolean save)
            throws Question2Exception
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Question2 question = new Question2();

        // fails if capacity is full...
        setInitialType(question, otherQuestions);

        question.setAdminOnly(false);
        question.setRequired(false);

        if (save)
        {
            ht.save(question);

            question.setQuestionText("Question " + question.getId());
            question.setPosition(question.getId());

            ht.update(question);
        }
        else
        {
            question.setQuestionText("Question "
                    + (otherQuestions == null ? "1" : otherQuestions.size() + 1));
            question.setPosition(
                    otherQuestions == null ? 0 : otherQuestions.size());
        }

        return question;
    }

    public static Question2 createQuestion(int type,
            List<? extends QuestionInterface> otherQuestions, boolean save)
            throws Question2Exception
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Question2 question = new Question2();

        question.setType(type);

        // fails if type-capacity is full...
        updateAnswerMappings(question, otherQuestions);

        question.setAdminOnly(false);
        question.setRequired(false);

        if (save)
        {
            ht.save(question);

            question.setQuestionText("Question " + question.getId());
            question.setPosition(question.getId());

            ht.update(question);
        }
        else
        {
            question.setQuestionText("Question "
                    + (otherQuestions == null ? "0" : otherQuestions.size()));
            question.setPosition(
                    otherQuestions == null ? 0 : otherQuestions.size());
        }

        return question;
    }

    private static void setInitialType(Question2 question,
            List<? extends QuestionInterface> otherQuestions)
            throws Question2Exception
    {
        try
        {
            question.setType(Question2.TYPE_TEXT);
            updateAnswerMappings(question, otherQuestions);
        }
        catch (Question2Exception e1)
        {
            try
            {
                question.setType(Question2.TYPE_BOOLEAN);
                updateAnswerMappings(question, otherQuestions);
            }
            catch (Question2Exception e2)
            {
                try
                {
                    question.setType(Question2.TYPE_DATE);
                    updateAnswerMappings(question, otherQuestions);
                }
                catch (Question2Exception e3)
                {
                    try
                    {
                        question.setType(Question2.TYPE_INTEGER);
                        updateAnswerMappings(question, otherQuestions);
                    }
                    catch (Question2Exception e4)
                    {
                        try
                        {
                            question.setType(Question2.TYPE_LARGE_TEXT);
                            updateAnswerMappings(question, otherQuestions);
                        }
                        catch (Question2Exception e5)
                        {
                            throw e5;
                        }
                    }
                }
            }
        }
    }

    public static void saveQuestion(QuestionInterface iQuestion,
            List<? extends QuestionInterface> otherQuestions,
            HttpServletRequest request) throws Question2Exception
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Question2 question = iQuestion.getQuestion();
        List<I18nLabel> errors = new ArrayList<I18nLabel>();

        if (question == null)
        {
            throw new RuntimeException("null Question");
        }

        question.setAdminOnly(request.getParameter("adminOnly") != null);
        question.setRequired(request.getParameter("required") != null);
        question.setDescription(request.getParameter("description"));

        if (!question.isQuestionTextLocked())
        {
            question.setQuestionText(request.getParameter("questionText"));
        }

        int type = question.getType();

        if (!question.isTypeLocked())
        {
            try
            {
                type = Integer.parseInt(request.getParameter("type"));
            }
            catch (Exception e)
            {
                errors.add(
                        new I18nLabel("i18n.Question2Helper.invalidQuestionType"));
                throw new Question2Exception(errors);
            }
        }

        if (!question.isAnswerFieldsSet() || question.getType() != type)
        {
            question.setType(type);
            question.setAnswerField1(null);
            question.setAnswerField2(null);
            question.setAnswerField3(null);
            question.setAnswerField4(null);
            question.setAnswerField5(null);
            updateAnswerMappings(question, otherQuestions);
        }

        int min = 0;
        int max = 0;
        int displayType = 0;
        boolean usingNA = false;
        String choices = null;
        int limitAnswer = 0;
        String treeNodes = null;
        boolean singleTreePath = false;
        boolean includeOther = false;
        String matrixColumns = null;
        String matrixRows = null;

        switch (type)
        {
            case Question2.TYPE_TEXT:
            case Question2.TYPE_LARGE_TEXT:
            case Question2.TYPE_BOOLEAN:
            case Question2.TYPE_DATE:
            case Question2.TYPE_FILE_UPLOAD:
                break;

            case Question2.TYPE_INTEGER:
                try
                {
                    min = Integer.parseInt(request.getParameter("numberMin"));
                    max = Integer.parseInt(request.getParameter("numberMax"));
                    displayType = Integer
                            .parseInt(request.getParameter("displayType"));
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                break;

            case Question2.TYPE_MULTI_CHOICE:
                includeOther = !StringUtils
                        .isEmpty(request.getParameter("multiChoiceMultiOther"));
                choices = request.getParameter("multiChoices");
                limitAnswer = StringUtils
                        .isInteger(request.getParameter("limitAnswer"))
                                ? Integer.valueOf(request.getParameter("limitAnswer"))
                                        .intValue()
                                : 0;
                break;

            case Question2.TYPE_SINGLE_CHOICE:
                includeOther = !StringUtils
                        .isEmpty(request.getParameter("multiChoiceSingleOther"));
                choices = request.getParameter("singleChoices");
                break;

            case Question2.TYPE_RATING:
                try
                {
                    min = Integer.parseInt(request.getParameter("ratingMin"));
                    max = Integer.parseInt(request.getParameter("ratingMax"));
                    if (min < 0 || min > max || Math.abs(max - min) > 100)
                    {
                        // remember we store NA as -1 when using question2
                        // framework
                        errors.add(new I18nLabel(
                                "i18n.Question2Helper.invalidMinMax"));
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                usingNA = request.getParameter("ratingUsingNA") != null;
                break;

            case Question2.TYPE_TREE:
                treeNodes = request.getParameter("treeNodes");
                singleTreePath = request.getParameter("singleTreePath") != null;
                break;

            case Question2.TYPE_MATRIX_MULTI:
                matrixColumns = request.getParameter("matrixMultiColumns");
                matrixRows = request.getParameter("matrixMultiRows");
                break;

            case Question2.TYPE_MATRIX_SINGLE:
                matrixColumns = request.getParameter("matrixSingleColumns");
                matrixRows = request.getParameter("matrixSingleRows");
                break;

            default:
                throw new RuntimeException("unknown question type: " + type);
        }

        question.setMax(max);
        question.setMin(min);
        question.setDisplayType(displayType);
        question.setUsingNA(usingNA);
        question.setChoices(choices);
        question.setLimitAnswer(limitAnswer);
        question.setIncludeOther(includeOther);
        question.setTreeNodes(treeNodes);
        question.setSingleTreePath(singleTreePath);
        question.setMappingKey(request.getParameter("mappingKey"));
        question.setMatrixColumns(matrixColumns);
        question.setMatrixRows(matrixRows);

        List<QuestionUserType> userTypes = question.getUserTypes();

        if (userTypes != null)
        {
            for (QuestionUserType userType : userTypes)
            {
                userType.setCanRead(request.getParameter(
                        userType.getUserTypeKey() + "_canRead") != null);
                userType.setCanWrite(request.getParameter(
                        userType.getUserTypeKey() + "_canWrite") != null);
                userType.setCanSearch(request.getParameter(
                        userType.getUserTypeKey() + "_canSearch") != null);
            }

            question.setUserTypes(userTypes);
        }

        if (errors.size() == 0)
        {
            ht.saveOrUpdate(question);
            ht.saveOrUpdate(iQuestion);
        }
        else
        {
            throw new Question2Exception(errors);
        }
    }

    public static void updateAnswerMappings(Question2 question,
            List<? extends QuestionInterface> otherQuestions)
            throws Question2Exception
    {
        if (question != null)
        {
            List<String> usedAnswerFields = new ArrayList<String>();

            if (otherQuestions != null && !otherQuestions.isEmpty())
            {
                // DETERMINE LIST OF 'USED' ANSWER FIELDS (FROM OTHER
                // QUESTIONS)...
                for (QuestionInterface q : otherQuestions)
                {

                    if (question.getId() == null
                            || !question.getId().equals(q.getQuestion().getId()))
                    {
                        usedAnswerFields.add(q.getQuestion().getAnswerField1());
                        usedAnswerFields.add(q.getQuestion().getAnswerField2());
                        usedAnswerFields.add(q.getQuestion().getAnswerField3());
                        usedAnswerFields.add(q.getQuestion().getAnswerField4());
                        usedAnswerFields.add(q.getQuestion().getAnswerField5());
                    }
                }
            }

            // SET 'UNUSED' ANSWER FIELD(S) TO QUESTION...
            String answerField = null;
            switch (question.getType())
            {
                case Question2.TYPE_TEXT:
                case Question2.TYPE_SINGLE_CHOICE:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case Question2.TYPE_INTEGER:
                case Question2.TYPE_RATING:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case Question2.TYPE_BOOLEAN:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "b" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case Question2.TYPE_DATE:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "d" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case Question2.TYPE_MULTI_CHOICE:
                case Question2.TYPE_LARGE_TEXT:
                case Question2.TYPE_TREE:
                case Question2.TYPE_MATRIX_MULTI:
                case Question2.TYPE_MATRIX_SINGLE:
                    int maxTFields = getMaxPermittedLargeTextFields();
                    for (int i = 1; i <= maxTFields; i++)
                    {
                        answerField = "t" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case Question2.TYPE_FILE_UPLOAD:
                    // answerField1 will contain the "File Name"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "File URL"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;

                default:
                    throw new RuntimeException("Unknown question type");
            }

            if (!question.isAnswerFieldsSet())
            {
                throw new Question2Exception(new I18nLabel(
                        "i18n.Question2Helper.questionCapacityExceeded"));
            }
        }
    }

    public static void bindAnswers(List<? extends QuestionInterface> questions,
            AnswersInterface answerEntity, boolean save, String userTypeKey,
            HttpServletRequest request)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        if (questions == null || answerEntity == null || request == null
                || ht == null)
        {
            throw new IllegalArgumentException();
        }

        QuestionAnswers questionAnswers = answerEntity.getQuestionAnswers();

        if (questionAnswers == null)
        {
            questionAnswers = createQuestionAnswers(answerEntity.getClass(), save);
            answerEntity.setQuestionAnswers(questionAnswers);
            if (save)
            {
                ht.update(answerEntity);
            }
        }
        else if (questionAnswers.getId() == null && save)
        {
            ht.saveOrUpdate(questionAnswers);
        }

        for (QuestionInterface q : questions)
        {
            Map<String, QuestionUserType> userPermissions = q.getQuestion()
                    .getUserPermissions();

            if (userTypeKey == null
                    || (userTypeKey != null && userPermissions != null
                            && userPermissions.get(userTypeKey) != null
                            && userPermissions.get(userTypeKey).isCanWrite()))
            {
                bindAnswer(q, questionAnswers, save, request);
            }
        }

        if (save)
        {
            ht.update(questionAnswers);
        }
    }

    public static void bindAnswer(QuestionInterface iQuestion,
            QuestionAnswers questionAnswers, boolean save,
            HttpServletRequest request)
    {
        Question2 question = iQuestion.getQuestion();

        if (question == null || questionAnswers == null || request == null)
        {
            throw new IllegalArgumentException();
        }

        switch (question.getType())
        {
            case Question2.TYPE_TEXT:
            case Question2.TYPE_LARGE_TEXT:
                bindAnswer_Text(request, questionAnswers, question);
                break;

            case Question2.TYPE_INTEGER:
                bindAnswer_Integer(request, questionAnswers, question);
                break;

            case Question2.TYPE_RATING:
                bindAnswer_Rating(request, questionAnswers, question);
                break;

            case Question2.TYPE_BOOLEAN:
                bindAnswer_Boolean(request, questionAnswers, question);
                break;

            case Question2.TYPE_DATE:
                bindAnswer_Date(request, questionAnswers, question);
                break;

            case Question2.TYPE_MULTI_CHOICE:
                bindAnswer_ChoiceMulti(request, questionAnswers, question);
                break;

            case Question2.TYPE_SINGLE_CHOICE:
                bindAnswer_ChoiceSingle(request, questionAnswers, question);
                break;

            case Question2.TYPE_FILE_UPLOAD:
                bindAnswer_FileUpload(question, questionAnswers, save, request);
                break;

            case Question2.TYPE_TREE:
                bindAnswer_Tree(request, questionAnswers, question);
                break;

            case Question2.TYPE_MATRIX_MULTI:
                bindAnswer_MatrixMulti(request, questionAnswers, question);
                break;

            case Question2.TYPE_MATRIX_SINGLE:
                bindAnswer_MatrixSingle(request, questionAnswers, question);
                break;

            default:
                throw new RuntimeException("Unknown question type");
        }

    }

    private static void bindAnswer_Boolean(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        Boolean b = getBooleanValue(
                request.getParameter("question_" + question.getId().toString()));
        bindValue(questionAnswers, question.getAnswerField1(), b);
    }

    private static void bindAnswer_Date(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        Date date = DateUtils.getDatepickerVal(request,
                "question_" + question.getId().toString());
        bindValue(questionAnswers, question.getAnswerField1(), date);
    }

    private static void bindAnswer_Integer(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String number = request
                .getParameter("question_" + question.getId().toString());
        if (StringUtils.isInteger(number))
        {
            bindValue(questionAnswers, question.getAnswerField1(),
                    Integer.valueOf(number));
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_Text(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String text = request
                .getParameter("question_" + question.getId().toString());
        if (StringUtils.isEmpty(text))
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), text);
        }
    }

    private static void bindAnswer_Rating(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String rating = request
                .getParameter("question_" + question.getId().toString());
        Integer ratingAsInt = (StringUtils.isInteger(rating) ? Integer.valueOf(rating)
                : null);
        if (ratingAsInt != null && (ratingAsInt.intValue() <= question.getMax()
                && ratingAsInt.intValue() >= question.getMin()
                || ratingAsInt.intValue() == Question2.RATING_NA_DB_VALUE))
        {
            bindValue(questionAnswers, question.getAnswerField1(), ratingAsInt);
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_ChoiceMulti(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String choices[] = request
                .getParameterValues("question_" + question.getId().toString());
        if (choices != null && choices.length > 0)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < choices.length; i++)
            {
                sb.append("^").append(choices[i]);
            }

            if (StringUtils.isEmpty(sb.toString()))
            {
                bindValue(questionAnswers, question.getAnswerField1(), null);
            }
            else
            {
                sb.append("^");
                bindValue(questionAnswers, question.getAnswerField1(),
                        sb.toString());
            }
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_ChoiceSingle(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        if (StringUtils.isEmpty(
                request.getParameter("question_" + question.getId().toString())))
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), request
                    .getParameter("question_" + question.getId().toString()));
        }
    }

    private static void bindAnswer_Tree(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String treeAnswer = request
                .getParameter("question_" + question.getId().toString());
        if (StringUtils.isEmpty(treeAnswer))
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), treeAnswer);
        }
    }

    private static void bindAnswer_MatrixSingle(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        Map<String, Map<String, Boolean>> matrixMap = getMatrixMap(questionAnswers,
                question);

        Object[] matrixSet = matrixMap.entrySet().toArray();

        StringBuilder sb = new StringBuilder();

        for (int rowIndex = 0; rowIndex < matrixSet.length; rowIndex++)
        {
            if (!StringUtils.isEmpty(request.getParameter(
                    "question_" + question.getId().toString() + "_" + rowIndex)))
            {
                int colIndex = Integer.valueOf(request.getParameter("question_"
                        + question.getId().toString() + "_" + rowIndex));

                Entry rowEntry = (Entry) matrixSet[rowIndex];
                String rowLabel = (String) rowEntry.getKey();
                Map<String, Boolean> subMap = (Map<String, Boolean>) rowEntry
                        .getValue();
                Object[] subMapSet = subMap.entrySet().toArray();
                Entry subEntry = (Entry) subMapSet[colIndex];
                String colLabel = (String) subEntry.getKey();
                sb.append("^").append(rowLabel + "~" + colLabel);
            }
        }

        if (StringUtils.isEmpty(sb.toString()))
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
        else
        {
            sb.append("^");
            bindValue(questionAnswers, question.getAnswerField1(), sb.toString());
        }
    }

    private static void bindAnswer_MatrixMulti(HttpServletRequest request,
            QuestionAnswers questionAnswers, Question2 question)
    {
        String multiMatrix[] = request
                .getParameterValues("question_" + question.getId().toString());

        if (multiMatrix != null && multiMatrix.length > 0)
        {
            Map<String, Map<String, Boolean>> matrixMap = getMatrixMap(
                    questionAnswers, question);

            Object[] matrixSet = matrixMap.entrySet().toArray();

            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < multiMatrix.length; i++)
            {
                String value = multiMatrix[i];

                int rowIndex = Integer
                        .valueOf(value.substring(0, value.indexOf("_")));

                int colIndex = Integer
                        .valueOf(value.substring(value.indexOf("_") + 1));

                Entry entry = (Entry) matrixSet[rowIndex];
                String rowLabel = (String) entry.getKey();
                Map<String, Boolean> subMap = (Map<String, Boolean>) entry
                        .getValue();
                Object[] subMapSet = subMap.entrySet().toArray();
                Entry subEntry = (Entry) subMapSet[colIndex];
                String colLabel = (String) subEntry.getKey();
                sb.append("^").append(rowLabel + "~" + colLabel);
            }

            if (StringUtils.isEmpty(sb.toString()))
            {
                bindValue(questionAnswers, question.getAnswerField1(), null);
            }
            else
            {
                sb.append("^");
                bindValue(questionAnswers, question.getAnswerField1(),
                        sb.toString());
            }
        }
        else
        {
            bindValue(questionAnswers, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_FileUpload(Question2 question,
            QuestionAnswers questionAnswers, boolean save,
            HttpServletRequest request)
    {
        OrbisModule module = OrbisController.getOrbisModule(request);

        if (module == null || !(module instanceof FPModule))
        {
            String fileUuid = request.getParameter("question_" + question.getId());
            boolean newFile = false;
            boolean removeFile = false;

            // Cancel binding for 'profileImage' fields as they will be handled
            // differently
            if ("profileImage".equals(question.getAnswerField1()))
            {
                return;
            }

            if (isAnswered(question, questionAnswers))
            {
                removeFile = StringUtils.isEmpty(fileUuid)
                        || fileUuid.length() != 32;
                if (fileUuid != null && !StringUtils.isEmpty(fileUuid))
                {
                    try
                    {
                        newFile = !removeFile && !fileUuid
                                .equals(PropertyUtils.getProperty(questionAnswers,
                                        question.getAnswerField2()));
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
                if (removeFile)
                {
                    question.setValidated(true);
                }
            }
            else
            {
                newFile = fileUuid != null && fileUuid.length() == 32;
            }

            if (newFile)
            {
                String filePath = FilePathUtils.getFilePathUrlForUUID(fileUuid,
                        true);
                if (filePath != null)
                {
                    String fileName = filePath
                            .substring(filePath.lastIndexOf('/') + 1);
                    bindValue(questionAnswers, question.getAnswerField1(),
                            fileName);
                    bindValue(questionAnswers, question.getAnswerField2(),
                            fileUuid);
                }
            }
            else if (removeFile)
            {
                bindValue(questionAnswers, question.getAnswerField1(), null);
                bindValue(questionAnswers, question.getAnswerField2(), null);
            }
        }
        // Exception for FP which uses old FileUpload code (remove when FP is
        // fully deprecated)
        else
        {
            boolean uploadTheFile = true;
            if (isAnswered(question, questionAnswers))
            {
                if (!FileUtils.doesRequestHaveFile(request,
                        "question_" + question.getId().toString())
                        && !Boolean.valueOf(request.getParameter(
                                "removeFile" + question.getId().toString())))
                {
                    question.setValidated(true);
                    uploadTheFile = false;
                }
            }

            if (uploadTheFile)
            {
                MultipartFile uploadedFile = FileUtils.getUploadedFile(request,
                        "question_" + question.getId().toString());

                if (FileUtils.validateUpload(uploadedFile, null, true,
                        question.getMaxUploadSize()).isEmpty())
                {
                    if (save)
                    {
                        String fileUrl = saveUploadedFile(uploadedFile,
                                request.getSession().getServletContext());

                        bindValue(questionAnswers, question.getAnswerField1(),
                                uploadedFile.getOriginalFilename());

                        bindValue(questionAnswers, question.getAnswerField2(),
                                fileUrl);
                    }
                    else
                    {
                        // because we dont want to save the fileupload (in this
                        // case, it's probably a mock object for validation)
                        // we mark the question as valid, which is used to
                        // override any futher validation checks
                        question.setValidated(true);
                    }
                }
                else
                {
                    bindValue(questionAnswers, question.getAnswerField1(), null);
                    bindValue(questionAnswers, question.getAnswerField2(), null);
                }
            }
        }
    }

    private static String saveUploadedFile(MultipartFile uploadedFile,
            ServletContext servletContext)
    {
        String fileUrl = null;

        try
        {
            String rootPath = PortalUtils.getRealPath("/");
            String urlPath = UPLOAD_FOLDER + FileUtils.getDateFolder()
                    + FileUtils.generateRandomFileOrFolderName() + "/";

            String fullPath = FileUtils.fixFileName(rootPath + urlPath);
            String fileName = FileUtils.sanitizeFileName(
                    uploadedFile.getOriginalFilename(), 255 - urlPath.length());

            // create upload directory...
            File uploadDir = new File(fullPath);
            uploadDir.mkdirs();

            File saveFile = new File(fullPath + fileName);

            uploadedFile.transferTo(saveFile);

            fileUrl = urlPath + fileName;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return fileUrl;
    }

    public static Boolean getBooleanValue(String str)
    {
        Boolean ret = false;

        if (str != null)
        {
            str = str.trim();
            ret = (str.equalsIgnoreCase("on") || str.equalsIgnoreCase("yes")
                    || str.equalsIgnoreCase("true") || str.equalsIgnoreCase("1"));
        }

        return ret;
    }

    public static void bindValue(QuestionAnswers questionAnswers, String field,
            Object value)
    {
        if (questionAnswers != null && !StringUtils.isEmpty(field))
        {
            // int and boolean answers have special handling...

            if (field.startsWith("i"))
            {

                if (value == null)
                {
                    value = 0;
                }
                else if (value instanceof String)
                {
                    // expect failure if the string is not in integer format
                    value = Integer.parseInt((String) value);
                }

                // else assume (value instanceof Integer)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(questionAnswers, field, value);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else if (field.startsWith("b"))
            {
                if (value == null)
                {
                    value = false;
                }
                else if (value instanceof String)
                {
                    value = getBooleanValue((String) value);
                }

                // else assume (value instanceof Boolean)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(questionAnswers, field,
                            (value == null ? 0 : value));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else
            {
                try
                {
                    PropertyUtils.setProperty(questionAnswers, field, value);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    public static Integer getInteger(HttpServletRequest request, String key)
    {
        Integer id = null;

        if (request.getAttribute(key) != null)
        {
            id = (Integer) request.getAttribute(key);
        }
        else if (StringUtils.isInteger(request.getParameter(key)))
        {
            id = Integer.valueOf(request.getParameter(key));
        }

        return id;
    }

    public static Question2 getQuestion(Integer questionId)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Question2 question = null;

        try
        {
            question = (Question2) ht.load(Question2.class, questionId);
        }
        catch (Exception e)
        {
        }

        return question;
    }

    public static void updateQuestionOrder(HttpServletRequest request)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<String> qIds = StringUtils.listify(request.getParameter("questionIds"),
                "\\|");
        int order = 1;
        for (String id : qIds)
        {
            Question2 q = (Question2) ht.load(Question2.class, Integer.valueOf(id));
            q.setPosition(order);
            ht.update(q);
            order++;
        }
    }

    public static QuestionAnswers createQuestionAnswers(
            Class<? extends AnswersInterface> answerEntity, boolean save)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        if (answerEntity == null)
        {
            throw new IllegalArgumentException("answerEntity is null");
        }

        QuestionAnswers ret = new QuestionAnswers();
        ret.setAnswerEntity(answerEntity.getName());
        if (save)
        {
            ht.save(ret);
        }
        return ret;
    }

    public static Map<Category2, Map<Question2, Question2Stats>> getCriteriaGroupStats(
            Category2Model model, Class<? extends AnswersInterface> answerInterface,
            String hqlAlias, String staticWhereHql)
    {
        Map<Category2, Map<Question2, Question2Stats>> ret = new LinkedHashMap<Category2, Map<Question2, Question2Stats>>();

        Map<Category2, List<? extends QuestionInterface>> categoryMap = Category2Helper
                .getCategoryMap(model);

        for (Category2 category : categoryMap.keySet())
        {
            if (!categoryMap.get(category).isEmpty())
            {
                ret.put(category, getQuestionStats(categoryMap.get(category),
                        answerInterface, hqlAlias, staticWhereHql));
            }
        }

        return ret;
    }

    public static void populateQuestionsStats(ModelAndView mv,
            List<? extends QuestionInterface> questions,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        mv.addObject("statsMap", getQuestionStats(questions, answerInterface,
                hqlAlias, staticWhereHql));
    }

    public static Map<Question2, Question2Stats> getQuestionStats(
            List<? extends QuestionInterface> questions,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        Map<Question2, Question2Stats> statsMap = new LinkedHashMap<Question2, Question2Stats>();

        for (QuestionInterface question : questions)
        {
            if (StringUtils.isEmpty(hqlAlias))
            {
                hqlAlias = "a";
            }
            if (staticWhereHql == null)
            {
                staticWhereHql = "";
            }

            Question2Stats stats = new Question2Stats(question.getQuestion(),
                    answerInterface, hqlAlias, staticWhereHql);
            statsMap.put(question.getQuestion(), stats);
        }
        return statsMap;
    }

    public static LinkedList<CriteriaQuestion> getCriteriaQuestions(
            List<? extends QuestionInterface> questions, String hqlPrefix,
            String userTypeKey, Class<? extends AnswersInterface> answerInterface)
    {
        LinkedList<CriteriaQuestion> ret = new LinkedList<CriteriaQuestion>();

        for (QuestionInterface q : questions)
        {
            CriteriaQuestion cq = getCriteriaQuestion(q, hqlPrefix, userTypeKey,
                    answerInterface);

            if (cq != null)
            {
                ret.add(cq);
            }
        }

        return ret;
    }

    public static CriteriaQuestion getCriteriaQuestion(QuestionInterface iQuestion,
            String hqlPrefix, String userTypeKey)
    {
        return getCriteriaQuestion(iQuestion, hqlPrefix, userTypeKey, null);
    }

    public static CriteriaQuestion getCriteriaQuestion(QuestionInterface iQuestion,
            String hqlPrefix, String userTypeKey, Class answerClass)
    {
        CriteriaQuestion cq = null;

        Question2 question = iQuestion.getQuestion();

        switch (question.getType())
        {
            case Question2.TYPE_TEXT:
            case Question2.TYPE_LARGE_TEXT:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_TEXT);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                break;

            case Question2.TYPE_BOOLEAN:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                break;

            case Question2.TYPE_DATE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_DATE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setDateFormatForDisplay(CriteriaQuestion.STANDARD_DATE_FORMAT);
                break;

            case Question2.TYPE_INTEGER:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_NUMBER);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColAlignment("right");
                break;

            case Question2.TYPE_RATING:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_RATING_RANGE);
                cq.setMin(question.getMin());
                cq.setMax(question.getMax());
                cq.setRatingNaDbValue(Question2.RATING_NA_DB_VALUE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColAlignment("right");
                break;

            case Question2.TYPE_MULTI_CHOICE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_CHOICE_MULTI);
                cq.setOperation(CriteriaQuestion.OPERATION_CONTAINS);
                cq.setAnswerDelimiter("^");
                cq.setOptionChoices(StringUtils.mapify(question.getChoiceList(),
                        question.getChoiceList()));
                cq.setOptionIncludeOtherFlag(question.isIncludeOther());
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                break;

            case Question2.TYPE_SINGLE_CHOICE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                cq.setOperation(CriteriaQuestion.OPERATION_EQUALS);
                cq.setOptionChoices(StringUtils.mapify(question.getChoiceList(),
                        question.getChoiceList()));
                cq.setOptionIncludeOtherFlag(question.isIncludeOther());
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                break;

            case Question2.TYPE_FILE_UPLOAD:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_FILE_UPLOAD);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(200);
                break;

            case Question2.TYPE_TREE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setType(CriteriaQuestion.TYPE_TREE);
                cq.setTreeOptions(question.getTreeNodes());

                if (question.isSingleTreePath())
                {
                    cq.setDisplayFormatMultiline(
                            CriteriaQuestion.DISPLAY_FORMAT_MULTILINE_SINGLE_PATH);
                }
                break;

            case Question2.TYPE_MATRIX_MULTI:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_MATRIX_MULTI);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setMatrixColumnLabels(question.getMatrixColumnLabels());
                cq.setMatrixRowLabels(question.getMatrixRowLabels());
                break;

            case Question2.TYPE_MATRIX_SINGLE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_MATRIX_SINGLE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setMatrixColumnLabels(question.getMatrixColumnLabels());
                cq.setMatrixRowLabels(question.getMatrixRowLabels());
                break;

            default:
                throw new RuntimeException("Unknown question type");
        }

        // APPLY PERMISSION BEHAVIOR...
        cq.setVisibleCriteria(canSearch(question, userTypeKey));
        cq.setVisibleResults(canRead(question, userTypeKey));
        cq.setQuestionOrder(question.getPosition());
        return cq;
    }

    // Unused Method
    private static LinkedHashMap<String, String> buildChoiceWithOther(
            Question2 question, Class<? extends AnswersInterface> answerClass)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<String> answers = ht
                .find("select ai.questionAnswers." + question.getAnswerField1()
                        + " from " + answerClass.getName() + " ai");
        List<String> allChoices = new LinkedList<String>();

        for (String answer : answers)
        {
            if (!StringUtils.isEmpty(answer))
            {
                if (question.getType() == Question2.TYPE_MULTI_CHOICE)
                {
                    String[] splitAnswers = answer.split("\\^");
                    for (int i = 0; i < splitAnswers.length; i++)
                    {
                        if (!StringUtils.isEmpty(splitAnswers[i])
                                && !allChoices.contains(splitAnswers[i])
                                && !question.getChoiceList()
                                        .contains(splitAnswers[i]))
                        {
                            allChoices.add(splitAnswers[i]);
                        }
                    }
                }
                else if (question.getType() == Question2.TYPE_SINGLE_CHOICE
                        && !question.getChoiceList().contains(answer)
                        && !allChoices.contains(answer))
                {
                    allChoices.add(answer);
                }
            }
        }

        return StringUtils.mapify(allChoices, allChoices);
    }

    public static int getMaxPermittedLargeTextFields()
    {
        int maxTFields = 10; // (MYSQL limitation)

        PortalConfig pc = PortalConfigHelper.getPortalConfig(PortalConfig.DB_TYPE);

        if (pc != null && pc.getOrbisValue() != null
                && pc.getOrbisValue().equals("MSSQL"))
        {
            maxTFields = 100;
        }

        return maxTFields;
    }

    public static boolean isAnswered(Question2 question,
            QuestionAnswers questionAnswers)
    {
        boolean answered = false;

        if (question != null && question.isAnswerFieldsSet()
                && questionAnswers != null)
        {
            try
            {
                switch (question.getType())
                {
                    case Question2.TYPE_BOOLEAN:
                    case Question2.TYPE_DATE:
                    case Question2.TYPE_INTEGER:
                    case Question2.TYPE_LARGE_TEXT:
                    case Question2.TYPE_MULTI_CHOICE:
                    case Question2.TYPE_SINGLE_CHOICE:
                    case Question2.TYPE_TEXT:
                    case Question2.TYPE_MATRIX_SINGLE:
                    case Question2.TYPE_MATRIX_MULTI:
                    case Question2.TYPE_TREE:
                        answered = PropertyUtils.getProperty(questionAnswers,
                                question.getAnswerField1()) != null;
                        break;

                    case Question2.TYPE_FILE_UPLOAD:
                        answered = PropertyUtils.getProperty(questionAnswers,
                                question.getAnswerField1()) != null
                                && PropertyUtils.getProperty(questionAnswers,
                                        question.getAnswerField2()) != null;
                        break;
                    default:
                        break;
                }
            }
            catch (Exception e)
            {
            }
        }

        return answered;
    }

    /**
     * HARD DELETE!
     */
    public static boolean deleteQuestion(QuestionInterface questionEntity)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        boolean deleted = false;
        String qName = null;
        Question2 q = null;

        if (questionEntity != null)
        {
            try
            {
                qName = questionEntity.getClass().getSimpleName() + "["
                        + questionEntity.getId() + "] "
                        + questionEntity.getQuestion();

                q = questionEntity.getQuestion();
                ht.delete(questionEntity);
                deleted = true;
                ht.delete(q);
            }
            catch (Exception e)
            {
                if (deleted)
                {
                    logger.warn(
                            "Question2 referenced from another QuestionEntity. QuestionEntity successfully deleted but Question2 is retained");
                }
            }
        }

        if (!deleted)
        {
            logger.warn("NOT DELETED: " + qName);
        }
        return deleted;
    }

    /**
     * HARD DELETE! we take an answersJoinEntity and delete it's answers and the
     * join itself
     */
    public static boolean deleteAnswers(AnswersInterface answerJoinEntity)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        boolean isDeleted = false;

        try
        {
            if (answerJoinEntity != null)
            {
                if (answerJoinEntity.getQuestionAnswers() != null)
                {
                    //
                    QuestionAnswers toDelete = answerJoinEntity
                            .getQuestionAnswers();
                    answerJoinEntity.setQuestionAnswers(null);
                    ht.update(answerJoinEntity);
                    //
                    ht.delete(toDelete);
                }
                ht.delete(answerJoinEntity);
            }
            isDeleted = true;
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }

        return isDeleted;
    }

    public static Question2 cloneQuestion(Question2 original)
            throws Question2Exception
    {
        Question2 clone = new Question2();
        clone.setAdminOnly(original.isAdminOnly());
        clone.setType(original.getType());
        clone.setChoices(original.getChoices());
        clone.setDescription(original.getDescription());
        clone.setMaxUploadSize(original.getMaxUploadSize());
        clone.setPosition(original.getPosition());
        clone.setQuestionText(original.getQuestionText());
        clone.setRequired(original.isRequired());
        clone.setMin(original.getMin());
        clone.setMax(original.getMax());
        clone.setTreeNodes(original.getTreeNodes());
        clone.setSingleTreePath(original.isSingleTreePath());
        clone.setAnswerField1(original.getAnswerField1());
        clone.setAnswerField2(original.getAnswerField2());
        clone.setAnswerField3(original.getAnswerField3());
        clone.setAnswerField4(original.getAnswerField4());
        clone.setAnswerField5(original.getAnswerField5());
        clone.setJsonUserModel(original.getJsonUserModel());
        clone.setMappingKey(original.getMappingKey());

        return clone;
    }

    public static List<I18nLabel> validateAnswers(
            List<? extends QuestionInterface> questions,
            AnswersInterface answerEntity)
    {
        List<I18nLabel> errors = new ArrayList<I18nLabel>();

        for (QuestionInterface q : questions)
        {
            Question2 question = q.getQuestion();
            if (question.isRequired() && !question.isValidated())
            {
                try
                {
                    Object answerValue = PropertyUtils.getProperty(
                            answerEntity.getQuestionAnswers(),
                            question.getAnswerField1());

                    if (answerValue == null)
                    {
                        errors.add(
                                new I18nLabel("i18n.Question2Helper.answerRequired",
                                        question.getQuestionText()));
                    }
                    else if (question.getType() == Question2.TYPE_TREE
                            && !isTreeQuestionAnswered((String) answerValue))
                    {
                        errors.add(
                                new I18nLabel("i18n.Question2Helper.answerRequired",
                                        question.getQuestionText()));
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.answerRequired",
                            question.getQuestionText()));
                }
            }
        }

        return errors;
    }

    private static boolean isTreeQuestionAnswered(String jsonArray)
    {
        boolean selected = false;

        try
        {
            JSONArray selectedNodes = new JSONArray(jsonArray);
            selected = selectedNodes.length() > 0;
        }
        catch (Exception e)
        {
        }

        return selected;
    }

    /**
     * Creates a clone of "original". The clone is then saved to the database.
     * The cloned instance is then returned.
     */
    public static QuestionAnswers cloneAnswers(QuestionAnswers original)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        QuestionAnswers clone = null;

        if (original != null)
        {
            try
            {
                clone = (QuestionAnswers) BeanUtils.cloneBean(original);
                ht.save(clone);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        return clone;
    }

    public static List<I18nLabel> validate(Question2 question)
    {
        List<I18nLabel> errors = new ArrayList<I18nLabel>();

        if (StringUtils.isEmpty(question.getQuestionText()))
        {
            errors.add(new I18nLabel("i18n.Question2Helper.missingQuestionText"));
        }

        switch (question.getType())
        {
            case Question2.TYPE_INTEGER:
                if (question.getMax() < question.getMin())
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                break;

            case Question2.TYPE_MULTI_CHOICE:
                if (StringUtils.isEmpty(question.getChoices())
                        || question.getChoiceList().isEmpty())
                {
                    errors.add(
                            new I18nLabel("i18n.Question2Helper.missingChoices"));
                }
                break;
            default:
                break;
        }

        return errors;
    }

    public static String getQuestionTypeLabel(Integer questionType)
    {
        switch (questionType)
        {
            case Question2.TYPE_TEXT:
                return "TEXT";

            case Question2.TYPE_INTEGER:
                return "NUMERIC INTEGER";

            case Question2.TYPE_MULTI_CHOICE:
                return "MULTI CHOICE";

            case Question2.TYPE_BOOLEAN:
                return "BOOLEAN";

            case Question2.TYPE_DATE:
                return "DATE";

            case Question2.TYPE_LARGE_TEXT:
                return "LARGE TEXT";

            case Question2.TYPE_RATING:
                return "RATING";

            case Question2.TYPE_FILE_UPLOAD:
                return "FILE UPLOAD";

            case Question2.TYPE_SINGLE_CHOICE:
                return "SINGLE CHOICE";

            case Question2.TYPE_TREE:
                return "TREE";

            case Question2.TYPE_MATRIX_MULTI:
                return "MULTI CHOICE MATRIX";

            case Question2.TYPE_MATRIX_SINGLE:
                return "SINGLE CHOICE MATRIX";
            default:
                break;
        }

        return "UNKNOWN";
    }

    private static boolean canRead(Question2 question, String userTypeKey)
    {
        boolean ret = true;

        if (userTypeKey != null && question != null
                && !StringUtils.isEmpty(question.getJsonUserModel()))
        {
            for (QuestionUserType t : question.getUserTypes())
            {
                if (t.getUserTypeKey().equals(userTypeKey))
                {
                    ret = t.isCanRead();
                    break;
                }
            }
        }

        return ret;
    }

    // Unused Method
    private static boolean canWrite(Question2 question, String userTypeKey)
    {
        boolean ret = true;

        if (userTypeKey != null && question != null
                && !StringUtils.isEmpty(question.getJsonUserModel()))
        {
            for (QuestionUserType t : question.getUserTypes())
            {
                if (t.getUserTypeKey().equals(userTypeKey))
                {
                    ret = t.isCanWrite();
                    break;
                }
            }
        }

        return ret;
    }

    private static boolean canSearch(Question2 question, String userTypeKey)
    {
        boolean ret = true;

        if (userTypeKey != null && question != null
                && !StringUtils.isEmpty(question.getJsonUserModel()))
        {
            for (QuestionUserType t : question.getUserTypes())
            {
                if (t.getUserTypeKey().equals(userTypeKey))
                {
                    ret = t.isCanSearch();
                    break;
                }
            }
        }

        return ret;
    }

    /**
     * A JSP helper method for matrix-type questions.
     * 
     * @return Map<rowLabel, Map<columnLabel, isChecked>>
     */
    public static Map<String, Map<String, Boolean>> getMatrixMap(
            QuestionAnswers questionAnswers, Question2 question)
    {
        Map<String, Map<String, Boolean>> map = new LinkedHashMap<String, Map<String, Boolean>>();

        try
        {
            List<String> rows = question.getMatrixRowLabels();
            List<String> cols = question.getMatrixColumnLabels();
            if (questionAnswers == null)
            {
                questionAnswers = new QuestionAnswers();
            }
            String answerText = (String) PropertyUtils.getProperty(questionAnswers,
                    question.getAnswerField1());

            rowLoop: for (int r = 0; r < rows.size(); r++)
            {
                String rowLabel = rows.get(r);

                if (!StringUtils.isEmpty(rowLabel))
                {
                    Map subMap = new LinkedHashMap();
                    for (int c = 0; c < cols.size(); c++)
                    {
                        String colLabel = cols.get(c);

                        if (StringUtils.isEmpty(colLabel))
                        {
                            continue rowLoop;
                        }

                        boolean checked = false;

                        if (answerText != null)
                        {
                            checked = answerText.indexOf(
                                    "^" + rowLabel + "~" + colLabel + "^") != -1;
                        }

                        subMap.put(colLabel, checked);
                    }
                    map.put(rowLabel, subMap);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return map;
    }

}
