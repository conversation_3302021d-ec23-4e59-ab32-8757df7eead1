package com.orbis.question2;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.orbis.portal.CommandQueryTemplate;

import com.orbis.utils.StringUtils;
import com.orbis.portal.PortalUtils;

public class Question2Stats
{
    private Map<String, Integer> countedChoices;

    private Map<Integer, Integer> plotPoints;

    private Map<String, String> calcMatrixChoices;

    // This field will only be populated whenever it is necessary to display it
    // outside of the question stats
    private String notAnswered = "";

    private int min = 0;

    private int max = 10;

    private int totalAnswers = 0;

    private String modifiedTree = "[]";

    public Question2Stats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        switch (question.getType())
        {
            case Question2.TYPE_TEXT:
            case Question2.TYPE_DATE:
            case Question2.TYPE_LARGE_TEXT:
                prepareAnsweredStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_RATING:
            case Question2.TYPE_INTEGER:
                prepareIntegerStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_BOOLEAN:
                prepareBooleanStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_SINGLE_CHOICE:
            case Question2.TYPE_MULTI_CHOICE:
                prepareSimpleChoiceStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_MATRIX_SINGLE:
            case Question2.TYPE_MATRIX_MULTI:
                prepareMatrixChoiceStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_TREE:
                prepareTreeStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
            case Question2.TYPE_FILE_UPLOAD:
                prepareFileUploadStats(question, answerInterface, hqlAlias,
                        staticWhereHql);
                break;
        }
    }

    public int getMin()
    {
        return min;
    }

    public int getMax()
    {
        return max;
    }

    private void prepareIntegerStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        this.plotPoints = new HashMap<Integer, Integer>();

        List<Integer> answers = ht.find("select " + hqlAlias + ".questionAnswers."
                + question.getAnswerField1() + " from " + answerInterface.getName()
                + " " + hqlAlias + " where " + hqlAlias + ".questionAnswers."
                + question.getAnswerField1() + " is not null " + staticWhereHql);
        for (Integer answer : answers)
        {
            if (!this.plotPoints.containsKey(answer))
            {
                this.plotPoints.put(answer, 1);
            }
            else
            {
                this.plotPoints.put(answer,
                        this.plotPoints.get(answer).intValue() + 1);
            }

            if (answer.intValue() < this.min)
            {
                this.min = answer;
            }

            if (answer.intValue() > this.max)
            {
                this.max = answer;
            }
        }

        if (this.min >= 5 || this.min < 0)
        {
            this.min -= 5;
        }

        this.max += 5;
    }

    private void prepareAnsweredStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        countedChoices = new LinkedHashMap<String, Integer>();
        this.countedChoices.put("Answered",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is not null "
                        + staticWhereHql).get(0));
        this.countedChoices.put("Not Answered",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is null " + staticWhereHql)
                        .get(0));
    }

    private void prepareTreeStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        this.countedChoices = new LinkedHashMap<String, Integer>();
        List<Object[]> answers = ht.find("select " + hqlAlias + ".id, " + hqlAlias
                + ".questionAnswers." + question.getAnswerField1() + " from "
                + answerInterface.getName() + " " + hqlAlias + " where " + hqlAlias
                + ".questionAnswers." + question.getAnswerField1() + " is not null "
                + staticWhereHql);

        int notAnswered = ((Integer) ht
                .find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is null " + staticWhereHql)
                .get(0)).intValue();

        this.totalAnswers = answers.size() + notAnswered;

        this.notAnswered = MessageFormat.format("{0,number,#.##%}",
                new Double(notAnswered) / new Double(this.totalAnswers));

        if (!answers.isEmpty())
        {
            Map<String, Set<Integer>> customCountedChoices = new HashMap<String, Set<Integer>>();
            try
            {
                for (Object[] o : answers)
                {
                    String answer = (String) o[1];
                    JSONArray jsonAnswer = new JSONArray(answer);
                    for (int i = 0; i < jsonAnswer.length(); i++)
                    {
                        if (!customCountedChoices
                                .containsKey(jsonAnswer.getString(i)))
                        {
                            customCountedChoices.put(jsonAnswer.getString(i),
                                    new HashSet<Integer>());
                        }

                        customCountedChoices.get(jsonAnswer.getString(i))
                                .add((Integer) o[0]);
                    }
                }

                JSONArray treeNodes = new JSONArray(question.getTreeNodes());
                this.modifiedTree = prepareTreeStats_populateNumbers(treeNodes, "",
                        customCountedChoices)[0].toString();

            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    /**
     * This method is recursive
     * 
     * @param treeNodes
     * @param currentNode
     * @param customCountedChoices
     * @throws JSONException
     */
    private Object[] prepareTreeStats_populateNumbers(JSONArray treeNodes,
            String currentNode, Map<String, Set<Integer>> customCountedChoices)
            throws JSONException
    {
        JSONArray finalTree = new JSONArray();
        Set numberOfAnswers = new HashSet<Integer>();
        for (int i = 0; i < treeNodes.length(); i++)
        {
            JSONObject node = treeNodes.getJSONObject(i);
            if (node.getBoolean("hasChildren"))
            {
                Object[] result = prepareTreeStats_populateNumbers(
                        node.getJSONArray("ChildNodes"), node.getString("value"),
                        customCountedChoices);
                node.put("ChildNodes", result[0]);
                node.put("text",
                        node.getString("text") + " - <b>"
                                + MessageFormat.format("{0,number,#.##%}",
                                        new Double(((Set) result[1]).size())
                                                / new Double(this.totalAnswers))
                                + "</b>");

                numberOfAnswers.addAll((Set) result[1]);
            }
            else
            {
                if (customCountedChoices.containsKey(node.getString("value")))
                {
                    numberOfAnswers.addAll(
                            customCountedChoices.get(node.getString("value")));
                    node.put("text", node.getString("text") + " - <b>"
                            + MessageFormat.format("{0,number,#.##%}",
                                    new Double(customCountedChoices
                                            .get(node.getString("value")).size())
                                            / new Double(this.totalAnswers))
                            + "</b>");
                }
                else
                {
                    node.put("text", node.getString("text") + " - <b>0%</b>");
                }
            }
            finalTree.put(node);
        }

        return new Object[] { finalTree, numberOfAnswers };
    }

    private void prepareMatrixChoiceStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        this.countedChoices = new LinkedHashMap<String, Integer>();
        this.calcMatrixChoices = new LinkedHashMap<String, String>();

        List<String> answers = ht.find("select " + hqlAlias + ".questionAnswers."
                + question.getAnswerField1() + " from " + answerInterface.getName()
                + " " + hqlAlias + " where " + hqlAlias + ".questionAnswers."
                + question.getAnswerField1() + " is not null " + staticWhereHql);
        int notAnswered = ((Integer) ht
                .find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is null " + staticWhereHql)
                .get(0)).intValue();
        int totalAnswers = answers.size() + notAnswered;

        for (String combinedAnswer : answers)
        {
            if (!StringUtils.isEmpty(combinedAnswer))
            {
                String[] answerArray = combinedAnswer.split("\\^");
                for (int i = 0; i < answerArray.length; i++)
                {
                    String answer = answerArray[i];

                    if (this.countedChoices.containsKey(answer))
                    {
                        this.countedChoices.put(answer,
                                this.countedChoices.get(answer) + 1);
                    }
                    else
                    {
                        this.countedChoices.put(answer, 1);
                    }

                }
            }
            else
            {
                notAnswered++;
            }
        }

        for (String answer : this.countedChoices.keySet())
        {
            this.calcMatrixChoices.put(answer,
                    MessageFormat.format("{0,number,#.##%}",
                            new Double(this.countedChoices.get(answer).intValue())
                                    / new Double(totalAnswers)));
        }

        this.notAnswered = MessageFormat.format("{0,number,#.##%}",
                new Double(notAnswered) / new Double(totalAnswers));
    }

    private void prepareFileUploadStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        this.countedChoices = new LinkedHashMap<String, Integer>();
        this.countedChoices.put("Uploaded",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is not null "
                        + staticWhereHql).get(0));
        this.countedChoices.put("Not Uploaded",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " is null " + staticWhereHql)
                        .get(0));
    }

    private void prepareSimpleChoiceStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        this.countedChoices = new LinkedHashMap<String, Integer>();
        for (String option : question.getChoiceList())
        {
            this.countedChoices.put(option, 0);
        }

        if (question.isIncludeOther())
        {
            this.countedChoices.put("Other", 0);
        }

        List<String> answers = ht.find("select " + hqlAlias + ".questionAnswers."
                + question.getAnswerField1() + " from " + answerInterface.getName()
                + " " + hqlAlias + " "
                + (!StringUtils.isEmpty(staticWhereHql)
                        ? " where " + staticWhereHql.trim().substring(3)
                        : ""));

        switch (question.getType())
        {
            case Question2.TYPE_SINGLE_CHOICE:
                handleSingleChoice(question, answers);
                break;
            case Question2.TYPE_MULTI_CHOICE:
                handleMultiChoice(question, answers);
                break;
        }
    }

    private void handleMultiChoice(Question2 question, List<String> answers)
    {
        for (String answer : answers)
        {
            if (!StringUtils.isEmpty(answer))
            {
                String[] choicesSelected = answer.substring(1, answer.length() - 1)
                        .split("\\^");
                for (int i = 0; i < choicesSelected.length; i++)
                {
                    if (this.countedChoices.containsKey(choicesSelected[i]))
                    {
                        this.countedChoices.put(choicesSelected[i],
                                this.countedChoices.get(choicesSelected[i])
                                        .intValue() + 1);
                    }
                    else
                    {
                        if (question.isIncludeOther())
                        {
                            this.countedChoices.put("Other",
                                    this.countedChoices.get("Other").intValue()
                                            + 1);
                        }
                    }
                }
            }
            else
            {
                if (!this.countedChoices.containsKey("No Answer"))
                {
                    this.countedChoices.put("No Answer", 0);
                }

                this.countedChoices.put("No Answer",
                        this.countedChoices.get("No Answer").intValue() + 1);
            }
        }
    }

    private void handleSingleChoice(Question2 question, List<String> answers)
    {
        for (String answer : answers)
        {
            if (!StringUtils.isEmpty(answer))
            {
                if (this.countedChoices.containsKey(answer))
                {
                    this.countedChoices.put(answer,
                            this.countedChoices.get(answer).intValue() + 1);
                }
                else
                {
                    if (question.isIncludeOther())
                    {
                        this.countedChoices.put("Other",
                                this.countedChoices.get("Other").intValue() + 1);
                    }
                }
            }
            else
            {
                if (!this.countedChoices.containsKey("No Answer"))
                {
                    this.countedChoices.put("No Answer", 0);
                }

                this.countedChoices.put("No Answer",
                        this.countedChoices.get("No Answer").intValue() + 1);
            }
        }
    }

    private void prepareBooleanStats(Question2 question,
            Class<? extends AnswersInterface> answerInterface, String hqlAlias,
            String staticWhereHql)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        countedChoices = new LinkedHashMap<String, Integer>();
        this.countedChoices.put("Yes",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " = true " + staticWhereHql)
                        .get(0));
        this.countedChoices.put("No",
                (Integer) ht.find("select count(" + hqlAlias + ".id) from "
                        + answerInterface.getName() + " " + hqlAlias + " where "
                        + hqlAlias + ".questionAnswers."
                        + question.getAnswerField1() + " = false " + staticWhereHql)
                        .get(0));
    }

    public Map<String, Integer> getCountedChoices()
    {
        return countedChoices;
    }

    public Map<Integer, Integer> getPlotPoints()
    {
        return plotPoints;
    }

    public Map<String, String> getCalcMatrixChoices()
    {
        return calcMatrixChoices;
    }

    public String getNotAnswered()
    {
        return notAnswered;
    }

    public String getModifiedTree()
    {
        return modifiedTree;
    }

}
