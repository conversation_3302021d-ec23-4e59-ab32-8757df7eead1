package com.orbis.question2;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.PropertyUtils;
import org.hibernate.annotations.ColumnDefault;
import org.json.JSONArray;

import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class QuestionAnswers extends ContentItem
{
    private String answerEntity;

    private String s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15,
            s16, s17, s18, s19, s20, s21, s22, s23, s24, s25, s26, s27, s28, s29,
            s30, s31, s32, s33, s34, s35, s36, s37, s38, s39, s40, s41, s42, s43,
            s44, s45, s46, s47, s48, s49, s50, s51, s52, s53, s54, s55, s56, s57,
            s58, s59, s60, s61, s62, s63, s64, s65, s66, s67, s68, s69, s70, s71,
            s72, s73, s74, s75, s76, s77, s78, s79, s80, s81, s82, s83, s84, s85,
            s86, s87, s88, s89, s90, s91, s92, s93, s94, s95, s96, s97, s98, s99,
            s100;

    private Date d1, d2, d3, d4, d5, d6, d7, d8, d9, d10, d11, d12, d13, d14, d15,
            d16, d17, d18, d19, d20, d21, d22, d23, d24, d25, d26, d27, d28, d29,
            d30, d31, d32, d33, d34, d35, d36, d37, d38, d39, d40, d41, d42, d43,
            d44, d45, d46, d47, d48, d49, d50, d51, d52, d53, d54, d55, d56, d57,
            d58, d59, d60, d61, d62, d63, d64, d65, d66, d67, d68, d69, d70, d71,
            d72, d73, d74, d75, d76, d77, d78, d79, d80, d81, d82, d83, d84, d85,
            d86, d87, d88, d89, d90, d91, d92, d93, d94, d95, d96, d97, d98, d99,
            d100;

    @ColumnDefault("0")
    private int i1, i2, i3, i4, i5, i6, i7, i8, i9, i10, i11, i12, i13, i14, i15,
            i16, i17, i18, i19, i20, i21, i22, i23, i24, i25, i26, i27, i28, i29,
            i30, i31, i32, i33, i34, i35, i36, i37, i38, i39, i40, i41, i42, i43,
            i44, i45, i46, i47, i48, i49, i50, i51, i52, i53, i54, i55, i56, i57,
            i58, i59, i60, i61, i62, i63, i64, i65, i66, i67, i68, i69, i70, i71,
            i72, i73, i74, i75, i76, i77, i78, i79, i80, i81, i82, i83, i84, i85,
            i86, i87, i88, i89, i90, i91, i92, i93, i94, i95, i96, i97, i98, i99,
            i100;

    @ColumnDefault("0")
    private boolean b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, b13, b14,
            b15, b16, b17, b18, b19, b20, b21, b22, b23, b24, b25, b26, b27, b28,
            b29, b30, b31, b32, b33, b34, b35, b36, b37, b38, b39, b40, b41, b42,
            b43, b44, b45, b46, b47, b48, b49, b50, b51, b52, b53, b54, b55, b56,
            b57, b58, b59, b60, b61, b62, b63, b64, b65, b66, b67, b68, b69, b70,
            b71, b72, b73, b74, b75, b76, b77, b78, b79, b80, b81, b82, b83, b84,
            b85, b86, b87, b88, b89, b90, b91, b92, b93, b94, b95, b96, b97, b98,
            b99, b100;

    private String t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13, t14, t15,
            t16, t17, t18, t19, t20, t21, t22, t23, t24, t25, t26, t27, t28, t29,
            t30, t31, t32, t33, t34, t35, t36, t37, t38, t39, t40, t41, t42, t43,
            t44, t45, t46, t47, t48, t49, t50, t51, t52, t53, t54, t55, t56, t57,
            t58, t59, t60, t61, t62, t63, t64, t65, t66, t67, t68, t69, t70, t71,
            t72, t73, t74, t75, t76, t77, t78, t79, t80, t81, t82, t83, t84, t85,
            t86, t87, t88, t89, t90, t91, t92, t93, t94, t95, t96, t97, t98, t99,
            t100;

    /**
     * This is a JSP helper method for retrieving answers to single-choice questions
     * 
     * map key = "answer field name"
     * 
     * map val = "selected choice"
     * 
     */
    public Map<String, String> getSingleSelectedChoices()
    {
        Map<String, String> map = new HashMap<String, String>();

        String answerField = null;
        String choice = null;
        for (int i = 1; i <= 100; i++)
        {
            try
            {
                answerField = "s" + i;
                choice = (String) PropertyUtils.getProperty(this, answerField);
                map.put(answerField, choice);
            }
            catch (Exception e)
            {
            }
        }

        return map;
    }

    /**
     * This is a JSP helper method for retrieving answers to multi-choice questions
     * 
     * map key = "answer field name"
     * 
     * map val = "List of selected choices"
     * 
     */
    public Map<String, List<String>> getMultipleSelectedChoices()
    {
        Map<String, List<String>> map = new HashMap<String, List<String>>();

        String answerField = null;
        String choices = null;
        for (int i = 1; i <= 100; i++)
        {
            try
            {
                answerField = "t" + i;
                choices = (String) PropertyUtils.getProperty(this, answerField);
                // we need to remove the leading ^ (^ is originally needed for
                // crazy sql reasons)
                choices = choices != null && choices.startsWith("^")
                        ? choices.substring(1)
                        : choices;
                map.put(answerField,
                        choices != null ? Arrays.asList(choices.split("\\^"))
                                : null);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        return map;
    }

    public Map<String, String> getMultipleSelectedChoicesJson()
    {
        Map<String, String> map = new HashMap<String, String>();

        String answerField = null;
        String choices = null;

        for (int i = 1; i <= 100; i++)
        {
            try
            {
                answerField = "t" + i;
                choices = (String) PropertyUtils.getProperty(this, answerField);
                // we need to remove the leading ^ (^ is originally needed for
                // craaaaAzy sql reasons)
                choices = choices != null && choices.startsWith("^")
                        ? choices.substring(1)
                        : choices;
                map.put(answerField,
                        choices != null
                                ? new JSONArray(Arrays.asList(choices.split("\\^")))
                                        .toString()
                                : null);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        return map;
    }

    public String getAnswerEntity()
    {
        return answerEntity;
    }

    public void setAnswerEntity(String answerEntity)
    {
        this.answerEntity = answerEntity;
    }

    public String getS1()
    {
        return s1;
    }

    public void setS1(String s1)
    {
        this.s1 = s1;
    }

    public String getS2()
    {
        return s2;
    }

    public void setS2(String s2)
    {
        this.s2 = s2;
    }

    public String getS3()
    {
        return s3;
    }

    public void setS3(String s3)
    {
        this.s3 = s3;
    }

    public String getS4()
    {
        return s4;
    }

    public void setS4(String s4)
    {
        this.s4 = s4;
    }

    public String getS5()
    {
        return s5;
    }

    public void setS5(String s5)
    {
        this.s5 = s5;
    }

    public String getS6()
    {
        return s6;
    }

    public void setS6(String s6)
    {
        this.s6 = s6;
    }

    public String getS7()
    {
        return s7;
    }

    public void setS7(String s7)
    {
        this.s7 = s7;
    }

    public String getS8()
    {
        return s8;
    }

    public void setS8(String s8)
    {
        this.s8 = s8;
    }

    public String getS9()
    {
        return s9;
    }

    public void setS9(String s9)
    {
        this.s9 = s9;
    }

    public String getS10()
    {
        return s10;
    }

    public void setS10(String s10)
    {
        this.s10 = s10;
    }

    public String getS11()
    {
        return s11;
    }

    public void setS11(String s11)
    {
        this.s11 = s11;
    }

    public String getS12()
    {
        return s12;
    }

    public void setS12(String s12)
    {
        this.s12 = s12;
    }

    public String getS13()
    {
        return s13;
    }

    public void setS13(String s13)
    {
        this.s13 = s13;
    }

    public String getS14()
    {
        return s14;
    }

    public void setS14(String s14)
    {
        this.s14 = s14;
    }

    public String getS15()
    {
        return s15;
    }

    public void setS15(String s15)
    {
        this.s15 = s15;
    }

    public String getS16()
    {
        return s16;
    }

    public void setS16(String s16)
    {
        this.s16 = s16;
    }

    public String getS17()
    {
        return s17;
    }

    public void setS17(String s17)
    {
        this.s17 = s17;
    }

    public String getS18()
    {
        return s18;
    }

    public void setS18(String s18)
    {
        this.s18 = s18;
    }

    public String getS19()
    {
        return s19;
    }

    public void setS19(String s19)
    {
        this.s19 = s19;
    }

    public String getS20()
    {
        return s20;
    }

    public void setS20(String s20)
    {
        this.s20 = s20;
    }

    public String getS21()
    {
        return s21;
    }

    public void setS21(String s21)
    {
        this.s21 = s21;
    }

    public String getS22()
    {
        return s22;
    }

    public void setS22(String s22)
    {
        this.s22 = s22;
    }

    public String getS23()
    {
        return s23;
    }

    public void setS23(String s23)
    {
        this.s23 = s23;
    }

    public String getS24()
    {
        return s24;
    }

    public void setS24(String s24)
    {
        this.s24 = s24;
    }

    public String getS25()
    {
        return s25;
    }

    public void setS25(String s25)
    {
        this.s25 = s25;
    }

    public String getS26()
    {
        return s26;
    }

    public void setS26(String s26)
    {
        this.s26 = s26;
    }

    public String getS27()
    {
        return s27;
    }

    public void setS27(String s27)
    {
        this.s27 = s27;
    }

    public String getS28()
    {
        return s28;
    }

    public void setS28(String s28)
    {
        this.s28 = s28;
    }

    public String getS29()
    {
        return s29;
    }

    public void setS29(String s29)
    {
        this.s29 = s29;
    }

    public String getS30()
    {
        return s30;
    }

    public void setS30(String s30)
    {
        this.s30 = s30;
    }

    public String getS31()
    {
        return s31;
    }

    public void setS31(String s31)
    {
        this.s31 = s31;
    }

    public String getS32()
    {
        return s32;
    }

    public void setS32(String s32)
    {
        this.s32 = s32;
    }

    public String getS33()
    {
        return s33;
    }

    public void setS33(String s33)
    {
        this.s33 = s33;
    }

    public String getS34()
    {
        return s34;
    }

    public void setS34(String s34)
    {
        this.s34 = s34;
    }

    public String getS35()
    {
        return s35;
    }

    public void setS35(String s35)
    {
        this.s35 = s35;
    }

    public String getS36()
    {
        return s36;
    }

    public void setS36(String s36)
    {
        this.s36 = s36;
    }

    public String getS37()
    {
        return s37;
    }

    public void setS37(String s37)
    {
        this.s37 = s37;
    }

    public String getS38()
    {
        return s38;
    }

    public void setS38(String s38)
    {
        this.s38 = s38;
    }

    public String getS39()
    {
        return s39;
    }

    public void setS39(String s39)
    {
        this.s39 = s39;
    }

    public String getS40()
    {
        return s40;
    }

    public void setS40(String s40)
    {
        this.s40 = s40;
    }

    public String getS41()
    {
        return s41;
    }

    public void setS41(String s41)
    {
        this.s41 = s41;
    }

    public String getS42()
    {
        return s42;
    }

    public void setS42(String s42)
    {
        this.s42 = s42;
    }

    public String getS43()
    {
        return s43;
    }

    public void setS43(String s43)
    {
        this.s43 = s43;
    }

    public String getS44()
    {
        return s44;
    }

    public void setS44(String s44)
    {
        this.s44 = s44;
    }

    public String getS45()
    {
        return s45;
    }

    public void setS45(String s45)
    {
        this.s45 = s45;
    }

    public String getS46()
    {
        return s46;
    }

    public void setS46(String s46)
    {
        this.s46 = s46;
    }

    public String getS47()
    {
        return s47;
    }

    public void setS47(String s47)
    {
        this.s47 = s47;
    }

    public String getS48()
    {
        return s48;
    }

    public void setS48(String s48)
    {
        this.s48 = s48;
    }

    public String getS49()
    {
        return s49;
    }

    public void setS49(String s49)
    {
        this.s49 = s49;
    }

    public String getS50()
    {
        return s50;
    }

    public void setS50(String s50)
    {
        this.s50 = s50;
    }

    public String getS51()
    {
        return s51;
    }

    public void setS51(String s51)
    {
        this.s51 = s51;
    }

    public String getS52()
    {
        return s52;
    }

    public void setS52(String s52)
    {
        this.s52 = s52;
    }

    public String getS53()
    {
        return s53;
    }

    public void setS53(String s53)
    {
        this.s53 = s53;
    }

    public String getS54()
    {
        return s54;
    }

    public void setS54(String s54)
    {
        this.s54 = s54;
    }

    public String getS55()
    {
        return s55;
    }

    public void setS55(String s55)
    {
        this.s55 = s55;
    }

    public String getS56()
    {
        return s56;
    }

    public void setS56(String s56)
    {
        this.s56 = s56;
    }

    public String getS57()
    {
        return s57;
    }

    public void setS57(String s57)
    {
        this.s57 = s57;
    }

    public String getS58()
    {
        return s58;
    }

    public void setS58(String s58)
    {
        this.s58 = s58;
    }

    public String getS59()
    {
        return s59;
    }

    public void setS59(String s59)
    {
        this.s59 = s59;
    }

    public String getS60()
    {
        return s60;
    }

    public void setS60(String s60)
    {
        this.s60 = s60;
    }

    public String getS61()
    {
        return s61;
    }

    public void setS61(String s61)
    {
        this.s61 = s61;
    }

    public String getS62()
    {
        return s62;
    }

    public void setS62(String s62)
    {
        this.s62 = s62;
    }

    public String getS63()
    {
        return s63;
    }

    public void setS63(String s63)
    {
        this.s63 = s63;
    }

    public String getS64()
    {
        return s64;
    }

    public void setS64(String s64)
    {
        this.s64 = s64;
    }

    public String getS65()
    {
        return s65;
    }

    public void setS65(String s65)
    {
        this.s65 = s65;
    }

    public String getS66()
    {
        return s66;
    }

    public void setS66(String s66)
    {
        this.s66 = s66;
    }

    public String getS67()
    {
        return s67;
    }

    public void setS67(String s67)
    {
        this.s67 = s67;
    }

    public String getS68()
    {
        return s68;
    }

    public void setS68(String s68)
    {
        this.s68 = s68;
    }

    public String getS69()
    {
        return s69;
    }

    public void setS69(String s69)
    {
        this.s69 = s69;
    }

    public String getS70()
    {
        return s70;
    }

    public void setS70(String s70)
    {
        this.s70 = s70;
    }

    public String getS71()
    {
        return s71;
    }

    public void setS71(String s71)
    {
        this.s71 = s71;
    }

    public String getS72()
    {
        return s72;
    }

    public void setS72(String s72)
    {
        this.s72 = s72;
    }

    public String getS73()
    {
        return s73;
    }

    public void setS73(String s73)
    {
        this.s73 = s73;
    }

    public String getS74()
    {
        return s74;
    }

    public void setS74(String s74)
    {
        this.s74 = s74;
    }

    public String getS75()
    {
        return s75;
    }

    public void setS75(String s75)
    {
        this.s75 = s75;
    }

    public String getS76()
    {
        return s76;
    }

    public void setS76(String s76)
    {
        this.s76 = s76;
    }

    public String getS77()
    {
        return s77;
    }

    public void setS77(String s77)
    {
        this.s77 = s77;
    }

    public String getS78()
    {
        return s78;
    }

    public void setS78(String s78)
    {
        this.s78 = s78;
    }

    public String getS79()
    {
        return s79;
    }

    public void setS79(String s79)
    {
        this.s79 = s79;
    }

    public String getS80()
    {
        return s80;
    }

    public void setS80(String s80)
    {
        this.s80 = s80;
    }

    public String getS81()
    {
        return s81;
    }

    public void setS81(String s81)
    {
        this.s81 = s81;
    }

    public String getS82()
    {
        return s82;
    }

    public void setS82(String s82)
    {
        this.s82 = s82;
    }

    public String getS83()
    {
        return s83;
    }

    public void setS83(String s83)
    {
        this.s83 = s83;
    }

    public String getS84()
    {
        return s84;
    }

    public void setS84(String s84)
    {
        this.s84 = s84;
    }

    public String getS85()
    {
        return s85;
    }

    public void setS85(String s85)
    {
        this.s85 = s85;
    }

    public String getS86()
    {
        return s86;
    }

    public void setS86(String s86)
    {
        this.s86 = s86;
    }

    public String getS87()
    {
        return s87;
    }

    public void setS87(String s87)
    {
        this.s87 = s87;
    }

    public String getS88()
    {
        return s88;
    }

    public void setS88(String s88)
    {
        this.s88 = s88;
    }

    public String getS89()
    {
        return s89;
    }

    public void setS89(String s89)
    {
        this.s89 = s89;
    }

    public String getS90()
    {
        return s90;
    }

    public void setS90(String s90)
    {
        this.s90 = s90;
    }

    public String getS91()
    {
        return s91;
    }

    public void setS91(String s91)
    {
        this.s91 = s91;
    }

    public String getS92()
    {
        return s92;
    }

    public void setS92(String s92)
    {
        this.s92 = s92;
    }

    public String getS93()
    {
        return s93;
    }

    public void setS93(String s93)
    {
        this.s93 = s93;
    }

    public String getS94()
    {
        return s94;
    }

    public void setS94(String s94)
    {
        this.s94 = s94;
    }

    public String getS95()
    {
        return s95;
    }

    public void setS95(String s95)
    {
        this.s95 = s95;
    }

    public String getS96()
    {
        return s96;
    }

    public void setS96(String s96)
    {
        this.s96 = s96;
    }

    public String getS97()
    {
        return s97;
    }

    public void setS97(String s97)
    {
        this.s97 = s97;
    }

    public String getS98()
    {
        return s98;
    }

    public void setS98(String s98)
    {
        this.s98 = s98;
    }

    public String getS99()
    {
        return s99;
    }

    public void setS99(String s99)
    {
        this.s99 = s99;
    }

    public String getS100()
    {
        return s100;
    }

    public void setS100(String s100)
    {
        this.s100 = s100;
    }

    public Date getD1()
    {
        return d1;
    }

    public void setD1(Date d1)
    {
        this.d1 = d1;
    }

    public Date getD2()
    {
        return d2;
    }

    public void setD2(Date d2)
    {
        this.d2 = d2;
    }

    public Date getD3()
    {
        return d3;
    }

    public void setD3(Date d3)
    {
        this.d3 = d3;
    }

    public Date getD4()
    {
        return d4;
    }

    public void setD4(Date d4)
    {
        this.d4 = d4;
    }

    public Date getD5()
    {
        return d5;
    }

    public void setD5(Date d5)
    {
        this.d5 = d5;
    }

    public Date getD6()
    {
        return d6;
    }

    public void setD6(Date d6)
    {
        this.d6 = d6;
    }

    public Date getD7()
    {
        return d7;
    }

    public void setD7(Date d7)
    {
        this.d7 = d7;
    }

    public Date getD8()
    {
        return d8;
    }

    public void setD8(Date d8)
    {
        this.d8 = d8;
    }

    public Date getD9()
    {
        return d9;
    }

    public void setD9(Date d9)
    {
        this.d9 = d9;
    }

    public Date getD10()
    {
        return d10;
    }

    public void setD10(Date d10)
    {
        this.d10 = d10;
    }

    public Date getD11()
    {
        return d11;
    }

    public void setD11(Date d11)
    {
        this.d11 = d11;
    }

    public Date getD12()
    {
        return d12;
    }

    public void setD12(Date d12)
    {
        this.d12 = d12;
    }

    public Date getD13()
    {
        return d13;
    }

    public void setD13(Date d13)
    {
        this.d13 = d13;
    }

    public Date getD14()
    {
        return d14;
    }

    public void setD14(Date d14)
    {
        this.d14 = d14;
    }

    public Date getD15()
    {
        return d15;
    }

    public void setD15(Date d15)
    {
        this.d15 = d15;
    }

    public Date getD16()
    {
        return d16;
    }

    public void setD16(Date d16)
    {
        this.d16 = d16;
    }

    public Date getD17()
    {
        return d17;
    }

    public void setD17(Date d17)
    {
        this.d17 = d17;
    }

    public Date getD18()
    {
        return d18;
    }

    public void setD18(Date d18)
    {
        this.d18 = d18;
    }

    public Date getD19()
    {
        return d19;
    }

    public void setD19(Date d19)
    {
        this.d19 = d19;
    }

    public Date getD20()
    {
        return d20;
    }

    public void setD20(Date d20)
    {
        this.d20 = d20;
    }

    public Date getD21()
    {
        return d21;
    }

    public void setD21(Date d21)
    {
        this.d21 = d21;
    }

    public Date getD22()
    {
        return d22;
    }

    public void setD22(Date d22)
    {
        this.d22 = d22;
    }

    public Date getD23()
    {
        return d23;
    }

    public void setD23(Date d23)
    {
        this.d23 = d23;
    }

    public Date getD24()
    {
        return d24;
    }

    public void setD24(Date d24)
    {
        this.d24 = d24;
    }

    public Date getD25()
    {
        return d25;
    }

    public void setD25(Date d25)
    {
        this.d25 = d25;
    }

    public Date getD26()
    {
        return d26;
    }

    public void setD26(Date d26)
    {
        this.d26 = d26;
    }

    public Date getD27()
    {
        return d27;
    }

    public void setD27(Date d27)
    {
        this.d27 = d27;
    }

    public Date getD28()
    {
        return d28;
    }

    public void setD28(Date d28)
    {
        this.d28 = d28;
    }

    public Date getD29()
    {
        return d29;
    }

    public void setD29(Date d29)
    {
        this.d29 = d29;
    }

    public Date getD30()
    {
        return d30;
    }

    public void setD30(Date d30)
    {
        this.d30 = d30;
    }

    public Date getD31()
    {
        return d31;
    }

    public void setD31(Date d31)
    {
        this.d31 = d31;
    }

    public Date getD32()
    {
        return d32;
    }

    public void setD32(Date d32)
    {
        this.d32 = d32;
    }

    public Date getD33()
    {
        return d33;
    }

    public void setD33(Date d33)
    {
        this.d33 = d33;
    }

    public Date getD34()
    {
        return d34;
    }

    public void setD34(Date d34)
    {
        this.d34 = d34;
    }

    public Date getD35()
    {
        return d35;
    }

    public void setD35(Date d35)
    {
        this.d35 = d35;
    }

    public Date getD36()
    {
        return d36;
    }

    public void setD36(Date d36)
    {
        this.d36 = d36;
    }

    public Date getD37()
    {
        return d37;
    }

    public void setD37(Date d37)
    {
        this.d37 = d37;
    }

    public Date getD38()
    {
        return d38;
    }

    public void setD38(Date d38)
    {
        this.d38 = d38;
    }

    public Date getD39()
    {
        return d39;
    }

    public void setD39(Date d39)
    {
        this.d39 = d39;
    }

    public Date getD40()
    {
        return d40;
    }

    public void setD40(Date d40)
    {
        this.d40 = d40;
    }

    public Date getD41()
    {
        return d41;
    }

    public void setD41(Date d41)
    {
        this.d41 = d41;
    }

    public Date getD42()
    {
        return d42;
    }

    public void setD42(Date d42)
    {
        this.d42 = d42;
    }

    public Date getD43()
    {
        return d43;
    }

    public void setD43(Date d43)
    {
        this.d43 = d43;
    }

    public Date getD44()
    {
        return d44;
    }

    public void setD44(Date d44)
    {
        this.d44 = d44;
    }

    public Date getD45()
    {
        return d45;
    }

    public void setD45(Date d45)
    {
        this.d45 = d45;
    }

    public Date getD46()
    {
        return d46;
    }

    public void setD46(Date d46)
    {
        this.d46 = d46;
    }

    public Date getD47()
    {
        return d47;
    }

    public void setD47(Date d47)
    {
        this.d47 = d47;
    }

    public Date getD48()
    {
        return d48;
    }

    public void setD48(Date d48)
    {
        this.d48 = d48;
    }

    public Date getD49()
    {
        return d49;
    }

    public void setD49(Date d49)
    {
        this.d49 = d49;
    }

    public Date getD50()
    {
        return d50;
    }

    public void setD50(Date d50)
    {
        this.d50 = d50;
    }

    public Date getD51()
    {
        return d51;
    }

    public void setD51(Date d51)
    {
        this.d51 = d51;
    }

    public Date getD52()
    {
        return d52;
    }

    public void setD52(Date d52)
    {
        this.d52 = d52;
    }

    public Date getD53()
    {
        return d53;
    }

    public void setD53(Date d53)
    {
        this.d53 = d53;
    }

    public Date getD54()
    {
        return d54;
    }

    public void setD54(Date d54)
    {
        this.d54 = d54;
    }

    public Date getD55()
    {
        return d55;
    }

    public void setD55(Date d55)
    {
        this.d55 = d55;
    }

    public Date getD56()
    {
        return d56;
    }

    public void setD56(Date d56)
    {
        this.d56 = d56;
    }

    public Date getD57()
    {
        return d57;
    }

    public void setD57(Date d57)
    {
        this.d57 = d57;
    }

    public Date getD58()
    {
        return d58;
    }

    public void setD58(Date d58)
    {
        this.d58 = d58;
    }

    public Date getD59()
    {
        return d59;
    }

    public void setD59(Date d59)
    {
        this.d59 = d59;
    }

    public Date getD60()
    {
        return d60;
    }

    public void setD60(Date d60)
    {
        this.d60 = d60;
    }

    public Date getD61()
    {
        return d61;
    }

    public void setD61(Date d61)
    {
        this.d61 = d61;
    }

    public Date getD62()
    {
        return d62;
    }

    public void setD62(Date d62)
    {
        this.d62 = d62;
    }

    public Date getD63()
    {
        return d63;
    }

    public void setD63(Date d63)
    {
        this.d63 = d63;
    }

    public Date getD64()
    {
        return d64;
    }

    public void setD64(Date d64)
    {
        this.d64 = d64;
    }

    public Date getD65()
    {
        return d65;
    }

    public void setD65(Date d65)
    {
        this.d65 = d65;
    }

    public Date getD66()
    {
        return d66;
    }

    public void setD66(Date d66)
    {
        this.d66 = d66;
    }

    public Date getD67()
    {
        return d67;
    }

    public void setD67(Date d67)
    {
        this.d67 = d67;
    }

    public Date getD68()
    {
        return d68;
    }

    public void setD68(Date d68)
    {
        this.d68 = d68;
    }

    public Date getD69()
    {
        return d69;
    }

    public void setD69(Date d69)
    {
        this.d69 = d69;
    }

    public Date getD70()
    {
        return d70;
    }

    public void setD70(Date d70)
    {
        this.d70 = d70;
    }

    public Date getD71()
    {
        return d71;
    }

    public void setD71(Date d71)
    {
        this.d71 = d71;
    }

    public Date getD72()
    {
        return d72;
    }

    public void setD72(Date d72)
    {
        this.d72 = d72;
    }

    public Date getD73()
    {
        return d73;
    }

    public void setD73(Date d73)
    {
        this.d73 = d73;
    }

    public Date getD74()
    {
        return d74;
    }

    public void setD74(Date d74)
    {
        this.d74 = d74;
    }

    public Date getD75()
    {
        return d75;
    }

    public void setD75(Date d75)
    {
        this.d75 = d75;
    }

    public Date getD76()
    {
        return d76;
    }

    public void setD76(Date d76)
    {
        this.d76 = d76;
    }

    public Date getD77()
    {
        return d77;
    }

    public void setD77(Date d77)
    {
        this.d77 = d77;
    }

    public Date getD78()
    {
        return d78;
    }

    public void setD78(Date d78)
    {
        this.d78 = d78;
    }

    public Date getD79()
    {
        return d79;
    }

    public void setD79(Date d79)
    {
        this.d79 = d79;
    }

    public Date getD80()
    {
        return d80;
    }

    public void setD80(Date d80)
    {
        this.d80 = d80;
    }

    public Date getD81()
    {
        return d81;
    }

    public void setD81(Date d81)
    {
        this.d81 = d81;
    }

    public Date getD82()
    {
        return d82;
    }

    public void setD82(Date d82)
    {
        this.d82 = d82;
    }

    public Date getD83()
    {
        return d83;
    }

    public void setD83(Date d83)
    {
        this.d83 = d83;
    }

    public Date getD84()
    {
        return d84;
    }

    public void setD84(Date d84)
    {
        this.d84 = d84;
    }

    public Date getD85()
    {
        return d85;
    }

    public void setD85(Date d85)
    {
        this.d85 = d85;
    }

    public Date getD86()
    {
        return d86;
    }

    public void setD86(Date d86)
    {
        this.d86 = d86;
    }

    public Date getD87()
    {
        return d87;
    }

    public void setD87(Date d87)
    {
        this.d87 = d87;
    }

    public Date getD88()
    {
        return d88;
    }

    public void setD88(Date d88)
    {
        this.d88 = d88;
    }

    public Date getD89()
    {
        return d89;
    }

    public void setD89(Date d89)
    {
        this.d89 = d89;
    }

    public Date getD90()
    {
        return d90;
    }

    public void setD90(Date d90)
    {
        this.d90 = d90;
    }

    public Date getD91()
    {
        return d91;
    }

    public void setD91(Date d91)
    {
        this.d91 = d91;
    }

    public Date getD92()
    {
        return d92;
    }

    public void setD92(Date d92)
    {
        this.d92 = d92;
    }

    public Date getD93()
    {
        return d93;
    }

    public void setD93(Date d93)
    {
        this.d93 = d93;
    }

    public Date getD94()
    {
        return d94;
    }

    public void setD94(Date d94)
    {
        this.d94 = d94;
    }

    public Date getD95()
    {
        return d95;
    }

    public void setD95(Date d95)
    {
        this.d95 = d95;
    }

    public Date getD96()
    {
        return d96;
    }

    public void setD96(Date d96)
    {
        this.d96 = d96;
    }

    public Date getD97()
    {
        return d97;
    }

    public void setD97(Date d97)
    {
        this.d97 = d97;
    }

    public Date getD98()
    {
        return d98;
    }

    public void setD98(Date d98)
    {
        this.d98 = d98;
    }

    public Date getD99()
    {
        return d99;
    }

    public void setD99(Date d99)
    {
        this.d99 = d99;
    }

    public Date getD100()
    {
        return d100;
    }

    public void setD100(Date d100)
    {
        this.d100 = d100;
    }

    public int getI1()
    {
        return i1;
    }

    public void setI1(int i1)
    {
        this.i1 = i1;
    }

    public int getI2()
    {
        return i2;
    }

    public void setI2(int i2)
    {
        this.i2 = i2;
    }

    public int getI3()
    {
        return i3;
    }

    public void setI3(int i3)
    {
        this.i3 = i3;
    }

    public int getI4()
    {
        return i4;
    }

    public void setI4(int i4)
    {
        this.i4 = i4;
    }

    public int getI5()
    {
        return i5;
    }

    public void setI5(int i5)
    {
        this.i5 = i5;
    }

    public int getI6()
    {
        return i6;
    }

    public void setI6(int i6)
    {
        this.i6 = i6;
    }

    public int getI7()
    {
        return i7;
    }

    public void setI7(int i7)
    {
        this.i7 = i7;
    }

    public int getI8()
    {
        return i8;
    }

    public void setI8(int i8)
    {
        this.i8 = i8;
    }

    public int getI9()
    {
        return i9;
    }

    public void setI9(int i9)
    {
        this.i9 = i9;
    }

    public int getI10()
    {
        return i10;
    }

    public void setI10(int i10)
    {
        this.i10 = i10;
    }

    public int getI11()
    {
        return i11;
    }

    public void setI11(int i11)
    {
        this.i11 = i11;
    }

    public int getI12()
    {
        return i12;
    }

    public void setI12(int i12)
    {
        this.i12 = i12;
    }

    public int getI13()
    {
        return i13;
    }

    public void setI13(int i13)
    {
        this.i13 = i13;
    }

    public int getI14()
    {
        return i14;
    }

    public void setI14(int i14)
    {
        this.i14 = i14;
    }

    public int getI15()
    {
        return i15;
    }

    public void setI15(int i15)
    {
        this.i15 = i15;
    }

    public int getI16()
    {
        return i16;
    }

    public void setI16(int i16)
    {
        this.i16 = i16;
    }

    public int getI17()
    {
        return i17;
    }

    public void setI17(int i17)
    {
        this.i17 = i17;
    }

    public int getI18()
    {
        return i18;
    }

    public void setI18(int i18)
    {
        this.i18 = i18;
    }

    public int getI19()
    {
        return i19;
    }

    public void setI19(int i19)
    {
        this.i19 = i19;
    }

    public int getI20()
    {
        return i20;
    }

    public void setI20(int i20)
    {
        this.i20 = i20;
    }

    public int getI21()
    {
        return i21;
    }

    public void setI21(int i21)
    {
        this.i21 = i21;
    }

    public int getI22()
    {
        return i22;
    }

    public void setI22(int i22)
    {
        this.i22 = i22;
    }

    public int getI23()
    {
        return i23;
    }

    public void setI23(int i23)
    {
        this.i23 = i23;
    }

    public int getI24()
    {
        return i24;
    }

    public void setI24(int i24)
    {
        this.i24 = i24;
    }

    public int getI25()
    {
        return i25;
    }

    public void setI25(int i25)
    {
        this.i25 = i25;
    }

    public int getI26()
    {
        return i26;
    }

    public void setI26(int i26)
    {
        this.i26 = i26;
    }

    public int getI27()
    {
        return i27;
    }

    public void setI27(int i27)
    {
        this.i27 = i27;
    }

    public int getI28()
    {
        return i28;
    }

    public void setI28(int i28)
    {
        this.i28 = i28;
    }

    public int getI29()
    {
        return i29;
    }

    public void setI29(int i29)
    {
        this.i29 = i29;
    }

    public int getI30()
    {
        return i30;
    }

    public void setI30(int i30)
    {
        this.i30 = i30;
    }

    public int getI31()
    {
        return i31;
    }

    public void setI31(int i31)
    {
        this.i31 = i31;
    }

    public int getI32()
    {
        return i32;
    }

    public void setI32(int i32)
    {
        this.i32 = i32;
    }

    public int getI33()
    {
        return i33;
    }

    public void setI33(int i33)
    {
        this.i33 = i33;
    }

    public int getI34()
    {
        return i34;
    }

    public void setI34(int i34)
    {
        this.i34 = i34;
    }

    public int getI35()
    {
        return i35;
    }

    public void setI35(int i35)
    {
        this.i35 = i35;
    }

    public int getI36()
    {
        return i36;
    }

    public void setI36(int i36)
    {
        this.i36 = i36;
    }

    public int getI37()
    {
        return i37;
    }

    public void setI37(int i37)
    {
        this.i37 = i37;
    }

    public int getI38()
    {
        return i38;
    }

    public void setI38(int i38)
    {
        this.i38 = i38;
    }

    public int getI39()
    {
        return i39;
    }

    public void setI39(int i39)
    {
        this.i39 = i39;
    }

    public int getI40()
    {
        return i40;
    }

    public void setI40(int i40)
    {
        this.i40 = i40;
    }

    public int getI41()
    {
        return i41;
    }

    public void setI41(int i41)
    {
        this.i41 = i41;
    }

    public int getI42()
    {
        return i42;
    }

    public void setI42(int i42)
    {
        this.i42 = i42;
    }

    public int getI43()
    {
        return i43;
    }

    public void setI43(int i43)
    {
        this.i43 = i43;
    }

    public int getI44()
    {
        return i44;
    }

    public void setI44(int i44)
    {
        this.i44 = i44;
    }

    public int getI45()
    {
        return i45;
    }

    public void setI45(int i45)
    {
        this.i45 = i45;
    }

    public int getI46()
    {
        return i46;
    }

    public void setI46(int i46)
    {
        this.i46 = i46;
    }

    public int getI47()
    {
        return i47;
    }

    public void setI47(int i47)
    {
        this.i47 = i47;
    }

    public int getI48()
    {
        return i48;
    }

    public void setI48(int i48)
    {
        this.i48 = i48;
    }

    public int getI49()
    {
        return i49;
    }

    public void setI49(int i49)
    {
        this.i49 = i49;
    }

    public int getI50()
    {
        return i50;
    }

    public void setI50(int i50)
    {
        this.i50 = i50;
    }

    public int getI51()
    {
        return i51;
    }

    public void setI51(int i51)
    {
        this.i51 = i51;
    }

    public int getI52()
    {
        return i52;
    }

    public void setI52(int i52)
    {
        this.i52 = i52;
    }

    public int getI53()
    {
        return i53;
    }

    public void setI53(int i53)
    {
        this.i53 = i53;
    }

    public int getI54()
    {
        return i54;
    }

    public void setI54(int i54)
    {
        this.i54 = i54;
    }

    public int getI55()
    {
        return i55;
    }

    public void setI55(int i55)
    {
        this.i55 = i55;
    }

    public int getI56()
    {
        return i56;
    }

    public void setI56(int i56)
    {
        this.i56 = i56;
    }

    public int getI57()
    {
        return i57;
    }

    public void setI57(int i57)
    {
        this.i57 = i57;
    }

    public int getI58()
    {
        return i58;
    }

    public void setI58(int i58)
    {
        this.i58 = i58;
    }

    public int getI59()
    {
        return i59;
    }

    public void setI59(int i59)
    {
        this.i59 = i59;
    }

    public int getI60()
    {
        return i60;
    }

    public void setI60(int i60)
    {
        this.i60 = i60;
    }

    public int getI61()
    {
        return i61;
    }

    public void setI61(int i61)
    {
        this.i61 = i61;
    }

    public int getI62()
    {
        return i62;
    }

    public void setI62(int i62)
    {
        this.i62 = i62;
    }

    public int getI63()
    {
        return i63;
    }

    public void setI63(int i63)
    {
        this.i63 = i63;
    }

    public int getI64()
    {
        return i64;
    }

    public void setI64(int i64)
    {
        this.i64 = i64;
    }

    public int getI65()
    {
        return i65;
    }

    public void setI65(int i65)
    {
        this.i65 = i65;
    }

    public int getI66()
    {
        return i66;
    }

    public void setI66(int i66)
    {
        this.i66 = i66;
    }

    public int getI67()
    {
        return i67;
    }

    public void setI67(int i67)
    {
        this.i67 = i67;
    }

    public int getI68()
    {
        return i68;
    }

    public void setI68(int i68)
    {
        this.i68 = i68;
    }

    public int getI69()
    {
        return i69;
    }

    public void setI69(int i69)
    {
        this.i69 = i69;
    }

    public int getI70()
    {
        return i70;
    }

    public void setI70(int i70)
    {
        this.i70 = i70;
    }

    public int getI71()
    {
        return i71;
    }

    public void setI71(int i71)
    {
        this.i71 = i71;
    }

    public int getI72()
    {
        return i72;
    }

    public void setI72(int i72)
    {
        this.i72 = i72;
    }

    public int getI73()
    {
        return i73;
    }

    public void setI73(int i73)
    {
        this.i73 = i73;
    }

    public int getI74()
    {
        return i74;
    }

    public void setI74(int i74)
    {
        this.i74 = i74;
    }

    public int getI75()
    {
        return i75;
    }

    public void setI75(int i75)
    {
        this.i75 = i75;
    }

    public int getI76()
    {
        return i76;
    }

    public void setI76(int i76)
    {
        this.i76 = i76;
    }

    public int getI77()
    {
        return i77;
    }

    public void setI77(int i77)
    {
        this.i77 = i77;
    }

    public int getI78()
    {
        return i78;
    }

    public void setI78(int i78)
    {
        this.i78 = i78;
    }

    public int getI79()
    {
        return i79;
    }

    public void setI79(int i79)
    {
        this.i79 = i79;
    }

    public int getI80()
    {
        return i80;
    }

    public void setI80(int i80)
    {
        this.i80 = i80;
    }

    public int getI81()
    {
        return i81;
    }

    public void setI81(int i81)
    {
        this.i81 = i81;
    }

    public int getI82()
    {
        return i82;
    }

    public void setI82(int i82)
    {
        this.i82 = i82;
    }

    public int getI83()
    {
        return i83;
    }

    public void setI83(int i83)
    {
        this.i83 = i83;
    }

    public int getI84()
    {
        return i84;
    }

    public void setI84(int i84)
    {
        this.i84 = i84;
    }

    public int getI85()
    {
        return i85;
    }

    public void setI85(int i85)
    {
        this.i85 = i85;
    }

    public int getI86()
    {
        return i86;
    }

    public void setI86(int i86)
    {
        this.i86 = i86;
    }

    public int getI87()
    {
        return i87;
    }

    public void setI87(int i87)
    {
        this.i87 = i87;
    }

    public int getI88()
    {
        return i88;
    }

    public void setI88(int i88)
    {
        this.i88 = i88;
    }

    public int getI89()
    {
        return i89;
    }

    public void setI89(int i89)
    {
        this.i89 = i89;
    }

    public int getI90()
    {
        return i90;
    }

    public void setI90(int i90)
    {
        this.i90 = i90;
    }

    public int getI91()
    {
        return i91;
    }

    public void setI91(int i91)
    {
        this.i91 = i91;
    }

    public int getI92()
    {
        return i92;
    }

    public void setI92(int i92)
    {
        this.i92 = i92;
    }

    public int getI93()
    {
        return i93;
    }

    public void setI93(int i93)
    {
        this.i93 = i93;
    }

    public int getI94()
    {
        return i94;
    }

    public void setI94(int i94)
    {
        this.i94 = i94;
    }

    public int getI95()
    {
        return i95;
    }

    public void setI95(int i95)
    {
        this.i95 = i95;
    }

    public int getI96()
    {
        return i96;
    }

    public void setI96(int i96)
    {
        this.i96 = i96;
    }

    public int getI97()
    {
        return i97;
    }

    public void setI97(int i97)
    {
        this.i97 = i97;
    }

    public int getI98()
    {
        return i98;
    }

    public void setI98(int i98)
    {
        this.i98 = i98;
    }

    public int getI99()
    {
        return i99;
    }

    public void setI99(int i99)
    {
        this.i99 = i99;
    }

    public int getI100()
    {
        return i100;
    }

    public void setI100(int i100)
    {
        this.i100 = i100;
    }

    public boolean isB1()
    {
        return b1;
    }

    public void setB1(boolean b1)
    {
        this.b1 = b1;
    }

    public boolean isB2()
    {
        return b2;
    }

    public void setB2(boolean b2)
    {
        this.b2 = b2;
    }

    public boolean isB3()
    {
        return b3;
    }

    public void setB3(boolean b3)
    {
        this.b3 = b3;
    }

    public boolean isB4()
    {
        return b4;
    }

    public void setB4(boolean b4)
    {
        this.b4 = b4;
    }

    public boolean isB5()
    {
        return b5;
    }

    public void setB5(boolean b5)
    {
        this.b5 = b5;
    }

    public boolean isB6()
    {
        return b6;
    }

    public void setB6(boolean b6)
    {
        this.b6 = b6;
    }

    public boolean isB7()
    {
        return b7;
    }

    public void setB7(boolean b7)
    {
        this.b7 = b7;
    }

    public boolean isB8()
    {
        return b8;
    }

    public void setB8(boolean b8)
    {
        this.b8 = b8;
    }

    public boolean isB9()
    {
        return b9;
    }

    public void setB9(boolean b9)
    {
        this.b9 = b9;
    }

    public boolean isB10()
    {
        return b10;
    }

    public void setB10(boolean b10)
    {
        this.b10 = b10;
    }

    public boolean isB11()
    {
        return b11;
    }

    public void setB11(boolean b11)
    {
        this.b11 = b11;
    }

    public boolean isB12()
    {
        return b12;
    }

    public void setB12(boolean b12)
    {
        this.b12 = b12;
    }

    public boolean isB13()
    {
        return b13;
    }

    public void setB13(boolean b13)
    {
        this.b13 = b13;
    }

    public boolean isB14()
    {
        return b14;
    }

    public void setB14(boolean b14)
    {
        this.b14 = b14;
    }

    public boolean isB15()
    {
        return b15;
    }

    public void setB15(boolean b15)
    {
        this.b15 = b15;
    }

    public boolean isB16()
    {
        return b16;
    }

    public void setB16(boolean b16)
    {
        this.b16 = b16;
    }

    public boolean isB17()
    {
        return b17;
    }

    public void setB17(boolean b17)
    {
        this.b17 = b17;
    }

    public boolean isB18()
    {
        return b18;
    }

    public void setB18(boolean b18)
    {
        this.b18 = b18;
    }

    public boolean isB19()
    {
        return b19;
    }

    public void setB19(boolean b19)
    {
        this.b19 = b19;
    }

    public boolean isB20()
    {
        return b20;
    }

    public void setB20(boolean b20)
    {
        this.b20 = b20;
    }

    public boolean isB21()
    {
        return b21;
    }

    public void setB21(boolean b21)
    {
        this.b21 = b21;
    }

    public boolean isB22()
    {
        return b22;
    }

    public void setB22(boolean b22)
    {
        this.b22 = b22;
    }

    public boolean isB23()
    {
        return b23;
    }

    public void setB23(boolean b23)
    {
        this.b23 = b23;
    }

    public boolean isB24()
    {
        return b24;
    }

    public void setB24(boolean b24)
    {
        this.b24 = b24;
    }

    public boolean isB25()
    {
        return b25;
    }

    public void setB25(boolean b25)
    {
        this.b25 = b25;
    }

    public boolean isB26()
    {
        return b26;
    }

    public void setB26(boolean b26)
    {
        this.b26 = b26;
    }

    public boolean isB27()
    {
        return b27;
    }

    public void setB27(boolean b27)
    {
        this.b27 = b27;
    }

    public boolean isB28()
    {
        return b28;
    }

    public void setB28(boolean b28)
    {
        this.b28 = b28;
    }

    public boolean isB29()
    {
        return b29;
    }

    public void setB29(boolean b29)
    {
        this.b29 = b29;
    }

    public boolean isB30()
    {
        return b30;
    }

    public void setB30(boolean b30)
    {
        this.b30 = b30;
    }

    public boolean isB31()
    {
        return b31;
    }

    public void setB31(boolean b31)
    {
        this.b31 = b31;
    }

    public boolean isB32()
    {
        return b32;
    }

    public void setB32(boolean b32)
    {
        this.b32 = b32;
    }

    public boolean isB33()
    {
        return b33;
    }

    public void setB33(boolean b33)
    {
        this.b33 = b33;
    }

    public boolean isB34()
    {
        return b34;
    }

    public void setB34(boolean b34)
    {
        this.b34 = b34;
    }

    public boolean isB35()
    {
        return b35;
    }

    public void setB35(boolean b35)
    {
        this.b35 = b35;
    }

    public boolean isB36()
    {
        return b36;
    }

    public void setB36(boolean b36)
    {
        this.b36 = b36;
    }

    public boolean isB37()
    {
        return b37;
    }

    public void setB37(boolean b37)
    {
        this.b37 = b37;
    }

    public boolean isB38()
    {
        return b38;
    }

    public void setB38(boolean b38)
    {
        this.b38 = b38;
    }

    public boolean isB39()
    {
        return b39;
    }

    public void setB39(boolean b39)
    {
        this.b39 = b39;
    }

    public boolean isB40()
    {
        return b40;
    }

    public void setB40(boolean b40)
    {
        this.b40 = b40;
    }

    public boolean isB41()
    {
        return b41;
    }

    public void setB41(boolean b41)
    {
        this.b41 = b41;
    }

    public boolean isB42()
    {
        return b42;
    }

    public void setB42(boolean b42)
    {
        this.b42 = b42;
    }

    public boolean isB43()
    {
        return b43;
    }

    public void setB43(boolean b43)
    {
        this.b43 = b43;
    }

    public boolean isB44()
    {
        return b44;
    }

    public void setB44(boolean b44)
    {
        this.b44 = b44;
    }

    public boolean isB45()
    {
        return b45;
    }

    public void setB45(boolean b45)
    {
        this.b45 = b45;
    }

    public boolean isB46()
    {
        return b46;
    }

    public void setB46(boolean b46)
    {
        this.b46 = b46;
    }

    public boolean isB47()
    {
        return b47;
    }

    public void setB47(boolean b47)
    {
        this.b47 = b47;
    }

    public boolean isB48()
    {
        return b48;
    }

    public void setB48(boolean b48)
    {
        this.b48 = b48;
    }

    public boolean isB49()
    {
        return b49;
    }

    public void setB49(boolean b49)
    {
        this.b49 = b49;
    }

    public boolean isB50()
    {
        return b50;
    }

    public void setB50(boolean b50)
    {
        this.b50 = b50;
    }

    public boolean isB51()
    {
        return b51;
    }

    public void setB51(boolean b51)
    {
        this.b51 = b51;
    }

    public boolean isB52()
    {
        return b52;
    }

    public void setB52(boolean b52)
    {
        this.b52 = b52;
    }

    public boolean isB53()
    {
        return b53;
    }

    public void setB53(boolean b53)
    {
        this.b53 = b53;
    }

    public boolean isB54()
    {
        return b54;
    }

    public void setB54(boolean b54)
    {
        this.b54 = b54;
    }

    public boolean isB55()
    {
        return b55;
    }

    public void setB55(boolean b55)
    {
        this.b55 = b55;
    }

    public boolean isB56()
    {
        return b56;
    }

    public void setB56(boolean b56)
    {
        this.b56 = b56;
    }

    public boolean isB57()
    {
        return b57;
    }

    public void setB57(boolean b57)
    {
        this.b57 = b57;
    }

    public boolean isB58()
    {
        return b58;
    }

    public void setB58(boolean b58)
    {
        this.b58 = b58;
    }

    public boolean isB59()
    {
        return b59;
    }

    public void setB59(boolean b59)
    {
        this.b59 = b59;
    }

    public boolean isB60()
    {
        return b60;
    }

    public void setB60(boolean b60)
    {
        this.b60 = b60;
    }

    public boolean isB61()
    {
        return b61;
    }

    public void setB61(boolean b61)
    {
        this.b61 = b61;
    }

    public boolean isB62()
    {
        return b62;
    }

    public void setB62(boolean b62)
    {
        this.b62 = b62;
    }

    public boolean isB63()
    {
        return b63;
    }

    public void setB63(boolean b63)
    {
        this.b63 = b63;
    }

    public boolean isB64()
    {
        return b64;
    }

    public void setB64(boolean b64)
    {
        this.b64 = b64;
    }

    public boolean isB65()
    {
        return b65;
    }

    public void setB65(boolean b65)
    {
        this.b65 = b65;
    }

    public boolean isB66()
    {
        return b66;
    }

    public void setB66(boolean b66)
    {
        this.b66 = b66;
    }

    public boolean isB67()
    {
        return b67;
    }

    public void setB67(boolean b67)
    {
        this.b67 = b67;
    }

    public boolean isB68()
    {
        return b68;
    }

    public void setB68(boolean b68)
    {
        this.b68 = b68;
    }

    public boolean isB69()
    {
        return b69;
    }

    public void setB69(boolean b69)
    {
        this.b69 = b69;
    }

    public boolean isB70()
    {
        return b70;
    }

    public void setB70(boolean b70)
    {
        this.b70 = b70;
    }

    public boolean isB71()
    {
        return b71;
    }

    public void setB71(boolean b71)
    {
        this.b71 = b71;
    }

    public boolean isB72()
    {
        return b72;
    }

    public void setB72(boolean b72)
    {
        this.b72 = b72;
    }

    public boolean isB73()
    {
        return b73;
    }

    public void setB73(boolean b73)
    {
        this.b73 = b73;
    }

    public boolean isB74()
    {
        return b74;
    }

    public void setB74(boolean b74)
    {
        this.b74 = b74;
    }

    public boolean isB75()
    {
        return b75;
    }

    public void setB75(boolean b75)
    {
        this.b75 = b75;
    }

    public boolean isB76()
    {
        return b76;
    }

    public void setB76(boolean b76)
    {
        this.b76 = b76;
    }

    public boolean isB77()
    {
        return b77;
    }

    public void setB77(boolean b77)
    {
        this.b77 = b77;
    }

    public boolean isB78()
    {
        return b78;
    }

    public void setB78(boolean b78)
    {
        this.b78 = b78;
    }

    public boolean isB79()
    {
        return b79;
    }

    public void setB79(boolean b79)
    {
        this.b79 = b79;
    }

    public boolean isB80()
    {
        return b80;
    }

    public void setB80(boolean b80)
    {
        this.b80 = b80;
    }

    public boolean isB81()
    {
        return b81;
    }

    public void setB81(boolean b81)
    {
        this.b81 = b81;
    }

    public boolean isB82()
    {
        return b82;
    }

    public void setB82(boolean b82)
    {
        this.b82 = b82;
    }

    public boolean isB83()
    {
        return b83;
    }

    public void setB83(boolean b83)
    {
        this.b83 = b83;
    }

    public boolean isB84()
    {
        return b84;
    }

    public void setB84(boolean b84)
    {
        this.b84 = b84;
    }

    public boolean isB85()
    {
        return b85;
    }

    public void setB85(boolean b85)
    {
        this.b85 = b85;
    }

    public boolean isB86()
    {
        return b86;
    }

    public void setB86(boolean b86)
    {
        this.b86 = b86;
    }

    public boolean isB87()
    {
        return b87;
    }

    public void setB87(boolean b87)
    {
        this.b87 = b87;
    }

    public boolean isB88()
    {
        return b88;
    }

    public void setB88(boolean b88)
    {
        this.b88 = b88;
    }

    public boolean isB89()
    {
        return b89;
    }

    public void setB89(boolean b89)
    {
        this.b89 = b89;
    }

    public boolean isB90()
    {
        return b90;
    }

    public void setB90(boolean b90)
    {
        this.b90 = b90;
    }

    public boolean isB91()
    {
        return b91;
    }

    public void setB91(boolean b91)
    {
        this.b91 = b91;
    }

    public boolean isB92()
    {
        return b92;
    }

    public void setB92(boolean b92)
    {
        this.b92 = b92;
    }

    public boolean isB93()
    {
        return b93;
    }

    public void setB93(boolean b93)
    {
        this.b93 = b93;
    }

    public boolean isB94()
    {
        return b94;
    }

    public void setB94(boolean b94)
    {
        this.b94 = b94;
    }

    public boolean isB95()
    {
        return b95;
    }

    public void setB95(boolean b95)
    {
        this.b95 = b95;
    }

    public boolean isB96()
    {
        return b96;
    }

    public void setB96(boolean b96)
    {
        this.b96 = b96;
    }

    public boolean isB97()
    {
        return b97;
    }

    public void setB97(boolean b97)
    {
        this.b97 = b97;
    }

    public boolean isB98()
    {
        return b98;
    }

    public void setB98(boolean b98)
    {
        this.b98 = b98;
    }

    public boolean isB99()
    {
        return b99;
    }

    public void setB99(boolean b99)
    {
        this.b99 = b99;
    }

    public boolean isB100()
    {
        return b100;
    }

    public void setB100(boolean b100)
    {
        this.b100 = b100;
    }

    public String getT1()
    {
        return t1;
    }

    public String getT2()
    {
        return t2;
    }

    public void setT2(String t2)
    {
        this.t2 = t2;
    }

    public String getT3()
    {
        return t3;
    }

    public void setT3(String t3)
    {
        this.t3 = t3;
    }

    public String getT4()
    {
        return t4;
    }

    public void setT4(String t4)
    {
        this.t4 = t4;
    }

    public String getT5()
    {
        return t5;
    }

    public void setT5(String t5)
    {
        this.t5 = t5;
    }

    public String getT6()
    {
        return t6;
    }

    public void setT6(String t6)
    {
        this.t6 = t6;
    }

    public String getT7()
    {
        return t7;
    }

    public void setT7(String t7)
    {
        this.t7 = t7;
    }

    public String getT8()
    {
        return t8;
    }

    public void setT8(String t8)
    {
        this.t8 = t8;
    }

    public String getT9()
    {
        return t9;
    }

    public void setT9(String t9)
    {
        this.t9 = t9;
    }

    public String getT10()
    {
        return t10;
    }

    public void setT10(String t10)
    {
        this.t10 = t10;
    }

    public String getT11()
    {
        return t11;
    }

    public void setT11(String t11)
    {
        this.t11 = t11;
    }

    public String getT12()
    {
        return t12;
    }

    public void setT12(String t12)
    {
        this.t12 = t12;
    }

    public String getT13()
    {
        return t13;
    }

    public void setT13(String t13)
    {
        this.t13 = t13;
    }

    public String getT14()
    {
        return t14;
    }

    public void setT14(String t14)
    {
        this.t14 = t14;
    }

    public String getT15()
    {
        return t15;
    }

    public void setT15(String t15)
    {
        this.t15 = t15;
    }

    public String getT16()
    {
        return t16;
    }

    public void setT16(String t16)
    {
        this.t16 = t16;
    }

    public String getT17()
    {
        return t17;
    }

    public void setT17(String t17)
    {
        this.t17 = t17;
    }

    public String getT18()
    {
        return t18;
    }

    public void setT18(String t18)
    {
        this.t18 = t18;
    }

    public String getT19()
    {
        return t19;
    }

    public void setT19(String t19)
    {
        this.t19 = t19;
    }

    public String getT20()
    {
        return t20;
    }

    public void setT20(String t20)
    {
        this.t20 = t20;
    }

    public String getT21()
    {
        return t21;
    }

    public void setT21(String t21)
    {
        this.t21 = t21;
    }

    public String getT22()
    {
        return t22;
    }

    public void setT22(String t22)
    {
        this.t22 = t22;
    }

    public String getT23()
    {
        return t23;
    }

    public void setT23(String t23)
    {
        this.t23 = t23;
    }

    public String getT24()
    {
        return t24;
    }

    public void setT24(String t24)
    {
        this.t24 = t24;
    }

    public String getT25()
    {
        return t25;
    }

    public void setT25(String t25)
    {
        this.t25 = t25;
    }

    public String getT26()
    {
        return t26;
    }

    public void setT26(String t26)
    {
        this.t26 = t26;
    }

    public String getT27()
    {
        return t27;
    }

    public void setT27(String t27)
    {
        this.t27 = t27;
    }

    public String getT28()
    {
        return t28;
    }

    public void setT28(String t28)
    {
        this.t28 = t28;
    }

    public String getT29()
    {
        return t29;
    }

    public void setT29(String t29)
    {
        this.t29 = t29;
    }

    public String getT30()
    {
        return t30;
    }

    public void setT30(String t30)
    {
        this.t30 = t30;
    }

    public String getT31()
    {
        return t31;
    }

    public void setT31(String t31)
    {
        this.t31 = t31;
    }

    public String getT32()
    {
        return t32;
    }

    public void setT32(String t32)
    {
        this.t32 = t32;
    }

    public String getT33()
    {
        return t33;
    }

    public void setT33(String t33)
    {
        this.t33 = t33;
    }

    public String getT34()
    {
        return t34;
    }

    public void setT34(String t34)
    {
        this.t34 = t34;
    }

    public String getT35()
    {
        return t35;
    }

    public void setT35(String t35)
    {
        this.t35 = t35;
    }

    public String getT36()
    {
        return t36;
    }

    public void setT36(String t36)
    {
        this.t36 = t36;
    }

    public String getT37()
    {
        return t37;
    }

    public void setT37(String t37)
    {
        this.t37 = t37;
    }

    public String getT38()
    {
        return t38;
    }

    public void setT38(String t38)
    {
        this.t38 = t38;
    }

    public String getT39()
    {
        return t39;
    }

    public void setT39(String t39)
    {
        this.t39 = t39;
    }

    public String getT40()
    {
        return t40;
    }

    public void setT40(String t40)
    {
        this.t40 = t40;
    }

    public String getT41()
    {
        return t41;
    }

    public void setT41(String t41)
    {
        this.t41 = t41;
    }

    public String getT42()
    {
        return t42;
    }

    public void setT42(String t42)
    {
        this.t42 = t42;
    }

    public String getT43()
    {
        return t43;
    }

    public void setT43(String t43)
    {
        this.t43 = t43;
    }

    public String getT44()
    {
        return t44;
    }

    public void setT44(String t44)
    {
        this.t44 = t44;
    }

    public String getT45()
    {
        return t45;
    }

    public void setT45(String t45)
    {
        this.t45 = t45;
    }

    public String getT46()
    {
        return t46;
    }

    public void setT46(String t46)
    {
        this.t46 = t46;
    }

    public String getT47()
    {
        return t47;
    }

    public void setT47(String t47)
    {
        this.t47 = t47;
    }

    public String getT48()
    {
        return t48;
    }

    public void setT48(String t48)
    {
        this.t48 = t48;
    }

    public String getT49()
    {
        return t49;
    }

    public void setT49(String t49)
    {
        this.t49 = t49;
    }

    public String getT50()
    {
        return t50;
    }

    public void setT50(String t50)
    {
        this.t50 = t50;
    }

    public String getT51()
    {
        return t51;
    }

    public void setT51(String t51)
    {
        this.t51 = t51;
    }

    public String getT52()
    {
        return t52;
    }

    public void setT52(String t52)
    {
        this.t52 = t52;
    }

    public String getT53()
    {
        return t53;
    }

    public void setT53(String t53)
    {
        this.t53 = t53;
    }

    public String getT54()
    {
        return t54;
    }

    public void setT54(String t54)
    {
        this.t54 = t54;
    }

    public String getT55()
    {
        return t55;
    }

    public void setT55(String t55)
    {
        this.t55 = t55;
    }

    public String getT56()
    {
        return t56;
    }

    public void setT56(String t56)
    {
        this.t56 = t56;
    }

    public String getT57()
    {
        return t57;
    }

    public void setT57(String t57)
    {
        this.t57 = t57;
    }

    public String getT58()
    {
        return t58;
    }

    public void setT58(String t58)
    {
        this.t58 = t58;
    }

    public String getT59()
    {
        return t59;
    }

    public void setT59(String t59)
    {
        this.t59 = t59;
    }

    public String getT60()
    {
        return t60;
    }

    public void setT60(String t60)
    {
        this.t60 = t60;
    }

    public String getT61()
    {
        return t61;
    }

    public void setT61(String t61)
    {
        this.t61 = t61;
    }

    public String getT62()
    {
        return t62;
    }

    public void setT62(String t62)
    {
        this.t62 = t62;
    }

    public String getT63()
    {
        return t63;
    }

    public void setT63(String t63)
    {
        this.t63 = t63;
    }

    public String getT64()
    {
        return t64;
    }

    public void setT64(String t64)
    {
        this.t64 = t64;
    }

    public String getT65()
    {
        return t65;
    }

    public void setT65(String t65)
    {
        this.t65 = t65;
    }

    public String getT66()
    {
        return t66;
    }

    public void setT66(String t66)
    {
        this.t66 = t66;
    }

    public String getT67()
    {
        return t67;
    }

    public void setT67(String t67)
    {
        this.t67 = t67;
    }

    public String getT68()
    {
        return t68;
    }

    public void setT68(String t68)
    {
        this.t68 = t68;
    }

    public String getT69()
    {
        return t69;
    }

    public void setT69(String t69)
    {
        this.t69 = t69;
    }

    public String getT70()
    {
        return t70;
    }

    public void setT70(String t70)
    {
        this.t70 = t70;
    }

    public String getT71()
    {
        return t71;
    }

    public void setT71(String t71)
    {
        this.t71 = t71;
    }

    public String getT72()
    {
        return t72;
    }

    public void setT72(String t72)
    {
        this.t72 = t72;
    }

    public String getT73()
    {
        return t73;
    }

    public void setT73(String t73)
    {
        this.t73 = t73;
    }

    public String getT74()
    {
        return t74;
    }

    public void setT74(String t74)
    {
        this.t74 = t74;
    }

    public String getT75()
    {
        return t75;
    }

    public void setT75(String t75)
    {
        this.t75 = t75;
    }

    public String getT76()
    {
        return t76;
    }

    public void setT76(String t76)
    {
        this.t76 = t76;
    }

    public String getT77()
    {
        return t77;
    }

    public void setT77(String t77)
    {
        this.t77 = t77;
    }

    public String getT78()
    {
        return t78;
    }

    public void setT78(String t78)
    {
        this.t78 = t78;
    }

    public String getT79()
    {
        return t79;
    }

    public void setT79(String t79)
    {
        this.t79 = t79;
    }

    public String getT80()
    {
        return t80;
    }

    public void setT80(String t80)
    {
        this.t80 = t80;
    }

    public String getT81()
    {
        return t81;
    }

    public void setT81(String t81)
    {
        this.t81 = t81;
    }

    public String getT82()
    {
        return t82;
    }

    public void setT82(String t82)
    {
        this.t82 = t82;
    }

    public String getT83()
    {
        return t83;
    }

    public void setT83(String t83)
    {
        this.t83 = t83;
    }

    public String getT84()
    {
        return t84;
    }

    public void setT84(String t84)
    {
        this.t84 = t84;
    }

    public String getT85()
    {
        return t85;
    }

    public void setT85(String t85)
    {
        this.t85 = t85;
    }

    public String getT86()
    {
        return t86;
    }

    public void setT86(String t86)
    {
        this.t86 = t86;
    }

    public String getT87()
    {
        return t87;
    }

    public void setT87(String t87)
    {
        this.t87 = t87;
    }

    public String getT88()
    {
        return t88;
    }

    public void setT88(String t88)
    {
        this.t88 = t88;
    }

    public String getT89()
    {
        return t89;
    }

    public void setT89(String t89)
    {
        this.t89 = t89;
    }

    public String getT90()
    {
        return t90;
    }

    public void setT90(String t90)
    {
        this.t90 = t90;
    }

    public String getT91()
    {
        return t91;
    }

    public void setT91(String t91)
    {
        this.t91 = t91;
    }

    public String getT92()
    {
        return t92;
    }

    public void setT92(String t92)
    {
        this.t92 = t92;
    }

    public String getT93()
    {
        return t93;
    }

    public void setT93(String t93)
    {
        this.t93 = t93;
    }

    public String getT94()
    {
        return t94;
    }

    public void setT94(String t94)
    {
        this.t94 = t94;
    }

    public String getT95()
    {
        return t95;
    }

    public void setT95(String t95)
    {
        this.t95 = t95;
    }

    public String getT96()
    {
        return t96;
    }

    public void setT96(String t96)
    {
        this.t96 = t96;
    }

    public String getT97()
    {
        return t97;
    }

    public void setT97(String t97)
    {
        this.t97 = t97;
    }

    public String getT98()
    {
        return t98;
    }

    public void setT98(String t98)
    {
        this.t98 = t98;
    }

    public String getT99()
    {
        return t99;
    }

    public void setT99(String t99)
    {
        this.t99 = t99;
    }

    public String getT100()
    {
        return t100;
    }

    public void setT100(String t100)
    {
        this.t100 = t100;
    }

    public void setT1(String t1)
    {
        this.t1 = t1;
    }
}
