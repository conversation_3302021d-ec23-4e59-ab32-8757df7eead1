package com.orbis.question2;

import java.util.List;

import com.orbis.importExport.EntityImportExportQuestion;
import com.orbis.web.content.ContentItem;

public abstract class QuestionAbstract extends ContentItem
        implements QuestionInterface, EntityImportExportQuestion
{
    private Question2 question;

    public Question2 getQuestion()
    {
        return question;
    }

    public void setQuestion(Question2 question)
    {
        this.question = question;
    }

    public String getQuestionText()
    {
        return this.question.getQuestionText();
    }

    public String getAnswerMapping()
    {
        return this.question.getAnswerField1();
    }

    public int getIEType()
    {
        int ret = -1;
        if (this.question.getType() == Question2.TYPE_SINGLE_CHOICE)
        {
            ret = EntityImportExportQuestion.IE_TYPE_DROPDOWN;
        }
        else if (this.question.getType() == Question2.TYPE_DATE)
        {
            ret = EntityImportExportQuestion.IE_TYPE_DATE;
        }
        else
        {
            ret = EntityImportExportQuestion.IE_TYPE_TEXT;
        }
        return ret;
    }

    public List<String> getOptionChoices()
    {
        return this.question.getChoiceList();
    }

    public boolean isRequired()
    {
        return this.question.isRequired();
    }
}