package com.orbis.question2;

public class QuestionUserType
{
    private String userTypeLabel;

    /**
     * This is a 'key' that represents this user-type in a web context.
     */
    private String userTypeKey;

    private boolean canRead = true;

    private boolean canWrite = true;

    private boolean canSearch = true;

    public String getUserTypeLabel()
    {
        return userTypeLabel;
    }

    public void setUserTypeLabel(String userTypeLabel)
    {
        this.userTypeLabel = userTypeLabel;
    }

    public boolean isCanRead()
    {
        return canRead;
    }

    public void setCanRead(boolean canRead)
    {
        this.canRead = canRead;
    }

    public boolean isCanWrite()
    {
        return canWrite;
    }

    public void setCanWrite(boolean canWrite)
    {
        this.canWrite = canWrite;
    }

    public boolean isCanSearch()
    {
        return canSearch;
    }

    public void setCanSearch(boolean canSearch)
    {
        this.canSearch = canSearch;
    }

    public String getUserTypeKey()
    {
        return userTypeKey;
    }

    public void setUserTypeKey(String userTypeKey)
    {
        this.userTypeKey = userTypeKey;
    }

    @Override
    public String toString()
    {
        return "QuestionUserType [userTypeLabel=" + userTypeLabel + ", userTypeKey="
                + userTypeKey + ", canRead=" + canRead + ", canWrite=" + canWrite
                + ", canSearch=" + canSearch + "]";
    }
}
