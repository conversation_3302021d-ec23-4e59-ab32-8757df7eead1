package com.orbis.expressions;

import static com.orbis.utils.StringUtils.isNotEmpty;

import java.math.RoundingMode;
import java.text.Collator;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.atteo.evo.inflector.English;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;

import com.google.common.collect.Lists;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFModel;
import com.orbis.df.DFQuestion;
import com.orbis.portal.Breadcrumb;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.Action;
import com.orbis.utils.ActionHelper;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONBuilderUtils;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.JsonValue;
import com.orbis.utils.LambdaExceptionUtil;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmRegistrationController;
import com.orbis.web.content.acrm.AcrmRegistrationModule;
import com.orbis.web.content.dashboard.DashboardController;
import com.orbis.web.content.dashboard.DashboardHelper;
import com.orbis.web.content.dashboard.DashboardModule;
import com.orbis.web.content.doc.DocHelper;
import com.orbis.web.content.ec.ECHelper;
import com.orbis.web.content.grid.GridCategory;
import com.orbis.web.content.grid.GridColumn;
import com.orbis.web.content.grid.GridHelper;
import com.orbis.web.content.interaction.InteractionEntity;
import com.orbis.web.content.interaction.InteractionEntityTemplate;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionMessageTemplate;
import com.orbis.web.content.interaction.InteractionMessageTemplateHelper;
import com.orbis.web.content.interview.InterviewHelper;
import com.orbis.web.content.interview.InterviewModule;
import com.orbis.web.content.lp.LandingPageModule;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.np.NPosting;
import com.orbis.web.content.np.NPostingModule;
import com.orbis.web.content.sa.SAHelper;
import com.orbis.web.content.vc.VCHelper;
import com.orbis.web.site.SiteElement;
import com.orbis.web.site.SiteElementUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.jsp.PageContext;

public class Functions
{
    public static Integer getInteractionMessageNoRecipientUserId()
    {
        return Optional.ofNullable(InteractionHelper.getMessageNoRecipientUser())
                .map(UserDetailsImpl::getId).orElse(null);
    }

    public static boolean isVideoConferencingEnabled()
    {
        return VCHelper.getOpenTokKey() != null
                && StringUtils.isNotEmpty(VCHelper.getOpenTokSecret());
    }

    public static String timeAgo(String localeCode, Date date)
    {
        String ret = "";

        if (date != null)
        {
            LocalDate d1 = LocalDate.of(DateUtils.getYear(date),
                    DateUtils.getMonth(date), DateUtils.getDayOfMonth(date));
            LocalDate now = LocalDate.now();
            Period diff = Period.between(d1, now);
            int yd = diff.getYears();
            if (yd == 1)
            {
                ret = new I18nLabel("i18n.Functions.1year5304511490958514")
                        .getTranslation(localeCode) + " ";
            }
            else if (yd > 1)
            {
                ret = new I18nLabel("i18n.Functions.0years7983813676395473",
                        Integer.toString(yd)).getTranslation(localeCode) + " ";
            }

            int md = diff.getMonths();
            if (md == 1)
            {
                ret += new I18nLabel("i18n.Functions.1month1085639177555497")
                        .getTranslation(localeCode) + " ";
            }
            else if (md > 1)
            {
                ret += new I18nLabel("i18n.Functions.0months2518679361624509",
                        Integer.toString(md)).getTranslation(localeCode) + " ";
            }

            int dd = diff.getDays();
            if (dd == 1)
            {
                ret += new I18nLabel("i18n.Functions.1day2851612110730203")
                        .getTranslation(localeCode);
            }
            else if (dd > 1)
            {
                ret += new I18nLabel("i18n.Functions.0days9595687465725376",
                        Integer.toString(dd)).getTranslation(localeCode);
            }

            if (dd == 0)
            {
                long dm = DateUtils.getDifferenceInMinutes(date, new Date());
                if (dm > 60)
                {
                    long h = dm / 60;
                    long mm = dm % 60;

                    if (h == 1)
                    {
                        ret += new I18nLabel("i18n.Functions.1hour7672047967358131")
                                .getTranslation(localeCode);
                    }
                    else
                    {
                        ret += new I18nLabel(
                                "i18n.Functions.0hours0988227623170898",
                                Long.toString(h)).getTranslation(localeCode);
                    }

                    if (mm == 1)
                    {
                        ret += " " + new I18nLabel(
                                "i18n.Functions.1minute0162567794067067");
                    }
                    else if (mm > 1)
                    {
                        ret += " " + new I18nLabel(
                                "i18n.Functions.0minutes4776923335862928",
                                Long.toString(mm)).getTranslation(localeCode);
                    }
                }
                else if (dm == 60)
                {
                    ret += new I18nLabel("i18n.Functions.1hour7672047967358131")
                            .getTranslation(localeCode);
                }

            }

            if (ret != "")
            {
                /* Adds the "ago" suffix so that it can be localized */
                ret = new I18nLabel("i18n.Functions.0ago3773734327001279", ret)
                        .getTranslation(localeCode);
            }
            else
            {
                ret = new I18nLabel("i18n.Functions.justnow3969605213371957")
                        .getTranslation(localeCode);
            }

        }
        return ret;
    }

    public static boolean canEmployerEditPosting(int status, String internalStatus,
            String modulePreventStatuses, boolean employerCanEdit,
            boolean preventEmployerEdit, boolean statusPreventEmployerEdit)
    {
        boolean ret = true;

        if (preventEmployerEdit && status >= NPosting.STATUS_APPROVED
                && !employerCanEdit)
        {
            ret = false;
        }
        else if (statusPreventEmployerEdit
                && !canEditInternalStatus(internalStatus, modulePreventStatuses))
        {
            ret = false;
        }

        return ret;
    }

    public static boolean contains(Collection list, Object o)
    {
        return list != null && list.contains(o);
    }

    public static int size(Collection col)
    {
        return col == null || col.isEmpty() ? 0 : col.size();
    }

    public static boolean contains(Object[] object, Object o)
    {
        return Arrays.asList(object).contains(o);
    }

    public static boolean containsDate(List list, Date o, boolean sameDayCompare)
    {
        try
        {
            for (Object oo : list)
            {
                if ((sameDayCompare && DateUtils.areOnSameDay((Date) oo, o))
                        || ((Date) oo).compareTo(o) == 0)
                {
                    return true;
                }
            }
        }
        catch (Exception e)
        {
        }

        return false;
    }

    public static Object getProperty(Object bean, String property)
    {
        Object ret = null;

        try
        {
            ret = PropertyUtils.getProperty(bean, property);
        }
        catch (Exception e)
        {

        }

        return ret;
    }

    public static double round(double number, String mode, int maxFraction)
    {
        double ret = 0.0D;

        try
        {
            DecimalFormat df = new DecimalFormat();
            df.setRoundingMode(RoundingMode.valueOf(mode));
            df.setMaximumFractionDigits(maxFraction);
            ret = Double.parseDouble(df.format(number));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    public static Date addMinutes(Date date, Object minutes)
    {
        Integer mins = 0;

        try
        {
            mins = Integer.valueOf(minutes.toString());
        }
        catch (Exception e)
        {
        }

        return DateUtils.addMinute(date, mins);
    }

    public static Date addDays(Date date, Object minutes)
    {
        return Optional.ofNullable(minutes)//
                .map(Object::toString)//
                .map(StringUtils::toInteger)
                .map(days -> DateUtils.addDays(date, days))//
                .orElse(date);
    }

    public static Date getStartDate(Date date)
    {
        return DateUtils.getStartDate(date);
    }

    public static Date getEndDate(Date date)
    {
        return DateUtils.getEndDate(date);
    }

    public static long getDifferenceInDays(Date d1, Date d2)
    {
        return DateUtils.getDifferenceInDays(d1, d2);
    }

    public static long getDifferenceInMinutesDB(String d1, String d2)
    {
        if (!StringUtils.isEmpty(d1) && !StringUtils.isEmpty(d2))
        {
            Date date1 = DateUtils.parseDate(d1.substring(0, d1.indexOf(".")),
                    DateUtils.DF_DATABASE_DATETIME_COMPATIBLE);
            Date date2 = DateUtils.parseDate(d2.substring(0, d2.indexOf(".")),
                    DateUtils.DF_DATABASE_DATETIME_COMPATIBLE);
            return DateUtils.getDifferenceInMinutes(date1, date2);
        }
        else
        {
            return 0;
        }
    }

    public static Date convertLocalDateToDate(LocalDate date)
    {
        return java.sql.Date.valueOf(date);
    }

    public static int dateDiff(Date d1, Date d2)
    {
        Integer mins = 0;

        try
        {
            mins = (int) DateUtils.getDifferenceInMinutes(d1, d2);
        }
        catch (Exception e)
        {
        }

        return mins;
    }

    public static Double min(double a, double b)
    {
        return Math.min(a, b);
    }

    public static Double ceil(double a)
    {
        return (double) Math.ceil(a);
    }

    public static String getL1Value(String l2, Map<String, String> optionChoices)
    {
        return LocaleUtils.getL1Value(l2, optionChoices);
    }

    public static String getL2Value(String l1, Map<String, String> optionChoices)
    {
        return LocaleUtils.getL2Value(l1, optionChoices);
    }

    public static String getDFQuestionMultiChoicesJson(DFAnswerEntity entity,
            DFQuestion question, String orbisLocale)
    {
        String choiceList = null;
        String choices = null;

        try
        {
            if (entity != null)
            {
                choices = (String) PropertyUtils.getProperty(entity,
                        question.getAnswerField1());
                // we need to remove the leading ^ (^ is originally needed for
                // crazy sql reasons)
                choices = choices != null && choices.startsWith("^")
                        ? choices.substring(1)
                        : choices;

                if (choices != null)
                {
                    String[] r = choices.split("\\^");
                    List<String> n = new LinkedList<>();

                    for (String s : r)
                    {
                        n.add(getLValue(s, question.getChoices(), orbisLocale));
                    }

                    if (choices.contains("^^") || choices.equals("^"))
                        n.add("");

                    choiceList = new JSONArray(n).toString();
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return choiceList;
    }

    /**
     * Returns the other value of a multi-choice question
     *
     * @param entity
     * @param question
     * @param orbisLocale
     * @return null if no other value is found, otherwise the other value
     */
    public static String getDFQuestionMultiChoicesOtherVal(DFAnswerEntity entity,
            DFQuestion question, String orbisLocale)
    {
        List<String> selectableChoices = question.getChoiceList();

        String choicesJson = getDFQuestionMultiChoicesJson(entity, question,
                orbisLocale);
        String otherVal = null;

        if (!StringUtils.isEmpty(choicesJson))
        {
            try
            {
                JSONArray choices = new JSONArray(choicesJson);

                // Converting JS logic that sets the other value in the textbox in
                // the bootstrap version of df_questionMultiChoiceEdit.jsp

                for (int i = 0; i < choices.length(); i++)
                {
                    String choice = choices.getString(i);

                    if (!StringUtils.isEmpty(choice)
                            && !selectableChoices.contains(choice))
                    {
                        // Found the other value since it's not in the selectable
                        // choices
                        otherVal = choice;
                        break;
                    }
                }

            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

        }

        return otherVal;
    }

    public static List<String> getBilingualListFromString(String stringList,
            String languageCode)
    {

        return getBilingualListFromStringOrdering(stringList, languageCode, false);
    }

    public static List<String> getBilingualListFromStringOrdering(String stringList,
            String languageCode, boolean preserverOrdering)
    {
        List<String> ret;
        ret = StringUtils.listify(stringList, "\r\n", languageCode);

        if (!preserverOrdering)
        {
            Set<String> retSet = new HashSet<>(ret);
            ret.clear();
            ret.addAll(retSet);
            Locale locale = new Locale(languageCode);
            Collator coll = Collator.getInstance(locale);
            coll.setStrength(Collator.TERTIARY);
            coll.setDecomposition(Collator.FULL_DECOMPOSITION);
            Collections.sort(ret, coll);
        }

        return ret;
    }

    public static List<String> listify(String toList)
    {
        return StringUtils.listify(toList, "\r\n");
    }

    public static Map<String, String> getBilingualMapFromString(String stringList,
            String localeCode)
    {
        List<String> labelList = getBilingualListFromStringOrdering(stringList,
                localeCode, true);
        return StringUtils.mapify(labelList, labelList);
    }

    public static String getLValue(String bilingualValue, String stringList,
            String languageCode)
    {
        return LocaleUtils.getLValue(bilingualValue, stringList, languageCode);
    }

    public static String getLocalizedString(String string, String locale)
    {
        String ret = string;

        try
        {
            String[] tokens = string.split("\\|");
            ret = LocaleUtils.isL1(locale) ? tokens[0] : tokens[1];
            if (StringUtils.isEmpty(ret))
            {
                ret = string;
            }
        }
        catch (Exception e)
        {
        }

        return ret;
    }

    public static String getStatusBilingual(String locale, String status)
    {
        return SAHelper.getStatusBilingual(locale, status);
    }

    public static List listIntersection(List l1, List l2)
    {
        return new ArrayList(org.apache.commons.collections.CollectionUtils
                .intersection(l1, l2));
    }

    public static List distinctList(List list)
    {
        List newlist = list;

        try
        {
            if (list.get(0) instanceof Object[])
            {
                newlist = CollectionUtils.getDistinctListObjectArray(list);
            }
            else
            {
                newlist = CollectionUtils.getDistinctList(list);
            }
        }
        catch (Exception e)
        {
        }

        return newlist;
    }

    /**
     *
     * @param definition
     *            the name of the grid
     * @param request
     * @return
     */
    public static Map<String, GridColumn> gridColumnMapByJspProperty(
            String definition, HttpServletRequest request)
    {
        final UserDetailsImpl uzer = PortalUtils.getUserLoggedIn(request);

        return GridColumn.getGridColumnsByProperty(definition, uzer);

    }

    public static String getRequestParamMapAsString(
            Map<String, String[]> requestMap)
    {
        Map<String, String> s = new HashMap<>();
        for (Entry<String, String[]> entry : requestMap.entrySet())
        {
            for (String str : entry.getValue())
            {
                if (!StringUtils.isEmpty(str))
                {
                    s.put(entry.getKey(), str);
                    break;
                }
            }
        }

        return s.toString();
    }

    public static Map<GridCategory, List<GridColumn>> getGridColumnsMappedByCategoryWithContentId(
            String definition, Integer contentItemId, UserDetailsImpl uzer)
    {
        Map<GridCategory, List<GridColumn>> gridColumnsMappedByCategory = new TreeMap(
                new Comparator<GridCategory>()
                {

                    @Override
                    public int compare(GridCategory o1, GridCategory o2)
                    {
                        return o1.getOrdinal() - o2.getOrdinal();
                    }
                });
        for (Entry<GridCategory, List<GridColumn>> entry : GridHelper
                .getGridColumnsMappedByCategory(definition, uzer, false, true,
                        contentItemId)
                .entrySet())
        {
            Integer contentItemId2 = entry.getKey().getGrid().getContentItemId();
            if (contentItemId != null && contentItemId2 != null
                    && contentItemId.intValue() == contentItemId2.intValue())
            {
                gridColumnsMappedByCategory.put(entry.getKey(), entry.getValue());
            }
        }

        return gridColumnsMappedByCategory;

    }

    public static Map<GridCategory, List<GridColumn>> getGridColumnsMappedByCategory(
            String definition, UserDetailsImpl uzer)
    {
        final boolean globalChange = false;
        final boolean justVisible = true;
        final Map<GridCategory, List<GridColumn>> gridColumnsMappedByCategory = GridHelper
                .getGridColumnsMappedByCategory(definition, null, null, uzer,
                        globalChange, justVisible);

        return gridColumnsMappedByCategory;
    }

    public static Map<Integer, PersonGroup> permissionGroupsMappedById()
    {
        Map<Integer, PersonGroup> pgMap = new LinkedHashMap<>();
        List<PersonGroup> pg = PersonGroupHelper.getSecondaryGroups();
        for (PersonGroup personGroup : pg)
        {
            pgMap.put(personGroup.getId(), personGroup);
        }

        return pgMap;
    }

    public static int getDateYear(Date date)
    {
        return DateUtils.getYear(date);
    }

    public static String truncate(String string, int charLimit,
            boolean appendEllipsis)
    {
        try
        {
            string = Jsoup.parse(string).text();
        }
        catch (Exception e)
        {
        }

        return (string.length() > charLimit
                ? (string.substring(0, charLimit) + (appendEllipsis ? "..." : ""))
                : string);
    }

    public static boolean stringIsNumericValue(String value)
    {
        return NumberUtils.isNumber(value);
    }

    public static String getConfig(String configKey)
    {
        return Optional.ofNullable(PortalConfigHelper.getPortalConfig(configKey))
                .map(PortalConfig::getOrbisValue).orElse("");
    }

    public static String capitalize(String string)
    {
        return org.apache.commons.lang.StringUtils.capitalize(string);
    }

    public static String fixUrl(String url)
    {
        String ret = Optional.ofNullable(url).orElse("");
        if (!ret.toLowerCase().startsWith("http"))
        {
            // Pattern to catch domain names. I remove the .htm to avoid a
            // positive match with a url ending with htm or html
            String urlForTesting = ret.replace(".htm", "");
            Pattern p = Pattern.compile(
                    "(https?:\\/\\/)?((?:[\\w\\d-]+\\.)+([\\w\\d]{2,}))(.*)",
                    Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(urlForTesting);
            if (m.matches())
            {
                ret = "http://" + ret;
            }
            else
            {
                ret = (ret.startsWith("/") ? "" : "/") + ret;
            }
        }
        return ret;
    }

    public static Object parseInt(Object object)
    {
        try
        {
            return Integer.parseInt(object.toString());
        }
        catch (Exception e)
        {
        }
        return object;
    }

    public static String minutesToHumanReadable(int minutes)
    {
        int hours = minutes / 60;
        int mins = minutes % 60;
        return (hours > 0 ? (hours + " hour" + (hours > 1 ? "s " : " ")) : "")
                + (mins + " minute" + (mins == 0 || mins > 1 ? "s " : " "));
    }

    public static String getReadableInteractionType(String type)
    {
        String readableType = "";

        try
        {
            readableType = InteractionHelper.INTERACTION_TYPE.valueOf(type)
                    .getReadableString();
        }
        catch (Exception e)
        {
        }

        return readableType;
    }

    public static Map appStatusMap(NPostingModule jobModule, String locale,
            HttpServletRequest request)
    {
        Locale requestLocale = PortalUtils.getLocale(request);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        return appStatusMap(jobModule, user, requestLocale);
    }

    public static Map appStatusMap(NPostingModule jobModule, UserDetailsImpl user,
            Locale locale)
    {
        Map<Integer, String> map = new LinkedHashMap();

        try
        {
            map = NHelper.getApplicationStatusForDisplayMap(jobModule,
                    LocaleUtils.isL1(locale) ? LocaleUtils.getDefaultLocale()
                            : LocaleUtils.getSecondaryLocale(),
                    user, null);
            Set<Integer> statuses = new LinkedHashSet(map.keySet());
            for (Integer s : statuses)
            {
                if (user.isEmployer() && ((Boolean) PropertyUtils
                        .getProperty(jobModule, "s" + s + "a")))
                {
                    map.remove(s);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return map;
    }

    /**
     * This will check to see if the given site element has the spiral robot
     * template around it. This is a recursive method.
     *
     * @param siteElement
     * @return
     */
    public static boolean isSpiralRobotCompatible(SiteElement siteElement,
            PageContext pageContext)
    {
        boolean ret = false;

        if (pageContext != null
                && pageContext.getAttribute(PortalUtils.FORCE_SPIRAL_ROBOT,
                        PageContext.REQUEST_SCOPE) != null)
        {
            ret = (Boolean) pageContext.getAttribute(PortalUtils.FORCE_SPIRAL_ROBOT,
                    PageContext.REQUEST_SCOPE);
        }
        else if (siteElement != null)
        {
            if (PortalUtils.SPIRAL_ROBOT_TEMPLATE.equals(siteElement.getUrl()))
            {
                ret = true;
            }
            else if (siteElement.getTemplate() != null)
            {
                ret = PortalUtils.SPIRAL_ROBOT_TEMPLATE
                        .equals(siteElement.getTemplate().getUrl())
                        || pageContext != null
                                && PortalUtils.MY_ACCOUNT_TEMPLATE
                                        .equals(siteElement.getTemplate().getUrl())
                                && PortalUtils.MENU_VIEW_CLASS.equals(
                                        pageContext.getPage().getClass().getName());
            }
            else if (siteElement.getContentItemClass() != null)
            {
                ret = siteElement.getContentItemClass()
                        .equals(LandingPageModule.class);
            }

            if (!ret && siteElement.getParent() != null)
            {
                ret = isSpiralRobotCompatible(siteElement.getParent(), pageContext);
            }
        }

        return ret;
    }

    /**
     * This will check to see if the given site element has the spiral robot
     * template around it. This is a recursive method.
     *
     * @param siteElement
     * @return
     */
    public static boolean isUnderMyAccountTemplate(SiteElement siteElement)
    {
        boolean ret = false;

        if (siteElement != null)
        {
            if (siteElement.getTemplate() != null && PortalUtils.MY_ACCOUNT_TEMPLATE
                    .equals(siteElement.getTemplate().getUrl()))
            {
                ret = true;
            }
            else if (siteElement.getParent() != null)
            {
                ret = isUnderMyAccountTemplate(siteElement.getParent());
            }
        }

        return ret;
    }

    /**
     * Gets the first online landing page module. If there is none, it will return a
     * landing page module with a default config. This is to make sure we don't end
     * up on public pages without a theme.
     *
     * @return first online landing page module within the site element cache
     */
    public static LandingPageModule getLandingPageModule()
    {
        return Optional
                .ofNullable((LandingPageModule) SiteElementUtils
                        .getFirstOnlineModule("landingPageController"))
                .orElse(new LandingPageModule());
    }

    public static List<Breadcrumb> getBreadcrumbs(HttpServletRequest request,
            String title)
    {
        LinkedList<Breadcrumb> breadcrumbs = (LinkedList<Breadcrumb>) Optional
                .ofNullable((LinkedList<Breadcrumb>) request.getSession()
                        .getAttribute(PortalUtils.BREADCRUMBS_KEY))
                .orElse(new LinkedList<Breadcrumb>()).clone();
        PortalUtils.addBreadcrumb(HtmlUtils.stripHtml(title), request);
        return breadcrumbs;
    }

    public static Integer getDashboardNavType(PageContext pageContext)
    {
        return (Integer) pageContext.getAttribute(
                DashboardController.MY_DASHBOARD_NAV, PageContext.SESSION_SCOPE);
    }

    public static boolean hasExperientialCatalogue()
    {
        return ECHelper.getModule().isPresent();
    }

    public static Map<String, List<OrbisHqlResultSet>> getFooterLinkmap()
    {
        LandingPageModule module = getLandingPageModule();
        List<OrbisHqlResultSet> links = PortalUtils.getHt().f(
                "select l.linkbox.title, l.linkTitle, l.link from LandingPageLinkboxLink l where l.linkbox.module = ? order by l.linkbox.seq, l.seq",
                module);
        Map<String, List<OrbisHqlResultSet>> ret = new HashMap<>();
        if (links != null)
        {
            ret = links.stream().collect(
                    Collectors.groupingBy(m -> (String) m.select("linkbox.title")));
        }

        return ret;
    }

    public static Map<String, List<OrbisHqlResultSet>> getFooterLinkmapLocalized(
            String locale)
    {
        LandingPageModule module = getLandingPageModule();
        String linkboxTitleField = LocaleUtils.isL1(locale) ? "linkbox.title"
                : "linkbox.l2Title";
        String linkTitleField = LocaleUtils.isL1(locale) ? "linkTitle"
                : "l2LinkTitle";
        List<OrbisHqlResultSet> links = PortalUtils.getHt().f("select l."
                + linkboxTitleField + ", l." + linkTitleField
                + ", l.link from LandingPageLinkboxLink l where l.linkbox.module = ? order by l.linkbox.seq, l.seq",
                module);
        Map<String, List<OrbisHqlResultSet>> ret = new HashMap<>();
        if (links != null)
        {
            ret = links.stream().collect(Collectors
                    .groupingBy(m -> (String) m.select(linkboxTitleField)));
        }

        return ret;
    }

    public static String random()
    {
        return StringUtils.getRandomString();
    }

    public static Date now()
    {
        return DateUtils.now();
    }

    public static String formatDate(Date date, String pattern)
    {
        String string = "";

        try
        {
            string = DateUtils.formatDate(date, pattern, null);
        }
        catch (Exception e)
        {
        }

        return string;
    }

    public static String getFullName(String salutation, String firstName,
            String lastName)
    {
        return AcrmHelper.assembleFullName(firstName, lastName);
    }

    public static boolean canAccessSiteElement(UserDetailsImpl user, SiteElement se)
    {
        return PortalUtils.canAccessSiteElement(se, user, new HashSet());
    }

    public static boolean isAzureSite()
    {
        return PortalUtils.isAzureSite();
    }

    public static long getMilliseconds(Date date)
    {
        long time = 0;
        try
        {
            time = date.getTime();
        }
        catch (Exception e)
        {
        }
        return time;
    }

    public static String getInterviewMethodLabel(int interviewMethodTypeCode,
            InterviewModule interviewModule, String locale)
    {
        return InterviewHelper.getInterviewSchedule_interviewMethodLabel(
                interviewMethodTypeCode, interviewModule, new Locale(locale));
    }

    public static String getInterviewTypeLabel(int typeCode, InterviewModule module,
            String orbisLocale)
    {
        return InterviewHelper.getInterviewSchedule_interviewTypeLabel(typeCode,
                module, new Locale(orbisLocale));
    }

    public static Object removeMapEntry(Map map, Object key)
    {
        return map.remove(key);
    }

    public static boolean isServiceTeamEngaged()
    {
        return InteractionHelper.isServiceTeamEngaged();
    }

    public static String padString(Object string, String paddingChar, int length)
    {
        return StringUtils.padString(string.toString(), paddingChar, length);
    }

    public static String concat(String s1, String s2)
    {
        return s1 + s2;
    }

    public static boolean isValidDocPdf(Object pdfUUID, int docHandlerId)
    {
        return DocHelper.isValidPdfFile(pdfUUID, docHandlerId);
    }

    public static String join(List list, String glue)
    {
        return StringUtils.join(list, glue);
    }

    public static Locale getLocale(final PageContext pageContext)
    {
        return PortalUtils.getLocale((HttpServletRequest) pageContext.getRequest());
    }

    public static String escapeSingleQuote(String str)
    {
        return str != null ? str.replace("'", "\\'") : null;
    }

    public static String getBodyClass(SiteElement se)
    {
        String ret = "";
        try
        {
            ret = se.getType().replaceAll("Controller", "");
        }
        catch (Exception e)
        {
        }
        return ret;
    }

    public static String convertJobPostingOptionsToDataviewerOptions(
            List<String> availableChoices, String locale)
    {
        List<JsonValue> jsonParams = new ArrayList<>();

        for (String choice : availableChoices)
        {
            String[] ocs = choice.split("\\|");
            JsonValue val = new JsonValue(
                    LocaleUtils.isL1(locale) ? ocs[0] : ocs[1]);
            jsonParams.add(val);
        }

        return JSONBuilderUtils.createArrayJSONString(jsonParams);
    }

    public static Map<String, String> jsonToMap(String str)
    {
        return JSONUtils.toJSONObject(str).map(jo -> {
            Map<String, String> ret;
            try
            {
                Iterable<String> it = () -> jo.keys();
                ret = StreamSupport.stream(it.spliterator(), false)
                        // toMap OK -- optString always returns non-null
                        .collect(Collectors.toMap(Function.identity(),
                                jo::optString));
            }
            catch (Exception e)
            {
                ret = Collections.emptyMap();
            }

            return ret;
        }).orElse(Collections.emptyMap());
    }

    /**
     * Converts a json string to hidden form input in jsps
     *
     * @param jsonString
     * @return
     * @throws JSONException
     */
    public static String jsonStringToFormInputs(String jsonString)
            throws JSONException
    {
        try
        {
            JSONObject jsonObject = new JSONObject(jsonString);
            return Arrays.stream(JSONObject.getNames(jsonObject))
                    .map(LambdaExceptionUtil
                            .rethrowFunction(n -> "<input type=\"hidden\" name=\""
                                    + n + "\" value=\""
                                    + jsonObject.get(n).toString() + "\" />"))
                    .collect(Collectors.joining());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return "";
    }

    /**
     * Returns a {@link Set} containing the keys of the specified map
     *
     * @param map
     * @return
     */
    public static Set keySet(Map map)
    {
        return map.keySet();
    }

    /**
     * Returns a {@link List} containing the values of the specified map
     *
     * @param map
     * @return
     */
    public static List values(Map map)
    {
        return new ArrayList(map.values());
    }

    /**
     *
     * @param list
     * @param size
     * @return
     */
    public static List partition(List list, int size)
    {
        return Lists.partition(list, size);
    }

    public static LinkedHashSet<Object> truncateSet(Set<Object> set, int size)
    {
        LinkedHashSet<Object> ret = null;

        if (set != null)
        {
            ret = new LinkedHashSet<Object>();

            for (Object o : set)
            {
                if (ret.size() >= size)
                    break;

                ret.add(o);
            }
        }

        return ret;

    }

    public static List<Object> truncateList(List<Object> list, int size)
    {
        LinkedList<Object> ret = null;

        if (list != null)
        {
            ret = new LinkedList<Object>();

            for (Object o : list)
            {
                if (ret.size() >= size)
                    break;

                ret.add(o);
            }
        }
        return ret;
    }

    public static boolean isUserProfileImageEnabled(PersonGroup primaryGroup)
    {
        AcrmRegistrationModule module = AcrmRegistrationController
                .getRegistrationModule(primaryGroup);

        return module != null && module
                .getProfileImageSupport() != AcrmRegistrationModule.PROFILE_IMAGE_DISABLED;
    }

    public static Boolean equals(String s1, String s2)
    {
        return s1 == null && s2 == null
                || s1 != null && s2 != null && StringEscapeUtils.unescapeHtml(s1)
                        .equals(StringEscapeUtils.unescapeHtml(s2));
    }

    /**
     * Returns true if the user has {@link InteractionEntityTemplate}s available to
     * them to use
     *
     * @param interactionClass
     *            Shorthand name for the {@link InteractionEntity} type: <br>
     *            Use "messages" if you want to check for
     *            {@link InteractionMessageTemplate}s
     * @param interactionType
     *            The INTERACTION_TYPE
     * @param userLoggedIn
     *            The user logged in
     * @param messageTo
     *            Used by InteractionMessage
     * @return true if the user has {@link InteractionEntityTemplate}s available to
     *         them to use
     */
    public static boolean hasInteractionTemplates(String interactionClass,
            String interactionType, UserDetailsImpl userLoggedIn, String messageTo)
    {
        boolean hasTemplates;

        if ("messages".equals(interactionClass))
        {
            hasTemplates = (Integer) PortalUtils.getHt()
                    .find("SELECT COUNT(t.id) " + InteractionMessageTemplateHelper
                            .getFromWhereOwnerClause(messageTo, interactionType,
                                    userLoggedIn))
                    .get(0) > 0;
        }
        else
        {
            hasTemplates = false;
        }

        return hasTemplates;
    }

    public static String encrypt(String action, UserDetailsImpl currentUser)
    {
        final Action a = Action.newInstance(currentUser, action, null);
        return ActionHelper.encryptAction(a);
    }

    public static String encrypt(String action, String subAction,
            UserDetailsImpl currentUser)
    {
        final Action a = Action.newInstance(currentUser, action, subAction);
        return ActionHelper.encryptAction(a);
    }

    public static boolean modelHasQuestions(DFModel model)
    {
        return (Integer) PortalUtils.getHt()
                .find("select count(q) from DFQuestion q where q.category.model=?",
                        model)
                .get(0) > 0;
    }

    public static String getFullBaseUrl(SiteElement siteElement)
    {
        return PortalUtils.getFullBaseUrl(siteElement);
    }

    public static <T> T findAnyWithMatchingField(List<T> l, String property,
            Object equalTo)
    {
        T matchedElement = null;

        if (l != null)
        {
            matchedElement = l.stream().filter(o -> {
                try
                {
                    Object p = PropertyUtils.getProperty(o, property);
                    return Objects.equals(p, equalTo);
                }
                catch (Exception e)
                {
                }

                return false;
            }).findAny().orElse(null);
        }

        return matchedElement;
    }

    public static String cleanUserForDisplay(UserDetailsImpl user, String locale)
    {
        String ret = "";
        if (user != null)
        {
            if (user.isDeleted())
            {
                return removeDeleted(user.getUsername(), locale);
            }
            else if (locale != null)
            {
                Locale l = new Locale(locale);
                ret = PortalUtils.getI18nMessage(
                        "i18n.c_achievedCompetencies.0Login12304199189468548", l,
                        user.getFullName(), user.getUsername());
            }
        }
        return ret;
    }

    /**
     *
     * @return
     */
    public static String removeDeleted(String test, String locale)
    {
        String ret = test;
        if (isNotEmpty(ret) && ret.startsWith("DELETED"))
        {
            String ret1;
            List users = PortalUtils.getHt().find(
                    "select CONCAT(a.preferredFirstName, ' ', a.lastName) from UserDetailsImpl a where a.username=?",
                    ret);

            if (users.size() == 1)
            {
                ret1 = (String) users.get(0);
                ret = PortalUtils.getI18nMessage(
                        "i18n.Functions.UserNoLong5844554436280343",
                        new Locale(locale), ret1);
            }
            else
            {
                ret1 = ret.replaceAll("DELETED", "").replaceAll("0\\.\\d+$", "");
                ret = PortalUtils.getI18nMessage(
                        "i18n.Functions.OrgNoLong5844554436280343",
                        new Locale(locale), ret1);
            }

        }
        return ret;
    }

    public static String generateRandomId()
    {
        return Integer.toString(RandomUtils.nextInt());
    }

    public static String pluralize(String string, int count)
    {
        return English.plural(string, count);
    }

    public static boolean isInstance(Object object, String className)
    {
        boolean isInstance = false;
        try
        {
            Class cls = ClassUtils.loadClass(className);
            isInstance = cls.isInstance(object);
        }
        catch (ClassNotFoundException e)
        {
            e.printStackTrace();
        }
        return isInstance;
    }

    public static String localize(String l1, String l2, String locale)
    {
        return StringUtils.getStringForLocaleDefaultL1(l1, l2, locale);
    }

    public static String formatNumber(Number number, String locale)
    {
        return NumberFormat.getInstance(Locale.forLanguageTag(locale))
                .format(number);
    }

    private static boolean canEditInternalStatus(String internalStatus,
            String modulePreventStatuses)
    {
        List<String> modulePreventStatusesList = Arrays.asList(
                modulePreventStatuses != null ? modulePreventStatuses.split("\r\n")
                        : new String[] {});
        return modulePreventStatusesList.stream()
                .noneMatch(s -> (internalStatus.equals(s)
                        || (internalStatus + "|" + internalStatus).equals(s)));
    }

    public static String encodeBase64(String str)
    {
        return Base64.getUrlEncoder().withoutPadding()
                .encodeToString(str.getBytes());
    }

    public static boolean isAllEmpty(Object str1, Object str2)
    {
        return (str1 == null
                || str1 instanceof String && StringUtils.isEmpty((String) str1))
                && (str2 == null || str2 instanceof String
                        && StringUtils.isEmpty((String) str2));
    }

    public static List<Map<String, Object>> getHeaderTabs(
            UserDetailsImpl currentUser, HttpServletRequest request)
    {
        OrbisController.populateDashboardTabs(request);
        SiteElement dashboardSe = DashboardHelper
                .getDashboardSiteElement(currentUser);
        return DashboardHelper.getCurrentUserDashboardHeader(currentUser,
                (DashboardModule) dashboardSe.getContentItem());
    }
}
