package com.orbis.pdfCleaner;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.pdfbox.cos.COSArray;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.cos.COSNumber;
import org.apache.pdfbox.cos.COSStream;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDDocumentNameDictionary;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.action.PDDocumentCatalogAdditionalActions;
import org.apache.pdfbox.pdmodel.interactive.action.PDPageAdditionalActions;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationLink;

/**
 * Class for removing unwanted features from PDFs
 *
 * <AUTHOR>
 */
public class PDFCleaner
{

    /**
     * Removes any of the following content if it was found to exist in the
     * provided scan result
     * <ul>
     * <li>Document and Page level actions
     * <li>Viewer Preferences
     * <li>Document Outline
     * <li>Page Labels
     * <li>Digital Signatures
     * <li>Document Requirements
     * <li>Named JavaScript
     * <li>Named Destinations
     * <li>Annotations (other than links with URI actions)
     * <li>Thumbnails
     * <li>Page Durations
     * </ul>
     *
     * @param doc
     *            the file to be cleaned
     * @param result
     *            a ScanResult object to base current cleaning on. Only content
     *            found in the scan will be cleaned.
     * @return Returns a list of any errors encountered during the scan
     */
    public static ArrayList cleanFile(File doc, ScanResult result)
    {
        PDDocument document = null;
        ArrayList CleaningErrors = new ArrayList();
        Boolean loadError = false;

        // load file
        try
        {
            document = PDDocument.load(new FileInputStream(doc));
        }
        catch (Exception ex)
        {
            System.out
                    .println("ERROR: unable to load document for cleaning: " + ex);
            PDFCleaner.addError(CleaningErrors, "Load Error (clean)", ex);
            loadError = true;
        }

        if (!loadError && document != null)
        {
            try
            {
                if (result.isEncrypted() || result.containsForm()
                        || result.containsWebCapture()
                        || result.containsOptionalContent()
                        || result.containsArticles())
                {
                    PDFCleaner.addError(CleaningErrors,

                            "i18n.com.orbis.pdfCleaner.PDFCleaner.error",
                            new Exception());
                }

                // only attempt clean if no hard error is hit
                if (CleaningErrors.isEmpty())
                {
                    attemptClean(doc, result, document, CleaningErrors);
                }
            }
            catch (Exception ex)
            {
                System.out.println(
                        "ERROR: error while cleaning file: " + ex.toString());
                PDFCleaner.addError(CleaningErrors, "Clean Error", ex);
            }
        }

        try
        {
            if (document != null)
            {
                document.close();
            }
        }
        catch (Exception e)
        {
        }

        return CleaningErrors;
    }

    private static void attemptClean(File doc, ScanResult result,
            PDDocument document, ArrayList CleaningErrors)
    {
        // document level scan
        if (result.containsSignature())
        {
            removeSignatures(document, CleaningErrors);
        }

        // catalog level scan
        PDDocumentCatalog catalog = document.getDocumentCatalog();
        if (result.containsViewerPreferences())
        {
            removeViewerPreferences(catalog, CleaningErrors);
        }
        if (result.containsPageLabels())
        {
            removePageLabels(catalog, CleaningErrors);
        }
        if (result.containsDocReq())
        {
            removeRequirements(catalog, CleaningErrors);
        }
        if (result.containsActions())
        {
            removeDocumentActions(catalog, CleaningErrors);
        }
        if (result.containsDocOutline())
        {
            removeDocOutline(catalog, CleaningErrors);
        }

        if (document.getDocumentCatalog() != null)
        {
            PDDocumentNameDictionary names = document.getDocumentCatalog()
                    .getNames();
            if (result.containsFiles())
            {
                PDFCleaner.addError(CleaningErrors,

                        "i18n.com.orbis.pdfCleaner.PDFCleaner.error",
                        new Exception());
            }
            if (result.containsScript())
            {
                removeJavascript(names, CleaningErrors);
            }
            if (result.containsDests())
            {
                removeDestinations(names, CleaningErrors);
            }
        }

        // page level scan
        if (result.containsPgActions() || result.containsAnnotations()
                || result.containsThumbnails() || result.containsPageDuration()
                || result.containsArticles())
        {
            List pages = document.getDocumentCatalog().getAllPages();
            for (int i = 0; i < pages.size(); i++)
            {
                PDPage currentPage = (PDPage) pages.get(i);
                COSDictionary pgDictionary = currentPage.getCOSDictionary();

                if (result.containsPgActions())
                {
                    removePageActions(currentPage, CleaningErrors);
                }
                if (result.containsAnnotations())
                {
                    removePageAnnotations(currentPage, CleaningErrors);
                }
                if (result.containsArticles())
                {
                    removeArticleThreadBeads(currentPage, CleaningErrors);
                }
                if (result.containsThumbnails())
                {
                    removeThumbnails(pgDictionary, CleaningErrors);
                }
                if (result.containsPageDuration())
                {
                    removePageDuration(pgDictionary, CleaningErrors);
                }
            }
        }

        try
        {
            document.save(doc);
        }
        catch (Exception ex)
        {
            // unable to save changes
            System.out.println("ERROR: unable to save document: " + ex.toString());
            PDFCleaner.addError(CleaningErrors, "Save Error", ex);
        }

        try
        {
            document.close();
        }
        catch (Exception ex)
        {
            // unable to close document
            System.out.println("ERROR: unable to close document: " + ex.toString());
            PDFCleaner.addError(CleaningErrors, "Close Error (clean)", ex);
        }
    }

    public static ArrayList cleanFile(String doc, ScanResult result)
    {
        return cleanFile(new File(doc), result);
    }

    /**
     * Removes page-level actions from the page
     *
     * @param page
     *            the page to remove annotations from
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removePageActions(PDPage page, List errorList)
    {
        try
        {
            PDPageAdditionalActions additionalActions = page.getActions();
            if ((additionalActions.getO() != null))
            {
                additionalActions.setO(null);
            }
            if (additionalActions.getC() != null)
            {
                additionalActions.setC(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove page actions: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (page actions)", ex);
        }
    }

    /**
     * Removes all annotations except for Link Annotations with a URI action
     * from the page.
     *
     * @param page
     *            the page to remove annotations from
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removePageAnnotations(PDPage page, List errorList)
    {
        try
        {
            List annotations = page.getAnnotations();
            for (int i = 0; i < annotations.size(); i++)
            {
                Object oCurrentAnnotation = annotations.get(i);
                PDAnnotation currentAnnotation = (PDAnnotation) oCurrentAnnotation;
                if (currentAnnotation.getSubtype().equals("Link"))
                {
                    PDAnnotationLink link = (PDAnnotationLink) oCurrentAnnotation;
                    if (link.getAction() != null)
                    {
                        // check that the action type is a URI action
                        if ("URI".equals(link.getAction().getType()))
                        {
                            // URI action on link is permitted
                        }
                        else
                        {
                            annotations.remove(oCurrentAnnotation);
                            i -= 1;
                        }
                    }
                }
                else
                {
                    annotations.remove(oCurrentAnnotation);
                    i -= 1;
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove annotations: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (annotation)", ex);
        }
    }

    /**
     * Removes viewer preference and page mode specifications from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeViewerPreferences(PDDocumentCatalog catalog,
            List errorList)
    {
        try
        {
            // check page mode
            if (!catalog.getPageMode().equals("UseNone"))
            {
                catalog.setPageMode("UseNone");
            }
            // check viewer preferences
            if (catalog.getViewerPreferences() != null)
            {
                catalog.setViewerPreferences(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove viewer preferences: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (viewer pref)", ex);
        }
    }

    /**
     * Removes page labels from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removePageLabels(PDDocumentCatalog catalog, List errorList)
    {
        try
        {
            if (catalog.getPageLabels() != null)
            {
                catalog.setPageLabels(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove page labels: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (page labels)", ex);
        }
    }

    /**
     * Removes the thumbnail image entry from the page's dictionary.
     *
     * @param pgDictionary
     *            the page dictionary
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeThumbnails(COSDictionary pgDictionary, List errorList)
    {
        COSStream Thumbs = null;
        try
        {
            Thumbs = (COSStream) pgDictionary.getDictionaryObject("Thumb");
            if (Thumbs != null)
            {
                pgDictionary.removeItem(COSName.getPDFName("Thumb"));
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove thumbnails: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (thumbnails)", ex);
        }
        finally
        {
            if (Thumbs != null)
            {
                Thumbs.close();
            }
        }
    }

    /**
     * Removes the duration entry from the page's dictionary.
     *
     * @param pgDictionary
     *            the page dictionary
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removePageDuration(COSDictionary pgDictionary,
            List errorList)
    {
        try
        {
            COSNumber pgDuration = (COSNumber) pgDictionary
                    .getDictionaryObject("Dur");
            if (pgDuration != null)
            {
                pgDictionary.removeItem(COSName.getPDFName("Dur"));
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove page duration: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (pageDuration)", ex);
        }
    }

    /**
     * Removes document-level actions from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeDocumentActions(PDDocumentCatalog catalog,
            List errorList)
    {
        try
        {
            PDDocumentCatalogAdditionalActions actions = catalog.getActions();
            // actions performed after printing
            if (actions.getDP() != null)
            {
                actions.setDP(null);
            }

            // actions performed after saving
            if (actions.getDS() != null)
            {
                actions.setDS(null);
            }

            // actions performed before closing
            if (actions.getWC() != null)
            {
                actions.setWC(null);
            }

            // actions performed before printing
            if (actions.getWP() != null)
            {
                actions.setWP(null);
            }

            // actions performed before saving
            if (actions.getWS() != null)
            {
                actions.setWS(null);
            }

            // actions performed on opening
            if (catalog.getOpenAction() != null)
            {
                catalog.setOpenAction(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove document actions: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (document actions)", ex);
        }
    }

    /**
     * Removes document requirements from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeRequirements(PDDocumentCatalog catalog,
            List errorList)
    {
        try
        {
            COSDictionary catalogDictionary = catalog.getCOSDictionary();
            COSArray reqDictionaries = (COSArray) catalogDictionary
                    .getDictionaryObject("Requirements");
            if (reqDictionaries != null)
            {
                catalogDictionary.removeItem(COSName.getPDFName("Requirements"));
            }
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to remove document requirements: "
                    + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (requirements)", ex);
        }
    }

    /**
     * Removes digital signatures from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeSignatures(PDDocument doc, List errorList)
    {
        try
        {
            COSDictionary trailer = doc.getDocument().getTrailer();
            COSDictionary root = (COSDictionary) trailer
                    .getDictionaryObject(COSName.ROOT);
            COSDictionary acroForm = (COSDictionary) root
                    .getDictionaryObject(COSName.ACRO_FORM);
            if (acroForm != null)
            {
                COSArray fields = (COSArray) acroForm
                        .getDictionaryObject(COSName.FIELDS);
                if (fields != null)
                {
                    for (int i = 0; i < fields.size(); i++)
                    {
                        COSDictionary field = (COSDictionary) fields.getObject(i);
                        String type = field.getNameAsString("FT");
                        if ("Sig".equals(type))
                        {
                            fields.removeObject(field);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove signatures: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (signatures)", ex);
        }
    }

    /**
     * Removes the document outline from the document.
     *
     * @param catalog
     *            the document catalog
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeDocOutline(PDDocumentCatalog catalog, List errorList)
    {
        try
        {
            if (catalog.getDocumentOutline() != null)
            {
                catalog.setDocumentOutline(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to remove outline: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (outline)", ex);
        }
    }

    /**
     * Removes any named javascript from the document.
     *
     * @param names
     *            the document's name dictionary
     * @param errorList
     *            the list of errors encountered while cleaning
     */
    private static void removeJavascript(PDDocumentNameDictionary names,
            List errorList)
    {
        try
        {
            if (names != null)
            {
                if (names.getJavaScript() != null)
                {
                    names.setJavascript(null);
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove named javascript: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (javascript)", ex);
        }
    }

    /**
     * Removes article thread beads from an individual page.
     *
     * @param currentPage
     *            a single PDPage object
     * @param errorList
     *            a list to store any errors encountered
     */
    private static void removeArticleThreadBeads(PDPage currentPage, List errorList)
    {
        try
        {
            if (!currentPage.getThreadBeads().isEmpty())
            {
                currentPage.setThreadBeads(null);
            }
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to remove article thread beads: "
                    + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (article thread beads)",
                    ex);
        }
    }

    /**
     * Adds error to the error list
     *
     * @param currentPage
     *            a single PDPage object
     * @param errorList
     *            a list to store any errors encountered
     */
    private static void addError(List errorList, String errorType, Exception ex)
    {
        ArrayList currentError = new ArrayList();

        currentError.add(errorType);
        currentError.add(ex.getMessage());

        String stacktrace = "";
        if (ex.getStackTrace().length > 0)
        {
            for (int i = 0; i < ex.getStackTrace().length; i++)
            {
                stacktrace = stacktrace + ex.getStackTrace()[i] + "\n";
            }
        }
        currentError.add(stacktrace);

        errorList.add(currentError);
    }

    /**
     * Scans the document names dictionary for named destinations.
     *
     * @param names
     *            the document catalog's name dictionary
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document contains named
     *         destinations, or if an error was encountered during the scan;
     *         returns <code>false</code> otherwise.
     */
    private static boolean removeDestinations(PDDocumentNameDictionary names,
            List errorList)
    {
        try
        {
            if (names != null)
            {
                if (names.getDests() != null)
                {
                    names.setDests(null);
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to remove named desinations: " + ex.toString());
            PDFCleaner.addError(errorList, "Clean Error (destinations)", ex);
            return false;
        }
    }

    /*
     * these methods are not used - this content is being rejected instead
     * 
     * private static void removeOptionalContent (PDDocumentCatalog catalog,
     * List errorList) { try { PDOptionalContentProperties OCproperties =
     * catalog.getOCProperties();
     * 
     * if (OCproperties != null) { catalog.setOCProperties(null); } } catch
     * (Exception ex) {
     * System.out.println("ERROR: unable to clean for optional content: " +
     * ex.toString()); //result.addError("Scan Error (optional content)", ex);
     * //return true; } }
     * 
     * private static void removeAcroForm (PDDocumentCatalog catalog, List
     * errorList) { try { if (catalog.getAcroForm() != null) { //PDAcroForm form
     * = catalog.getAcroForm(); catalog.setAcroForm(null); } } catch (Exception
     * ex) { System.out.println("ERROR: unable to remove form: " +
     * ex.toString()); PDFCleaner.addError(errorList, "Clean Error (forms)",
     * ex); } }
     */

}
