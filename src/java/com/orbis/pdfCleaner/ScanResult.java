package com.orbis.pdfCleaner;

//import java.io.File;  /* for testing */
//import java.io.FileWriter; /* for testing */
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ScanResult
{

    private List<String> objectTypes;

    private boolean loadError;

    private boolean isEncrypted;

    private boolean containsFileIDs;

    private boolean containsDocRequirements;

    private boolean containsSignature;

    private boolean containsOptionalContent;

    private boolean containsWebCapture;

    private boolean containsPgDuration;

    private boolean containsThumbnails;

    private boolean containsArticles;

    private boolean containsForm;

    private boolean containsPgLabels;

    private boolean containsAnnotations;

    private boolean containsPgActions;

    private boolean containsActions;

    private boolean containsViewerPreferences;

    private boolean containsDocOutline;

    private boolean containsFiles;

    private boolean containsScript;

    private boolean containsDestinations;

    private ArrayList errorList = new ArrayList();

    public boolean loadError()
    {
        return loadError;
    }

    public void setLoadError(boolean loadError)
    {
        this.loadError = loadError;
    }

    public boolean isEncrypted()
    {
        return isEncrypted;
    }

    public void setIsEncrypted(boolean isEncrypted)
    {
        this.isEncrypted = isEncrypted;
    }

    public boolean containsFileIdentifiers()
    {
        return containsFileIDs;
    }

    public void setContainsFileIdentifiers(boolean containsFileIDs)
    {
        this.containsFileIDs = containsFileIDs;
    }

    public boolean containsDocReq()
    {
        return containsDocRequirements;
    }

    public void setContainsDocReq(boolean containsDocRequirements)
    {
        this.containsDocRequirements = containsDocRequirements;
    }

    public boolean containsSignature()
    {
        return containsSignature;
    }

    public void setContainsSignature(boolean containsSignature)
    {
        this.containsSignature = containsSignature;
    }

    public boolean containsOptionalContent()
    {
        return containsOptionalContent;
    }

    public void setContainsOptionalContent(boolean containsOptionalContent)
    {
        this.containsOptionalContent = containsOptionalContent;
    }

    public boolean containsWebCapture()
    {
        return containsWebCapture;
    }

    public void setContainsWebCapture(boolean containsWebCapture)
    {
        this.containsWebCapture = containsWebCapture;
    }

    public boolean containsPageDuration()
    {
        return containsPgDuration;
    }

    public void setContainsPageDuration(boolean containsPgDuration)
    {
        this.containsPgDuration = containsPgDuration;
    }

    public boolean containsThumbnails()
    {
        return containsThumbnails;
    }

    public void setContainsThumbnails(boolean containsThumbnails)
    {
        this.containsThumbnails = containsThumbnails;
    }

    public boolean containsArticles()
    {
        return containsArticles;
    }

    public void setContainsArticles(boolean containsArticles)
    {
        this.containsArticles = containsArticles;
    }

    public boolean containsForm()
    {
        return containsForm;
    }

    public void setContainsForm(boolean containsForm)
    {
        this.containsForm = containsForm;
    }

    public boolean containsPageLabels()
    {
        return containsPgLabels;
    }

    public void setContainsPageLabels(boolean containsPgLabels)
    {
        this.containsPgLabels = containsPgLabels;
    }

    public boolean containsAnnotations()
    {
        return containsAnnotations;
    }

    public void setContainsAnnotations(boolean containsAnnotations)
    {
        this.containsAnnotations = containsAnnotations;
    }

    public boolean containsPgActions()
    {
        return containsPgActions;
    }

    public void setContainsPgActions(boolean containsPgActions)
    {
        this.containsPgActions = containsPgActions;
    }

    public boolean containsActions()
    {
        return containsActions;
    }

    public void setContainsActions(boolean containsActions)
    {
        this.containsActions = containsActions;
    }

    public boolean containsViewerPreferences()
    {
        return containsViewerPreferences;
    }

    public void setContainsViewerPreferences(boolean containsViewerPreferences)
    {
        this.containsViewerPreferences = containsViewerPreferences;
    }

    public boolean containsDocOutline()
    {
        return containsDocOutline;
    }

    public void setContainsDocOutline(boolean containsDocOutline)
    {
        this.containsDocOutline = containsDocOutline;
    }

    public boolean containsFiles()
    {
        return containsFiles;
    }

    public void setContainsFiles(boolean containsFiles)
    {
        this.containsFiles = containsFiles;
    }

    public boolean containsScript()
    {
        return containsScript;
    }

    public void setContainsScript(boolean containsScript)
    {
        this.containsScript = containsScript;
    }

    public boolean containsDests()
    {
        return containsDestinations;
    }

    public void setContainsDests(boolean containsDestinations)
    {
        this.containsDestinations = containsDestinations;
    }

    public List<String> getObjectTypes()
    {
        return objectTypes;
    }

    public void setObjectTypes(List<String> objectTypes)
    {
        this.objectTypes = objectTypes;
    }

    public List<String> getErrorList()
    {
        return errorList;
    }

    public void addError(String errorType, String errorMsg)
    {
        ArrayList currentError = new ArrayList();

        currentError.add(errorType);
        currentError.add(errorMsg);

        errorList.add(currentError);
    }

    public void addError(String errorType, String errorMsg, String stacktrace)
    {
        ArrayList currentError = new ArrayList();

        currentError.add(errorType);
        currentError.add(errorMsg);
        currentError.add(stacktrace);

        errorList.add(currentError);
    }

    public void addError(String errorType, Exception ex)
    {
        ArrayList currentError = new ArrayList();

        currentError.add(errorType);
        currentError.add(ex.getMessage());

        String stacktrace = "";
        if (ex.getStackTrace().length > 0)
        {
            for (int i = 0; i < ex.getStackTrace().length; i++)
            {
                stacktrace = stacktrace + ex.getStackTrace()[i] + "\n";
            }
        }
        currentError.add(stacktrace);

        errorList.add(currentError);
    }

    public void addErrorsList(List errors)
    {
        Iterator listItr = errors.iterator();
        while (listItr.hasNext())
        {
            errorList.add(listItr.next());
        }
    }

    /*
     * for testing
     * 
     * public void writeToFile(File outputFile, String filename) throws
     * IOException { boolean containsErrors = false; if (!errorList.isEmpty()) {
     * containsErrors = true; } String output = filename + "," + loadError + ","
     * + containsErrors + "," + isEncrypted + "," + containsFiles + "," +
     * containsForm + "," + containsWebCapture + "," + containsOptionalContent +
     * "," + containsArticles + "," + containsActions + "," + containsPgActions
     * + "," + containsAnnotations + "," + containsViewerPreferences + "," +
     * containsDocOutline + "," + containsScript + "," + containsPgLabels + ","
     * + containsPgDuration + "," + containsThumbnails + "," + containsSignature
     * + "," + containsDocRequirements + "," + containsDestinations + "\n";
     * 
     * FileWriter fw = new FileWriter(outputFile, true); fw.write(output);
     * 
     * fw.close(); }
     */

}
