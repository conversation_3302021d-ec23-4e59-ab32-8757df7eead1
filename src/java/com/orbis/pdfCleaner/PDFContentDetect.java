package com.orbis.pdfCleaner;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.pdfbox.cos.COSArray;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSDocument;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.cos.COSNumber;
import org.apache.pdfbox.cos.COSObject;
import org.apache.pdfbox.cos.COSStream;
import org.apache.pdfbox.pdmodel.PDDestinationNameTreeNode;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDDocumentNameDictionary;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDResources;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDFontDescriptorDictionary;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.optionalcontent.PDOptionalContentGroup;
import org.apache.pdfbox.pdmodel.graphics.optionalcontent.PDOptionalContentProperties;
import org.apache.pdfbox.pdmodel.interactive.action.PDDocumentCatalogAdditionalActions;
import org.apache.pdfbox.pdmodel.interactive.action.PDPageAdditionalActions;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationLink;
import org.apache.pdfbox.persistence.util.COSObjectKey;

/**
 * This class contains methods for detecting potentially harmful or disruptive
 * content within a PDF file.
 * 
 * <AUTHOR>
 */
public class PDFContentDetect
{

    public static ScanResult scanDocument(ByteArrayOutputStream incomingDocument)
            throws IOException
    {
        PDDocument document = PDDocument
                .load(new ByteArrayInputStream(incomingDocument.toByteArray()));

        ScanResult result = new ScanResult();
        result.setIsEncrypted(document.isEncrypted());

        return result;
    }

    /**
     * Scans the specified file for the following active content
     * <ul>
     * <li>Encryption
     * <li>Document and Page level actions
     * <li>Viewer Preferences
     * <li>Document Outline
     * <li>Page Labels
     * <li>Interactive Forms
     * <li>Articles
     * <li>Optional Content
     * <li>Digital Signatures
     * <li>Document Requirements
     * <li>Embedded Files
     * <li>Named JavaScript
     * <li>Named Destinations
     * <li>Annotations (other than links with URI actions)
     * <li>Thumbnails
     * <li>Page Durations
     * </ul>
     * 
     * @param doc
     *            absolute path of the file to be scanned
     * @return Returns a ScanResult containing the results of the scan and any
     *         errors thrown.
     */
    public static ScanResult scanFile(File doc)
    {

        PDDocument document = null;
        COSDocument COSdocument = null;

        // set up result
        ScanResult result = new ScanResult();

        // load file
        try
        {
            document = PDDocument.load(new FileInputStream(doc));
            result.setLoadError(false);
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to load document: " + ex);
            result.addError("Load Error", ex);
            result.setLoadError(true);
        }

        // scan if document is loaded
        if (!result.loadError() && document != null)
        {

            try
            {
                PDDocumentCatalog catalog = document.getDocumentCatalog();
                COSdocument = document.getDocument();

                result.setIsEncrypted(document.isEncrypted());
                result.setContainsActions(
                        PDFContentDetect.containsActions(catalog, result));
                result.setObjectTypes(
                        getObjectTypes(catalog.getAllPages(), result));
                result.setContainsViewerPreferences(PDFContentDetect
                        .containsViewerPreferences(catalog, result));
                result.setContainsDocOutline(
                        PDFContentDetect.containsDocOutline(catalog, result));
                result.setContainsPageLabels(
                        PDFContentDetect.containsPageLabels(catalog, result));
                result.setContainsForm(
                        PDFContentDetect.containsAcroForm(catalog, result));
                result.setContainsArticles(
                        PDFContentDetect.containsArticles(catalog, result));
                result.setContainsOptionalContent(
                        PDFContentDetect.containsOptionalContent(catalog, result));
                result.setContainsSignature(
                        PDFContentDetect.containsSignatures(document, result));
                result.setContainsDocReq(
                        PDFContentDetect.containsRequirements(catalog, result));

                // get Catalog COS dictionary
                COSDictionary catalogDictionary = document.getDocumentCatalog()
                        .getCOSDictionary();
                result.setContainsWebCapture(
                        containsWebCapture(catalogDictionary, result));

                if (document.getDocumentCatalog() != null)
                {
                    PDDocumentNameDictionary names = document.getDocumentCatalog()
                            .getNames();
                    result.setContainsFiles(
                            PDFContentDetect.containsEmbeddedFiles(names, result));
                    result.setContainsScript(
                            PDFContentDetect.containsJavaScript(names, result));
                    result.setContainsDests(
                            PDFContentDetect.containsDestinations(names, result));
                }
                else
                {
                    result.setContainsFiles(false);
                    result.setContainsScript(false);
                    result.setContainsDests(false);
                }

                // page level scan
                Boolean blnPgActions = false;
                Boolean blnPgAnnotations = false;
                Boolean blnPgDuration = false;
                Boolean blnThumbs = false;
                List pages = document.getDocumentCatalog().getAllPages();
                Iterator pageItr = pages.iterator();
                while (pageItr.hasNext())
                {
                    PDPage currentPage = (PDPage) pageItr.next();
                    if (!blnPgActions)
                    {
                        blnPgActions = PDFContentDetect
                                .containsPageActions(currentPage, result);
                    }
                    if (!blnPgAnnotations)
                    {
                        blnPgAnnotations = PDFContentDetect
                                .containsPageAnnotations(currentPage, result);
                    }

                    // COSDictionary trailer = doc.getDocument().getTrailer();
                    // COSDictionary root = (COSDictionary) trailer
                    // .getDictionaryObject(COSName.ROOT);
                    // COSDictionary acroForm = (COSDictionary) root
                    // .getDictionaryObject(COSName.ACRO_FORM);

                    // get page COS dictionary
                    COSDictionary pgDictionary = currentPage.getCOSDictionary();
                    if (!blnPgDuration)
                    {
                        blnPgDuration = containsPageDuration(pgDictionary, result);
                    }
                    if (!blnThumbs)
                    {
                        blnThumbs = containsThumbnails(pgDictionary, result);
                    }
                }

                result.setContainsPgActions(blnPgActions);
                result.setContainsAnnotations(blnPgAnnotations);
                result.setContainsPageDuration(blnPgDuration);
                result.setContainsThumbnails(blnThumbs);
            }
            catch (Exception ex)
            {
                System.out.println("ERROR: error encountered while scanning file"
                        + ex.toString());
                result.addError("Scan Error", ex.getMessage());
            }
            finally
            {
                try
                {
                    if (document != null)
                    {
                        document.close();
                    }
                    if (COSdocument != null)
                    {
                        COSdocument.close();
                    }
                }
                catch (Exception ex)
                {
                    System.out.println(
                            "ERROR: unable to close document: " + ex.toString());
                    result.addError("Close Error", ex.getMessage());
                }
            }
        }
        return result;
    }

    public static ScanResult scanFile(String doc)
    {
        return scanFile(new File(doc));
    }

    /**
     * Searches each page in the PDF for fonts. <br>
     * This is not used in the main JobMine PDF scan, but was provided to assist
     * the JobMine Help Desk with font issues.
     *
     * @param doc
     *            absolute path of the file to be scanned
     * @return Returns the a string describing the fonts used per page
     */
    public static String checkFontsByPage(String doc)
    {
        String strFonts = "";

        PDDocument document = null;
        COSDocument COSdocument = null;

        // load file
        try
        {
            document = PDDocument.load(new FileInputStream(new File(doc)));
        }
        catch (Exception ex)
        {
            return "Error loading file";
        }

        try
        {
            List pages = document.getDocumentCatalog().getAllPages();
            Iterator pageItr = pages.iterator();
            Integer pageNum = 1;
            while (pageItr.hasNext())
            {
                strFonts = strFonts + "\nPage " + pageNum;
                strFonts = strFonts
                        + checkFonts((PDPage) pageItr.next(), COSdocument);
                strFonts = strFonts
                        + "\n-----------------------------------------------";
                pageNum += 1;
            }
        }
        catch (Exception ex)
        {
            return strFonts;
        }

        return strFonts;
    }

    /**
     * Searches each page in the provided list for XObjects. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param pages
     *            a list of PDPage objects
     * @param result
     *            the result object
     * @return Returns the complete list of XObjects found in the pages
     */
    private static List<String> getObjectTypes(List<PDPage> pages,
            ScanResult result)
    {
        List<String> objectList = new ArrayList<String>();

        try
        {
            for (int i = 0; i < pages.size(); i++)
            {
                PDPage page = pages.get(i);

                if (page.getResources() != null)
                {
                    Map objects = page.getResources().getXObjects();

                    Iterator oi = objects.entrySet().iterator();
                    while (oi.hasNext())
                    {
                        Map.Entry entry = (Map.Entry) oi.next();
                        if (!objectList.contains(
                                entry.getValue().getClass().getCanonicalName()))
                        {
                            objectList.add(
                                    entry.getValue().getClass().getCanonicalName());
                        }
                    }
                }
            }
            return objectList;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan Xobjects: " + ex.toString());
            result.addError("Scan Error (objects)", ex.getMessage());
            return objectList;
        }
    }

    /**
     * Checks a single page for the presence of additional page actions. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param currentPage
     *            a PDPage object
     * @param result
     *            the result object
     * @return Returns <code>true</code> if additional page actions exist or if
     *         an error is encountered; returns <code>false</code> otherwise
     */
    private static boolean containsPageActions(PDPage currentPage,
            ScanResult result)
    {
        try
        {
            PDPageAdditionalActions additionalActions = currentPage.getActions();
            if ((additionalActions.getO() != null)
                    || (additionalActions.getC() != null))
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for page actions: " + ex.toString());
            result.addError("Scan Error (page actions)", ex.getMessage());
            return true;
        }
    }

    /**
     * Checks a single page for the presence of annotations. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param currentPage
     *            a PDPage object
     * @param result
     *            a ScanResult object
     * @return Returns <code>true</code> if
     *         <ul>
     *         <li>an annotation is found with Subtype != "Link", or
     *         <li>a Link annotation contains an action that is not of subtype
     *         "URI"
     *         <li>an error is encountered during the scan
     *         </ul>
     *         otherwise, the method returns <code>false</code>.
     */
    private static boolean containsPageAnnotations(PDPage currentPage,
            ScanResult result)
    {
        try
        {
            List annotations = currentPage.getAnnotations();
            Iterator annotationItr = annotations.iterator();
            while (annotationItr.hasNext())
            {
                Object oCurrentAnnotation = annotationItr.next();
                PDAnnotation currentAnnotation = (PDAnnotation) oCurrentAnnotation;

                if (currentAnnotation.getSubtype().equals("Link"))
                {
                    PDAnnotationLink link = (PDAnnotationLink) oCurrentAnnotation;
                    if (link.getAction() != null)
                    {
                        if (!link.getAction().getSubType().equals("URI"))
                        {
                            return true;
                        }
                    }
                }
                else
                {
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for annotations: " + ex);
            result.addError("Scan Error (annotations)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the catalog for document level actions. <br>
     * Any errors encountered are stored in the result object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the catalog contains document level
     *         actions, or if any errors are encountered during the scan;
     *         returns <code>false</code> otherwise.
     */
    private static boolean containsActions(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            // check document level actions
            PDDocumentCatalogAdditionalActions actions = catalog.getActions();
            if ((actions.getDP() != null) // actions performed after printing
                    || (actions.getDS() != null) // actions performed after
                                                 // saving
                    || (actions.getWC() != null) // actions performed before
                                                 // closing
                    || (actions.getWP() != null) // actions performed before
                                                 // printing
                    || (actions.getWS() != null) // actions performed before
                                                 // saving
                    || (catalog.getOpenAction() != null))
            { // actions performed on opening
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for document actions: " + ex.toString());
            result.addError("Scan Error (document action)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for forced document requirements. <br>
     * Any errors encountered are stored in the result object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the catalog contains document
     *         requirements, or if an errors are encountered; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsRequirements(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            COSArray reqDictionaries = (COSArray) catalog.getCOSDictionary()
                    .getDictionaryObject("Requirements");
            if (reqDictionaries != null)
            {
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for document requirements: "
                    + ex.toString());
            result.addError("Scan Error (requirements)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document for digital signatures. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param doc
     *            the document object
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the document contains any digital
     *         signatures, or if any errors are encountered; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsSignatures(PDDocument doc, ScanResult result)
    {
        try
        {
            COSDictionary trailer = doc.getDocument().getTrailer();
            COSDictionary root = (COSDictionary) trailer
                    .getDictionaryObject(COSName.ROOT);
            COSDictionary acroForm = (COSDictionary) root
                    .getDictionaryObject(COSName.ACRO_FORM);
            if (acroForm != null)
            {
                COSArray fields = (COSArray) acroForm
                        .getDictionaryObject(COSName.FIELDS);
                if (fields != null)
                {
                    for (int i = 0; i < fields.size(); i++)
                    {
                        COSDictionary field = (COSDictionary) fields.getObject(i);
                        String type = field.getNameAsString("FT");
                        if ("Sig".equals(type))
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for digital signatures: "
                    + ex.toString());
            result.addError("Scan Error (signature)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the page dictionary for a page duration value. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param pgDictionary
     *            the <class>COSDictionary</class> for specific page
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the page contains a duration value,
     *         or if an error was encountered; returns <code>false</code>
     *         otherwise.
     */
    private static boolean containsPageDuration(COSDictionary pgDictionary,
            ScanResult result)
    {
        try
        {
            COSNumber pgDuration = (COSNumber) pgDictionary
                    .getDictionaryObject("Dur");
            if (pgDuration != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for page duration: " + ex.toString());
            result.addError("Scan Error (page duration)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the page dictionary for thumbnail images. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param pgDictionary
     *            the <class>COSDictionary</class> for a single page
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the page contains any thumbnails, or
     *         if an error was encountered; returns <code>false</code>
     *         otherwise.
     */
    private static boolean containsThumbnails(COSDictionary pgDictionary,
            ScanResult result)
    {
        try
        {
            COSStream Thumbs = (COSStream) pgDictionary
                    .getDictionaryObject("Thumb");
            if (Thumbs != null)
            {
                Thumbs.close();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for thumbnails: " + ex.toString());
            result.addError("Scan Error (thumbnails)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the catalog's dictionary for web capture content. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param catalogDictionary
     *            the <class>COSDictionary</class> for specific page
     * @param result
     *            the result object
     * @return Returns <code>true</code> if the document catalog contains a web
     *         capture dictionary, or if an error was encountered; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsWebCapture(COSDictionary catalogDictionary,
            ScanResult result)
    {
        try
        {
            COSDictionary SpiderInfo = (COSDictionary) catalogDictionary
                    .getDictionaryObject("SpiderInfo");
            if (SpiderInfo != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for web capture content: "
                    + ex.toString());
            result.addError("Scan Error (webcapture)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for optional content. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains
     *         optional content, or if an error was encountered. Returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsOptionalContent(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            PDOptionalContentProperties OCproperties = catalog.getOCProperties();

            if (OCproperties != null)
            {
                String[] groupnames = OCproperties.getGroupNames();
                Collection groups = OCproperties.getOptionalContentGroups();
                Iterator itr = groups.iterator();
                while (itr.hasNext())
                {
                    PDOptionalContentGroup OCgroup = (PDOptionalContentGroup) itr
                            .next();
                }
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for optional content: " + ex.toString());
            result.addError("Scan Error (optional content)", ex);
            return true;
        }
    }

    /**
     * Scans the document catalog for article threads. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains
     *         article threads, or if any errors are encountered; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsArticles(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            if (catalog.getThreads().isEmpty())
            {
                return false;
            }
            return true;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for articles: " + ex.toString());
            result.addError("Scan Error (articles)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for interactive forms. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains
     *         interactive forms, or if an error was encountered during the
     *         scan; returns <code>false</code> otherwise.
     */
    private static boolean containsAcroForm(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            if (catalog.getAcroForm() != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for forms: " + ex.toString());
            result.addError("Scan Error (forms)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for page labels. <br>
     * Any errors encountered are stored in the <code>result</code> object.
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains page
     *         labels, or if an error was encountered during the scan; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsPageLabels(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            if (catalog.getPageLabels() != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for page labels: " + ex.toString());
            result.addError("Scan Error (page label)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for the existence of viewer preferences. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains viewer
     *         preferences, or if an error was encountered during the scan;
     *         returns <code>false</code> otherwise.
     */
    private static boolean containsViewerPreferences(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            // check page mode
            if (!catalog.getPageMode().equals("UseNone"))
            {
                return true;
            }
            // check viewer preferences
            if (catalog.getViewerPreferences() != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan viewer preferences" + ex.toString());
            result.addError("Scan Error (viewer preferences)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document catalog for the presence of a document outline. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param catalog
     *            the document catalog
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document catalog contains an
     *         outline, or if an error was encountered during the scan; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsDocOutline(PDDocumentCatalog catalog,
            ScanResult result)
    {
        try
        {
            if (catalog.getDocumentOutline() != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for document outline:" + ex.toString());
            result.addError("Scan Error (document outline)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the names dictionary for embedded files. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param names
     *            the document catalog's name dictionary
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document contains embedded
     *         files, or if an error was encountered during the scan; returns
     *         <code>false</code> otherwise.
     */
    private static boolean containsEmbeddedFiles(PDDocumentNameDictionary names,
            ScanResult result)
    {
        try
        {
            if (names != null)
            {
                if (names.getEmbeddedFiles() != null)
                {
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for embedded files: " + ex.toString());
            result.addError("Scan Error (embedded files)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document names dictionary for named javascript. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param names
     *            the document catalog's name dictionary
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document contains named
     *         javascript, or if an error was encountered during the scan;
     *         returns <code>false</code> otherwise.
     */
    private static boolean containsJavaScript(PDDocumentNameDictionary names,
            ScanResult result)
    {
        try
        {
            if (names != null)
            {
                if (names.getJavaScript() != null)
                {
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println(
                    "ERROR: unable to scan for named javascript: " + ex.toString());
            result.addError("Scan Error (javascript)", ex.getMessage());
            return true;
        }
    }

    /**
     * Scans the document names dictionary for named destinations. <br>
     * Any errors encountered are stored in the <code>result</code> object
     *
     * @param names
     *            the document catalog's name dictionary
     * @param result
     *            the object containing the complete scan results
     * @return Returns <code>true</code> if the document contains named
     *         destinations, or if an error was encountered during the scan;
     *         returns <code>false</code> otherwise.
     */
    private static boolean containsDestinations(PDDocumentNameDictionary names,
            ScanResult result)
    {
        try
        {
            if (names != null)
            {
                if (names.getDests() != null)
                {
                    PDDestinationNameTreeNode dests = names.getDests();
                    if (dests.getKids() != null)
                    {
                        List destList = dests.getKids();
                        if (!destList.isEmpty())
                        {
                            Iterator destItr = destList.iterator();
                            while (destItr.hasNext())
                            {
                                PDDestinationNameTreeNode dest = (PDDestinationNameTreeNode) destItr
                                        .next();
                            }
                        }
                    }
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            System.out.println("ERROR: unable to scan for named desinations: "
                    + ex.toString());
            result.addError("Scan Error (destinations)", ex.getMessage());
            return true;
        }
    }

    /**
     * for testing only <br>
     * this is not used in the PDF content scan
     */
    private static String checkFonts(PDPage page, COSDocument COSDoc)
            throws IOException
    {

        String fontReport = "";

        try
        {

            PDResources resources = page.findResources();

            if (resources != null)
            {
                Map fonts = resources.getFonts();

                if (fonts != null)
                {
                    Set keySet = fonts.keySet();

                    if (keySet != null)
                    {
                        Iterator keyItr = keySet.iterator();
                        while (keyItr.hasNext())
                        {
                            Boolean isEmbedded = false;
                            String BaseFont = "";
                            String Type = "";
                            String SubType = "";
                            String encoding = "";
                            String d = "";

                            Object item = keyItr.next();
                            PDFont font = (PDFont) fonts.get(item);

                            BaseFont = font.getBaseFont();
                            Type = font.getType();
                            SubType = font.getSubType();

                            if (font.getFontEncoding() != null)
                            {
                                encoding = font.getFontEncoding().toString();
                            }
                            // System.out.println(BaseFont);
                            fontReport = fontReport + "\n   " + BaseFont;

                            // type0/composite/CID fonts
                            if ("Type0".equals(font.getSubType()))
                            {
                                // System.out.println(" Type: 0");
                                fontReport = fontReport + "\n      Type: Type0";

                                if (font.getFontDescriptor() != null)
                                {
                                    d = font.getFontDescriptor().toString();
                                    if (font.getFontDescriptor().toString()
                                            .contains("Dictionary"))
                                    {
                                        // System.out.println(" descriptor
                                        // dictionary");
                                    }
                                }
                                else
                                {
                                    // System.out.println("main font descriptor
                                    // does not exist");
                                }

                                PDType0Font Type0Font = (PDType0Font) fonts
                                        .get(item);

                                COSDictionary COSType0Font = (COSDictionary) Type0Font
                                        .getCOSObject();
                                COSArray descendants = (COSArray) COSType0Font
                                        .getDictionaryObject("DescendantFonts");

                                if (descendants != null)
                                {
                                    Iterator descItr = descendants.iterator();
                                    while (descItr.hasNext())
                                    {
                                        COSObject fontRef = (COSObject) descItr
                                                .next();

                                        if (COSDoc != null)
                                        {

                                            // COSObjectKey fontKey = new
                                            // COSObjectKey(176,0);
                                            COSObjectKey fontKey = new COSObjectKey(
                                                    fontRef.getObjectNumber()
                                                            .intValue(),
                                                    fontRef.getGenerationNumber()
                                                            .intValue());

                                            COSBase COSfont = COSDoc
                                                    .getObjectFromPool(fontKey)
                                                    .getDictionaryObject(
                                                            COSName.getPDFName(
                                                                    "FontDescriptor"));
                                            if (COSfont != null)
                                            {
                                                COSDictionary fontDescr = (COSDictionary) COSfont;
                                                if (fontDescr.getDictionaryObject(
                                                        "FontFile") != null
                                                        || fontDescr
                                                                .getDictionaryObject(
                                                                        "FontFile2") != null
                                                        || fontDescr
                                                                .getDictionaryObject(
                                                                        "FontFile3") != null)
                                                {
                                                    // System.out.println("
                                                    // Embedded: true");
                                                    fontReport = fontReport
                                                            + "\n      Embedded: Yes";
                                                }
                                                else
                                                {
                                                    // System.out.println("
                                                    // Embedded: false");
                                                    fontReport = fontReport
                                                            + "\n      Embedded: No";
                                                }
                                            }

                                        }
                                        else
                                        {
                                        }
                                    }
                                }
                                else
                                {
                                    fontReport = fontReport
                                            + "\n      Embedded: Yes";
                                }
                            }
                            else if ("Type1".equals(font.getSubType())
                                    || "TrueType".equals(font.getSubType())
                                    || "CIDFontType0".equals(font.getSubType())
                                    || "CIDFontType2".equals(font.getSubType()))
                            {
                                fontReport = fontReport + "\n      Type: "
                                        + font.getSubType();

                                PDFontDescriptorDictionary dd;

                                if (font.getFontDescriptor() != null)
                                {
                                    d = font.getFontDescriptor().toString();
                                    if (font.getFontDescriptor().toString()
                                            .contains("Dictionary"))
                                    {
                                        dd = (PDFontDescriptorDictionary) font
                                                .getFontDescriptor();

                                        if (dd.getFontFile() != null
                                                || dd.getFontFile2() != null
                                                || dd.getFontFile3() != null)
                                        {
                                            isEmbedded = true;
                                            fontReport = fontReport
                                                    + "\n      Embedded: Yes";
                                        }
                                        else
                                        {
                                            fontReport = fontReport
                                                    + "\n      Embedded: No";
                                        }
                                    }
                                    else
                                    {
                                        fontReport = fontReport
                                                + "\n      Embedded: No";
                                    }
                                }
                            }
                            else if ("Type3".equals(font.getSubType()))
                            {
                                // for type3, font dictionary defines the font
                                fontReport = fontReport + "\n      Type: Type3";

                                COSDictionary COSType3Font = (COSDictionary) font
                                        .getCOSObject();
                                if (COSType3Font
                                        .getDictionaryObject("CharProcs") != null)
                                {
                                    fontReport = fontReport
                                            + "\n      Embedded: Yes";
                                }
                                else
                                {
                                    fontReport = fontReport
                                            + "\n      Embedded: No";
                                }

                            }
                        }
                    }
                }
                else
                {
                }
            }

            return fontReport;

        }
        catch (Exception ex)
        {
            System.out.println("  ERROR: fonts " + ex.toString());
            StackTraceElement[] stackTrace = ex.getStackTrace();
            for (int i = 0; i < stackTrace.length; i++)
            {
                System.out.println(stackTrace[i].toString());
            }

            return fontReport;
        }
    }
}
