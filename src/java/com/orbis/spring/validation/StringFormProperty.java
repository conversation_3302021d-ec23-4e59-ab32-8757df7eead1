package com.orbis.spring.validation;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.validation.Errors;

import com.orbis.spring.beans.SimpleValueBean;

/**
 * Validates a property that contains a String. Includes the ability to match
 * the property value to a regular expression, and/or to check that the property
 * value is equal to another 'confirmation' property.
 * 
 * <AUTHOR>
 */
public class StringFormProperty extends FormProperty
{

    /**
     * Optional path to a second property that is used to confirm this property.
     * If provided, the two property values must match in order to validate.
     */
    private String confirmationPropertyPath;

    /**
     * Optional regular expression that the property value must match in order
     * to validate.
     */
    private String regEx;

    private SimpleValueBean valueListBean;

    public SimpleValueBean getValueListBean()
    {
        return valueListBean;
    }

    public void setValueListBean(SimpleValueBean valueSourceBean)
    {
        this.valueListBean = valueSourceBean;
    }

    public String getConfirmationPropertyPath()
    {
        return confirmationPropertyPath;
    }

    public void setConfirmationPropertyPath(String confirmationPropertyPath)
    {
        this.confirmationPropertyPath = confirmationPropertyPath;
    }

    public String getRegEx()
    {
        return regEx;
    }

    public void setRegEx(String regEx)
    {
        this.regEx = regEx;
    }

    /**
     * Overrides the base implementation by checking if the property value is an
     * empty String.
     * 
     * @param propertyValue
     *            the property value to check
     */
    protected boolean isEmpty(Object propertyValue)
    {
        return StringUtils.isEmpty((String) propertyValue);
    }

    /**
     * Validates an empty property value by checking to see if it's required,
     * and checking to make sure that the confirmation value is also empty.
     * 
     * @param obj
     *            the object being validated
     * @param errors
     *            the errors object to populate with errors
     * @param propertyValue
     *            the property value to validate
     */
    protected void validateEmptyPropertyValue(Object obj, Errors errors,
            Object propertyValue)
    {
        if (this.shouldValidate(obj))
        {
            this.rejectMissingValue(errors);
        }
        else if (StringUtils.isNotEmpty(this.confirmationPropertyPath))
        {
            BeanWrapper bw = new BeanWrapperImpl(obj);
            Object confirmationValue = bw
                    .getPropertyValue(this.confirmationPropertyPath);
            if (confirmationValue != null
                    && StringUtils.isNotEmpty(confirmationValue.toString()))
            {
                this.rejectMissingValue(errors);
            }
        }
    }

    /**
     * Validates the property value by matching the optional regular expression
     * and/or by comparing it to the optional confirmation property.
     * 
     * @param obj
     *            the object being validated
     * @param errors
     *            the errors object to populate with errors
     * @param propertyValue
     *            the property value to validate
     */
    protected void validatePropertyValue(Object obj, Errors errors,
            Object propertyValue)
    {
        String stringValue = (String) propertyValue;

        if (this.regEx != null && stringValue.matches(regEx) == false)
        {
            this.rejectInvalidValue(errors);
        }
        else if (StringUtils.isNotEmpty(this.confirmationPropertyPath))
        {
            BeanWrapper bw = new BeanWrapperImpl(obj);
            Object confirmationValue = bw
                    .getPropertyValue(this.confirmationPropertyPath);
            if (confirmationValue == null
                    || confirmationValue.toString().equals(""))
            {
                this.rejectValue(this.confirmationPropertyPath, "missing",
                        "Please confirm.", errors);
            }
            else if (confirmationValue.equals(propertyValue) == false)
            {
                this.rejectValue(this.confirmationPropertyPath, "doesNotMatch",
                        "Please enter a matching value.", errors);
            }
        }
    }

}
