package com.orbis.cachedSearch;

import com.orbis.utils.OrbisThread;

public class OrbisRefreshCacheThread extends OrbisThread
{
    private String threadName;

    private OrbisCacheRefresh cacheRefresh;

    public OrbisRefreshCacheThread(OrbisCacheRefresh cacheRefresh, int mode)
    {
        super();

        this.cacheRefresh = cacheRefresh;
        this.threadName = cacheRefresh.getCacheRefreshName() + " Thread";
    }

    @Override
    public void doRun() throws Exception
    {
        try
        {
            this.setTotal(1);
            this.setProgress(0);
            cacheRefresh.refreshCache(cacheRefresh.getMode());
            this.setProgress(1);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    public String getThreadName()
    {
        return threadName;
    }
}
