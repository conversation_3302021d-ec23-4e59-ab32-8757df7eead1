package com.orbis.cachedSearch;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.hibernate.SessionFactoryBuilder;
import org.hibernate.HibernateException;
import org.hibernate.mapping.PersistentClass;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;

import com.google.common.collect.Lists;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.function.OrbisPredicate;

import jakarta.persistence.EntityManager;


public abstract class OrbisContentItemCacheRefresh<E extends OrbisCachable<Integer>>
        extends OrbisCacheRefresh<Integer, E>
{
    private String LOAD_HQL = "";

    private String LOAD_COUNT_HQL = "";

    private String UPDATE_HQL = "";

    private String UPDATE_COUNT_HQL = "";

    private final String mainAlias;

    private final OrbisCacheRefreshDependency[] innerDeps;

    private final OrbisCacheRefreshDependency[] outerDeps;

    private final Map<String, Map<Object, Object>> innerDepsCache = new HashMap<>();

    private final Map<String, Map<Object, Object>> outerDepsCache = new HashMap<>();

    private Map<String, Map<Object, Object>> globalCache = new HashMap<>();

    public OrbisContentItemCacheRefresh(long duration, TimeUnit timeUnit,
            Class mainClass, String mainAlias)
    {
        super(duration, timeUnit, mainClass, mainAlias);

        this.mainAlias = mainAlias;
        this.innerDeps = getInnerDependencies();
        this.outerDeps = getOuterDependencies();

        for (int d = 0; d < this.innerDeps.length; d++)
        {
            innerDepsCache.put(this.innerDeps[d].getPropertyName(), new HashMap());
        }

        for (int d = 0; d < this.outerDeps.length; d++)
        {
            outerDepsCache.put(this.outerDeps[d].getPropertyName(), new HashMap());
        }

        for (OrbisCacheRefreshDependency d : innerDeps)
        {
            d.initInnerDepsCache();
        }

        for (OrbisCacheRefreshDependency d : outerDeps)
        {
            d.initInnerDepsCache();
        }
    }

    public String getMainAlias()
    {
        return mainAlias;
    }

    public abstract String getLoadHql();

    public abstract String getLoadCountSql();

    public String[] getLoadDepHql()
    {
        return new String[0];
    }

    public abstract String getUpdateHql();

    public abstract String getUpdateCountSql();

    public String[] getUpdateDepHql()
    {
        return new String[0];
    }

    public String getSelectHql()
    {
        return "SELECT ".concat(StringUtils.join(getColumnsList(), ", "));
    }

    public String getJoinHql()
    {
        StringBuilder sb = new StringBuilder();
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            if (dep.isLeftJoin())
            {
                sb.append(" LEFT");
            }
            sb.append(" JOIN ").append(dep.getEntityProperty()).append(" ")
                    .append(dep.getPropertyName());
        }
        return sb.toString();
    }

    public List<String> getInnerDepColumns()
    {
        List<String> ret = new ArrayList<>();
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            for (String c : dep.getColumns())
            {
                ret.add(dep.getEntityProperty()
                        .concat(c.substring(c.indexOf('.'))));
            }
        }
        return ret;
    }

    public String getSelectFromHql()
    {
        StringBuilder sb = new StringBuilder().append(getSelectHql())
                .append(" FROM ").append(getMainClass().getSimpleName()).append(" ")
                .append(mainAlias).append(getJoinHql());
        return sb.toString();
    }

    public String[] getColumns()
    {
        return new String[] { mainAlias.concat(".").concat("id") };
    }

    public List<String> getColumnsList()
    {
        List<String> ret = Lists.newArrayList(getColumns());
        ret.addAll(getInnerDepColumns());
        return ret;
    }

    @Override
    public void doLoad(EntityManager session) throws HibernateException, SQLException
    {
        LOAD_HQL = getLoadHql();
        LOAD_COUNT_HQL = getLoadCountSql();

        final int loadCount = PortalUtils.getJtReadOnly()
                .queryForInt(LOAD_COUNT_HQL);

        final boolean loggingEnabled = isLoggingEnabled();

        if (loggingEnabled)
        {
            getLogger().info(String.format("loading %d results", loadCount));
            getLogger().info(String.format("load HQL: %s", LOAD_HQL));
        }

        List<OrbisHqlResultSet> resultsList = PortalUtils.getHtReadOnly()
                .f(LOAD_HQL);

        if (loggingEnabled)
        {
            getLogger()
                    .info(String.format("%d results loaded", resultsList.size()));
            getLogger().info(
                    String.format("processing %d results", resultsList.size()));
        }

        Function<E, E> transformFunction = getTransformFunction();

        OrbisPredicate<E> activePredicate = getActivePredicate();

        OrbisCacheRefreshHelper.clearDependencyCache(outerDepsCache);
        globalCache = new HashMap<>();

        for (OrbisCacheRefreshDependency dep : outerDeps)
        {
            OrbisCacheRefreshHelper.mergeDependenciesIntoResults(resultsList, dep,
                    outerDepsCache, null, MODE_LOAD, globalCache);
        }

        OrbisCacheRefreshHelper.clearDependencyCache(innerDepsCache);

        List<E> entityResults = OrbisCacheRefreshHelper.getEntitiesForResultSets(
                resultsList, getMainClass(), innerDepsCache, null, null,
                globalCache, "mainClass");

        for (E p : entityResults.stream().filter(activePredicate)
                .map(transformFunction).toList())
        {
            this.replaceOrPut(p.getId(), p);
        }

        if (loggingEnabled)
        {
            getLogger().info(
                    String.format("%d results processed", entityResults.size()));
            getLogger().info(String.format("%d added", this.size()));
            getLogger().info("loading finished");
        }

        getLastFullLoad().set(LocalDateTime.now());
    }

    @Override
    public void doUpdate(EntityManager session) throws HibernateException {
        UPDATE_HQL = getUpdateHql();
        UPDATE_COUNT_HQL = getUpdateCountSql();

        final int updateCount = PortalUtils.getJtReadOnly()
                .queryForInt(UPDATE_COUNT_HQL);

        final boolean loggingEnabled = isLoggingEnabled();

        if (updateCount > 0)
        {
            if (loggingEnabled)
            {
                getLogger().info(String.format("updating %d results", updateCount));
                getLogger().info(String.format("update HQL: %s", UPDATE_HQL));
            }
            runUpdate(session, updateCount);
        }
        else if (loggingEnabled)
        {
            getLogger().info("skipping update (no results)");
        }

        evictDeletedItems();

        if (updateCount > 0 && loggingEnabled)
        {
            getLogger().info("update finished");
        }
    }

    protected void runUpdate(EntityManager session, final int updateCount)
            throws HibernateException
    {
        List<OrbisHqlResultSet> resultsList = PortalUtils.getHtReadOnly()
                .f(UPDATE_HQL);

        final boolean loggingEnabled = isLoggingEnabled();

        boolean performedUpdate = false;

        final List<E> added = Lists.newArrayList();
        final List<E> updated = Lists.newArrayList();
        final List<Integer> removedIds = Lists.newArrayList();

        Function<E, E> transformFunction = getTransformFunction();

        OrbisPredicate<E> activePredicate = getActivePredicate();

        Map<String, Map<Object, Object>> newOuterDepsCache = OrbisCacheRefreshHelper
                .getDependencyCacheForUpdate(outerDepsCache);

        for (OrbisCacheRefreshDependency dep : outerDeps)
        {
            OrbisCacheRefreshHelper.mergeDependenciesIntoResults(resultsList, dep,
                    newOuterDepsCache, outerDepsCache, MODE_UPDATE, globalCache);
        }

        Map<String, Map<Object, Object>> newInnerDepsCache = OrbisCacheRefreshHelper
                .getDependencyCacheForUpdate(innerDepsCache);

        List<E> entityResults = OrbisCacheRefreshHelper.getEntitiesForResultSets(
                resultsList, getMainClass(), newInnerDepsCache, innerDepsCache,
                null, globalCache, "mainClass");

        if (loggingEnabled)
        {
            getLogger().info(String.format("updating %d results", updateCount));
        }

        for (E i : entityResults)
        {
            if (activePredicate.test(i))
            {
                i = transformFunction.apply(i);
                E previous = this.replaceOrPut(i.getId(), i);

                if (previous == null)
                {
                    if (loggingEnabled)
                    {
                        getLogger().info(String.format("%s added", i));
                    }
                    added.add(i);
                }
                else
                {
                    if (loggingEnabled)
                    {
                        getLogger().info(String.format("%s updated", i));
                    }
                    OrbisCacheRefreshHelper.updateDependencies(i, previous,
                            outerDeps);
                    updated.add(i);
                }
                performedUpdate = true;
            }
            else
            {
                E previous = this.remove(i.getId());
                if (previous == null)
                {
                    if (loggingEnabled)
                    {
                        getLogger().info(String
                                .format("failed to remove %s (not in cache)", i));
                    }
                }
                else
                {
                    if (loggingEnabled)
                    {
                        getLogger().info(String.format("%s removed", i));
                    }
                    performedUpdate = true;
                    removedIds.add(i.getId());
                }
            }
        }

        if (performedUpdate)
        {
            notifyListeners(added, updated, removedIds);
            getLastUpdate().set(LocalDateTime.now());
        }
    }

    private void evictDeletedItems()
    {
        try
        {
            Set<Integer> deletedEntities = (Set<Integer>) PortalUtils
                    .getJtReadOnly()
                    .query("select contentItemId from deleted_entity where contentItemClassName=? and deletedAt >= ?",
                            new Object[] { getMainClass().getName(),
                                    DateUtils.todayStarts() },
                            (ResultSetExtractor) rs -> {
                                Set<Integer> rtn = new HashSet<>();
                                while (rs.next())
                                {
                                    rtn.add(rs.getInt("contentItemId"));
                                }

                                return rtn;
                            });

            this.keySet().removeAll(deletedEntities);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    public E addOrUpdateEntity(Integer entityId)
    {
        E ret = null;
        if (isEnabled())
        {
            List<OrbisHqlResultSet> resultsList = getEntityResults(" = ?",
                    entityId);
            if (resultsList != null)
            {
                List<E> addOrUpdateEntities = addOrUpdateEntities(resultsList, true,
                        true);
                ret = addOrUpdateEntities.stream().filter(Objects::nonNull)
                        .findFirst().orElse(null);
            }
        }
        return ret;
    }

    /**
     * Update the dependencies for the entity with the first id returned from
     * the provided HQL clause that is contained in the cache
     * 
     * This method is used to update dependency entities after saving them by
     * providing an HQL select query that returns ids for entities associated
     * with the dependency
     * 
     * Please ensure that all aliases in the provided HQL clause will not
     * conflict with any aliases used by the main cache entity nor its
     * dependencies
     * 
     * @param entityIdsHqlClause
     */
    public void updateEntityDependencies(String entityIdsHqlClause)
    {
        if (isEnabled())
        {
            List<OrbisHqlResultSet> resultsList = getEntityResults(
                    String.format(" in (%s)", entityIdsHqlClause));
            if (resultsList != null && !resultsList.isEmpty())
            {
                String idSelect = resultsList.get(0).containsKey("id") ? "id"
                        : String.format("%s.id", mainAlias);
                OrbisHqlResultSet firstResult = resultsList.stream()
                        .filter(rs -> this.containsKey(rs.get(idSelect)))
                        .findFirst().orElse(null);
                if (firstResult != null)
                {
                    addOrUpdateEntities(Lists.newArrayList(firstResult), false,
                            false);
                }
            }
        }
    }

    public List<E> addOrUpdateEntities(String entityIdsHqlClause, boolean canAdd,
            boolean canUpdate)
    {
        List<E> ret = null;
        if (isEnabled())
        {
            List<OrbisHqlResultSet> resultsList = getEntityResults(
                    String.format(" in (%s)", entityIdsHqlClause));
            if (resultsList != null)
            {
                ret = addOrUpdateEntities(resultsList, canAdd, canUpdate);
            }
        }
        return ret;
    }

    private List<E> addOrUpdateEntities(List<OrbisHqlResultSet> entitiesList,
            boolean canAdd, boolean canUpdate)
    {
        List<E> ret = new ArrayList();
        if (!entitiesList.isEmpty())
        {
            Function transformFunction = getTransformFunction();

            Map<String, Map<Object, Object>> newOuterDepsCache = OrbisCacheRefreshHelper
                    .getDependencyCacheForUpdate(outerDepsCache);

            for (OrbisCacheRefreshDependency dep : outerDeps)
            {
                OrbisCacheRefreshHelper.mergeDependenciesIntoResults(entitiesList,
                        dep, newOuterDepsCache, outerDepsCache, MODE_LOAD,
                        globalCache);
            }

            Map<String, Map<Object, Object>> newInnerDepsCache = OrbisCacheRefreshHelper
                    .getDependencyCacheForUpdate(innerDepsCache);

            List<E> entityResults = (List<E>) OrbisCacheRefreshHelper
                    .getEntitiesForResultSets(entitiesList, getMainClass(),
                            newInnerDepsCache, innerDepsCache, null, globalCache,
                            "mainClass")
                    .stream().map(transformFunction).collect(Collectors.toList());
            if (canAdd || canUpdate)
            {
                final boolean loggingEnabled = isLoggingEnabled();
                for (E entityResult : entityResults)
                {
                    Integer entityId = entityResult.getId();
                    if (canAdd && canUpdate)
                    {
                        E replaceOrPut = this.replaceOrPut(entityId, entityResult);
                        if (replaceOrPut != null)
                        {
                            ret.add(replaceOrPut);
                            if (loggingEnabled)
                            {
                                getLogger().info(String.format("%s added",
                                        entityResult.toString()));
                            }
                        }
                    }
                    else
                    {
                        boolean exists = this.containsKey(entityId);
                        if (exists && canAdd)
                        {
                            ret.add(this.put(entityId, entityResult));
                            if (loggingEnabled)
                            {
                                getLogger().info(String.format("%s added",
                                        entityResult.toString()));
                            }
                        }
                        else if (!exists && canUpdate)
                        {
                            ret.add(this.replace(entityId, entityResult));
                            if (loggingEnabled)
                            {
                                getLogger().info(String.format("%s updated",
                                        entityResult.toString()));
                            }
                        }
                    }
                }
            }
        }
        return ret;
    }

    private List<OrbisHqlResultSet> getEntityResults(String entityIdHql,
            Object... queryArgs)
    {
        List<OrbisHqlResultSet> ret = null;
        PersistentClass meta = null;
        try
        {
            meta = SessionFactoryBuilder.getMeta()
                    .getEntityBinding(getMainClass().getName());
        }
        catch (HibernateException e)
        {
            e.printStackTrace();
        }
        if (meta != null)
        {
            String hql = new StringBuilder().append(getSelectFromHql())
                    .append(" WHERE ").append(mainAlias).append(".")
                    .append(meta.getIdentifierProperty().getName())
                    .append(entityIdHql).toString();
            ret = PortalUtils.getHtReadOnly().f(hql, queryArgs);
        }
        return ret;
    }
}
