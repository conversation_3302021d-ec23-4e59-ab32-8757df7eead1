package com.orbis.cachedSearch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.google.common.collect.Lists;

public class OrbisCacheRefreshDependency
{
    private final Class clazz;

    private final Class mainClass;

    private final String alias;

    private final String mainAlias;

    private final String joinProperty;

    private final String joinToProperty;

    private final String propertyName;

    private final boolean manyToOne;

    private final boolean leftJoin;

    private final Integer[] idColIndexes;

    private final OrbisCacheRefreshDependency[] innerDeps;

    private Map<String, Map<Object, Object>> innerDepsCache = null;

    /**
     * @param clazz
     *            Class of the dependent entity
     * @param alias
     *            alias of {@code clazz} in the query
     * @param mainClass
     *            Class of the main entity of the cache
     * @param mainAlias
     *            alias of {@code mainClass} in the query
     * @param joinProperty
     *            property on {@code clazz} to use as join field. Match happens
     *            this value .equals to {@code joinToProperty} value. This
     *            property needs to be in the columns you query on.
     * @param joinToProperty
     *            property on {@code mainClass} to join to. Match happens this
     *            value .equals to {@code joinProperty} value
     * @param propertyName
     *            Field to fill in on the main class. If this is a
     *            {@code manyToOne==true}, the collection needs to be
     *            initialized with a collection to save into as the cacheRefresh
     *            process will not initialize it
     * @param manyToOne
     *            if this is a list being populated on {@code mainClass}
     * @param leftJoin
     */
    public OrbisCacheRefreshDependency(Class clazz, String alias, Class mainClass,
            String mainAlias, String joinProperty, String joinToProperty,
            String propertyName, boolean manyToOne, boolean leftJoin)
    {
        this.clazz = clazz;
        this.alias = alias;
        this.mainClass = mainClass;
        this.mainAlias = mainAlias;
        this.joinProperty = joinProperty;
        this.joinToProperty = joinToProperty;
        this.propertyName = propertyName;
        this.manyToOne = manyToOne;
        this.leftJoin = leftJoin;
        this.innerDeps = getInnerDependencies();

        List<Integer> idColIndexesList = new ArrayList<>();
        populateIdColIndexes(idColIndexesList, 1);
        this.idColIndexes = idColIndexesList
                .toArray(new Integer[idColIndexesList.size()]);
    }

    public void initInnerDepsCache()
    {
        innerDepsCache = new HashMap<>();
        List<String> propertyNames = getInnerDepPropertyNames();
        for (String p : propertyNames)
        {
            innerDepsCache.put(p, new HashMap());
        }
    }

    public List<String> getInnerDepPropertyNames()
    {
        List<String> ret = new ArrayList<>();
        boolean subDep = innerDepsCache == null;
        if (subDep)
        {
            ret.add(getPropertyName());
        }
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            List<String> innerDepPropertyNames = dep.getInnerDepPropertyNames();
            if (subDep)
            {
                for (String p : innerDepPropertyNames)
                {
                    ret.add(alias.concat(".").concat(p));
                }
            }
            else
            {
                ret.addAll(innerDepPropertyNames);
            }
        }
        return ret;
    }

    public int populateIdColIndexes(List<Integer> colIdIndexes, int curIndex)
    {
        colIdIndexes.add(curIndex);
        curIndex += getColumns().length;
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            curIndex = dep.populateIdColIndexes(colIdIndexes, curIndex);
        }
        return curIndex;
    }

    public String getSelectHql()
    {
        StringBuilder sb = new StringBuilder().append("SELECT DISTINCT ")
                .append(StringUtils.join(getColumnsList(), ", "));

        return sb.toString();
    }

    public String[] getColumns()
    {
        return new String[] { alias.concat("id") };
    }

    private List<String> getColumnsList()
    {
        List<String> ret = Lists.newArrayList(getColumns());
        ret.addAll(getInnerDepColumns());
        return ret;
    }

    public String getSelectFromHql()
    {
        StringBuilder sb = new StringBuilder().append(getSelectHql())
                .append(" FROM ").append(clazz.getSimpleName()).append(" ")
                .append(alias).append(getJoinHql(false)).append(", ")
                .append(mainClass.getSimpleName()).append(" ").append(mainAlias);
        return sb.toString();
    }

    public String getLoadHql()
    {
        StringBuilder sb = new StringBuilder().append(getSelectFromHql())
                .append(getWhereHql(true)).append(getOrderByHql());
        return sb.toString();
    }

    public String getUpdateHql()
    {
        StringBuilder sb = new StringBuilder().append(getSelectFromHql())
                .append(getWhereHql(false)).append(getOrderByHql());
        return sb.toString();
    }

    public String getWhereHql(boolean load)
    {
        StringBuilder sb = new StringBuilder().append(" WHERE ").append(alias)
                .append(".").append(joinProperty).append(" = ").append(mainAlias)
                .append(".").append(joinToProperty);
        return sb.toString();
    }

    public String getOrderByHql()
    {
        StringBuilder sb = new StringBuilder().append(" ORDER BY ")
                .append(StringUtils.join(idColIndexes, ", "));
        return sb.toString();
    }

    public Class getClazz()
    {
        return clazz;
    }

    public Class getMainClass()
    {
        return mainClass;
    }

    public String getAlias()
    {
        return alias;
    }

    public String getMainAlias()
    {
        return mainAlias;
    }

    public String getJoinProperty()
    {
        return joinProperty;
    }

    public String getJoinToProperty()
    {
        return joinToProperty;
    }

    public String getEntityProperty()
    {
        StringBuilder sb = new StringBuilder().append(mainAlias).append(".")
                .append(propertyName);
        return sb.toString();
    }

    public String getPropertyName()
    {
        return propertyName;
    }

    public boolean isManyToOne()
    {
        return manyToOne;
    }

    public boolean isLeftJoin()
    {
        return leftJoin;
    }

    protected String getJoinHql(boolean overrideLeftJoin)
    {
        StringBuilder sb = new StringBuilder();
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            if (leftJoin || overrideLeftJoin || dep.isLeftJoin())
            {
                sb.append(" LEFT");
                overrideLeftJoin = true;
            }
            sb.append(" JOIN ").append(dep.getEntityProperty()).append(" ")
                    .append(dep.getPropertyName())
                    .append(dep.getJoinHql(overrideLeftJoin));
        }
        return sb.toString();
    }

    protected List<String> getInnerDepColumns()
    {
        List<String> ret = new ArrayList<>();
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            ret.addAll(dep.getColumnsList());
        }
        return ret;
    }

    /**
     * Inner dependencies will chain down on the query so inner dependencies do
     * not need to have a from or where as the columns are added to the top
     * level outer dependency query
     *
     * @return
     */
    protected OrbisCacheRefreshDependency[] getInnerDependencies()
    {
        return new OrbisCacheRefreshDependency[0];
    }

    protected OrbisCacheRefreshDependency[] getInnerDependenciesCached()
    {
        return innerDeps;
    }

    protected OrbisCacheRefreshDependency[] getOuterDependencies()
    {
        return new OrbisCacheRefreshDependency[0];
    }

    public Map<String, Map<Object, Object>> getInnerDepsCache()
    {
        return innerDepsCache;
    }

    public Map<String, String> getJoinMappings(boolean subDep)
    {
        Map<String, String> ret = new HashMap<>();
        for (OrbisCacheRefreshDependency dep : innerDeps)
        {
            if (subDep)
            {
                ret.put(dep.getPropertyName(), dep.getEntityProperty());
            }
            Map<String, String> subMappings = dep.getJoinMappings(true);
            for (Map.Entry<String, String> s : subMappings.entrySet())
            {
                String value = s.getValue();
                if (subDep)
                {
                    value = alias.concat(".").concat(value);
                }
                ret.put(s.getKey(), value);
            }
        }
        return ret;
    }
}
