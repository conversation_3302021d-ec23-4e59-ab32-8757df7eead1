package com.orbis.cachedSearch;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.orbis.utils.NumberUtils;

public abstract class OrbisCacheSearchMetadata
{
    private int page;

    public OrbisCacheSearchMetadata()
    {
    }

    public int getPage()
    {
        return page;
    }

    public void setPage(int page)
    {
        this.page = page;
    }

    public void update(final Map<String, String[]> parameterMap)
    {
        final String page = firstOrEmpty(parameterMap.get("page"));

        if (StringUtils.isNotEmpty(page))
            setPage(NumberUtils.asInteger(page));
    }

    private static String firstOrEmpty(final String[] arr)
    {
        if (arr == null)
            return "";
        if (arr.length == 0)
            return "";
        return arr[0];
    }
}
