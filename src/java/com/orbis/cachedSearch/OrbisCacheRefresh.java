package com.orbis.cachedSearch;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.EventObject;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

import com.orbis.portal.QueryCallback;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.orm.hibernate5.HibernateCallback;

import com.google.common.collect.ImmutableList;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.cachemap.CacheMap;
import com.orbis.utils.cachemap.ExpiringEntry;
import com.orbis.utils.function.OrbisPredicate;


public abstract class OrbisCacheRefresh<K, E extends OrbisCachable>
        extends CacheMap<K, E> implements QueryCallback, InitializingBean
{
    public static final int MODE_LOAD = 0;

    public static final int MODE_UPDATE = 1;

    private final Class mainClass;

    private final AtomicReference<LocalDateTime> lastFullLoad = new AtomicReference<>(
            LocalDateTime.now());

    private final AtomicReference<LocalDateTime> lastUpdate = new AtomicReference<>(
            LocalDateTime.now());

    private volatile boolean enabled = false;

    private volatile boolean running = false;

    private volatile boolean loggingEnabled = false;

    protected volatile int mode;

    private ScheduledExecutorService scheduledExecutorService = Executors
            .newScheduledThreadPool(1, (r) -> new Thread(r, "OrbisCacheRefresh - "
                    .concat(OrbisCacheRefresh.this.getClass().getSimpleName())));

    protected Log logger;

    public OrbisCacheRefresh(long duration, TimeUnit timeUnit, Class mainClass,
            String mainAlias)
    {
        super(CacheMap.<K, E> builder().expiration(duration, timeUnit));
        this.mainClass = mainClass;

        this.enabled = OrbisCacheRefreshHelper.isCacheRefreshEnabled(this);
    }

    /**
     * @return a Function to that will apply any transformations on an object
     *         from the database to one stored in the cache. Usually includes
     *         setting a field of all strings to search on for a keyword search
     */
    public Function<E, E> getTransformFunction()
    {
        return new Function<E, E>()
        {
            @Override
            public E apply(E entity)
            {
                return entity;
            }
        };
    }

    /**
     * @return a predicate of whether or not the cache item should be considered
     *         active to be in the cache
     */
    public abstract OrbisPredicate<E> getActivePredicate();

    @Override
    public void afterPropertiesSet() throws Exception
    {
        getRefreshThread(MODE_LOAD).start();

        scheduledExecutorService.scheduleWithFixedDelay(
                getRefreshThread(MODE_UPDATE), getInitialDelay(),
                getExpirationInNanos().get(), TimeUnit.NANOSECONDS);
    }

    /**
     * Retrieves the initial delay in nanoseconds for scheduling cache refresh
     * operations.
     */
    protected long getInitialDelay()
    {
        return getExpirationInNanos().get();
    }

    @Override
    protected void scheduleEntry(ExpiringEntry<K, E> entry)
    {
        // do nothing
    }

    @Override
    protected void resetEntry(ExpiringEntry<K, E> entry, boolean scheduleFirstEntry)
    {
        // do nothing
    }

    public OrbisRefreshCacheThread getRefreshThread(int mode)
    {
        OrbisRefreshCacheThread ret = new OrbisRefreshCacheThread(this, mode)
        {
            @Override
            public void doRun() throws Exception
            {
                int runId = (int) (Math.random() * 100);
                if (loggingEnabled)
                {
                    getLogger().info(String.format("Run id:%s started", runId));
                }
                setTotal(1);
                setProgress(0);
                if (!running)
                {
                    synchronized (OrbisCacheRefresh.this)
                    {
                        running = true;
                        setDateStarted(new Date());
                        refreshCache(mode);
                        running = false;
                    }
                }
                if (loggingEnabled)
                {
                    getLogger().info(String.format("Run id:%s ended", runId));
                }
                setProgress(getTotal());
            }
        };

        return ret;
    }

    public void refreshCache(int mode)
    {
        try
        {
            boolean enabled = OrbisCacheRefreshHelper.isCacheRefreshEnabled(this);
            if (enabled != isEnabled())
            {
                setEnabled(enabled);
                if (enabled)
                {
                    mode = MODE_LOAD;
                }
                else
                {
                    clear();
                }
            }
            if (enabled)
            {
                setMode(mode);
                PortalUtils.getHt().execute(this);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public void updateElement(E element)
    {
        try
        {
            replaceOrPut((K) element.getId(), element);
            getTransformFunction().apply(element);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    @SneakyThrows
    public Object doInHibernate(EntityManager session)
            throws HibernateException {
        if (mode == MODE_LOAD || shouldPerformFullLoad())
        {
            doLoad(session);
        }
        else if (mode == MODE_UPDATE)
        {
            doUpdate(session);
        }

        return null;
    }

    protected Log getLogger()
    {
        if (loggingEnabled && logger == null)
        {
            logger = LogFactory.getLog(this.getClass());
        }
        return logger;
    }

    public abstract String getCacheRefreshName();

    public ScheduledExecutorService getScheduledExecutorService()
    {
        return scheduledExecutorService;
    }

    public boolean isEnabled()
    {
        return enabled;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public int getMode()
    {
        return mode;
    }

    public void setMode(int mode)
    {
        this.mode = mode;
    }

    public abstract void doLoad(EntityManager session)
            throws HibernateException, SQLException;

    public abstract void doUpdate(EntityManager session)
            throws HibernateException, SQLException;

    public abstract E addOrUpdateEntity(K entityId);

    public static class CacheUpdateEvent<F, K> extends EventObject
    {
        private static final long serialVersionUID = 3945233442527251591L;

        private final List<F> added;

        private final List<F> updated;

        private final List<K> removedIds;

        public CacheUpdateEvent(final Object source, final List<F> added,
                final List<F> updated, final List<K> removed)
        {
            super(source);
            this.added = ImmutableList.copyOf(added);
            this.updated = ImmutableList.copyOf(updated);
            this.removedIds = ImmutableList.copyOf(removed);
        }

        public List<F> getUpdated()
        {
            return updated;
        }

        public List<K> getRemovedIds()
        {
            return removedIds;
        }

        public List<F> getAdded()
        {
            return added;
        }
    }

    public static interface CacheUpdateListener
    {
        void cacheUpdate(CacheUpdateEvent event);
    }

    private List<CacheUpdateListener> cacheUpdateEventListeners = Collections
            .emptyList();

    public void setCacheUpdateListeners(final List<CacheUpdateListener> listeners)
    {
        this.cacheUpdateEventListeners = ImmutableList.copyOf(listeners);
    }

    protected void notifyListeners(final List<E> added, final List<E> updated,
            final List<K> removedIds)
    {
        final CacheUpdateEvent event = new CacheUpdateEvent(this, added, updated,
                removedIds);
        for (final CacheUpdateListener listener : cacheUpdateEventListeners)
        {
            listener.cacheUpdate(event);
        }
    }

    public Class getMainClass()
    {
        return mainClass;
    }

    public OrbisCacheRefreshDependency[] getInnerDependencies()
    {
        return new OrbisCacheRefreshDependency[0];
    }

    public OrbisCacheRefreshDependency[] getOuterDependencies()
    {
        return new OrbisCacheRefreshDependency[0];
    }

    public AtomicReference<LocalDateTime> getLastFullLoad()
    {
        return lastFullLoad;
    }

    public AtomicReference<LocalDateTime> getLastUpdate()
    {
        return lastUpdate;
    }

    /**
     * Returns true approximately every hundredth time
     *
     * @return
     */
    protected boolean shouldPerformFullLoad()
    {
        return Math.random() > 0.99d;
    }

    public boolean isLoggingEnabled()
    {
        return loggingEnabled;
    }

    public void setLoggingEnabled(boolean loggingEnabled)
    {
        this.loggingEnabled = loggingEnabled;
    }

    public boolean isRunning()
    {
        return running;
    }
}