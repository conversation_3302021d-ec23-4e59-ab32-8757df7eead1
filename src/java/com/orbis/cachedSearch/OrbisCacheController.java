package com.orbis.cachedSearch;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;

public interface OrbisCacheController<E> extends BeanFactoryAware
{
    /**
     * This method must set the orbisCacheRefresh inside it. After the cache
     * refresh is set to the controller, the controller and the class that will
     * be cached must be populated into the cache refresh <br>
     * <br>
     * eg. this.orbisCacheRefresh = (PCProjectCacheRefresh) beanFactory.getBean(
     * "projectCacheRefresh", PCProjectCacheRefresh.class);<br>
     * projectCacheRefresh.setController(this);<br>
     * projectCacheRefresh.setClazz(PCProject.class);<br>
     * 
     * @param beanFactory
     * @throws BeansException
     */
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException;

    /**
     * This method must refresh the cache in MODE_UPDATE <br>
     * <br>
     * eg.
     * this.projectCacheRefresh.refreshCache(PCProjectCacheRefresh.MODE_UPDATE);
     */
    public void refreshCache();

    public OrbisCacheRefresh getOrbisCacheRefresh();
}
