package com.orbis.cachedSearch;

import com.orbis.web.content.ec.promote.Promoted;
import com.orbis.web.content.ec.view.ECViewConfigSupportOrganization;
import com.orbis.web.content.ec.view.ECViewController;
import com.orbis.web.content.ec.view.card.ECViewCardSupportNPosting;
import com.orbis.web.content.np.NPostingCacheRefresh;

/**
 * Indicates that objects of this class can be {@link Promoted}. This interface
 * should be used for objects whose associated {@link OrbisCacheRefresh} is used
 * when retrieving opportunities for the {@link ECViewController experiential
 * catalogue}.
 * <p>
 * For an example of what this means, compare
 * {@link ECViewCardSupportNPosting#getCards}(uses {@link NPostingCacheRefresh})
 * to {@link ECViewConfigSupportOrganization#getCachables}(doesn't use a cache)
 * </p>
 */
public interface Cached<C extends OrbisCacheRefresh>
{
    public C getCache();
}
