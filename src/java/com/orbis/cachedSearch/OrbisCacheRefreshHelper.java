package com.orbis.cachedSearch;

import static com.orbis.portal.PortalConfig.CACHE_CONFIGURATION;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.persistence.metamodel.EntityType;
import jakarta.persistence.metamodel.Metamodel;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.ClassUtils;
import org.apache.commons.lang.reflect.FieldUtils;
import org.json.JSONException;
import org.json.JSONObject;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.web.content.ec.view.ECViewCacheRefresh;
import com.orbis.web.content.ep.EPPostingCacheRefresh;
import com.orbis.web.content.ep.occ.OCCCuratedContentCacheRefresh;
import com.orbis.web.content.ep.occ.OCCMacroMajorCacheRefresh;
import com.orbis.web.content.ep.occ.OCCPostingCacheRefresh;
import com.orbis.web.content.ev.web.globalevents.GlobalEventCacheRefresh;
import com.orbis.web.content.exp.EXPPostingCacheRefresh;
import com.orbis.web.content.kiosk.ApptKioskCacheRefresh;
import com.orbis.web.content.np.NPostingCacheRefresh;
import com.orbis.web.content.pc.PCProjectCacheRefresh;


public final class OrbisCacheRefreshHelper
{

    /**
     * Return a list of entities for a list of OrbisHqlResultSet objects.
     * Results must contain no duplicates of the same main entities.
     * 
     * @param results
     * @param clazz
     * @param depsCache
     *            Map for dependency objects (can be null)
     * @param joinMappings
     * @return
     */
    public static <T> List<T> getEntitiesForResultSets(
            List<OrbisHqlResultSet> results, Class clazz,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache,
            Map<String, String> joinMappings,
            Map<String, Map<Object, Object>> globalCache, String depName)
    {
        List<T> ret = new ArrayList<>(results.size());
        EntityType<?> cm = null;

        if (!results.isEmpty())
        {
            try
            {
                cm = PortalUtils.getHt().getSessionFactory()
                        .getMetamodel()
                        .entity(clazz);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            if (cm != null)
            {
                for (int r = 0; r < results.size(); r++)
                {
                    OrbisHqlResultSet rs = results.get(r);
                    String identifierPropertyName = cm.getId(cm.getIdType().getJavaType()).getName();
                    ret.add(getEntityForResultSet(rs, clazz, identifierPropertyName,
                            depsCache, oldDepsCache, joinMappings, globalCache,
                            depName));
                }
            }
        }
        return ret;
    }

    private static <T> T getEntityForResultSet(OrbisHqlResultSet rs, Class clazz,
            String identifierPropertyName,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache,
            Map<String, String> joinMappings,
            Map<String, Map<Object, Object>> globalCache, String depName)
    {
        T entity = null;

        boolean foundCached = false;
        String gKey = depName + clazz.getName();
        for (Map.Entry<String, Object> rse : rs.entrySet())
        {
            String key = rse.getKey();
            if (key.equals(identifierPropertyName))
            {
                Object value = rse.getValue();
                if (globalCache.containsKey(gKey)
                        && globalCache.get(gKey).containsKey(value))
                {
                    entity = (T) globalCache.get(gKey).get(value);
                    foundCached = true;
                    break;
                }
            }
        }
        if (!foundCached)
        {
            try
            {
                entity = (T) clazz.newInstance();
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        if (entity != null)
        {
            Object id = null;
            for (Map.Entry<String, Object> rse : rs.entrySet())
            {
                String key = rse.getKey();
                Object value = rse.getValue();
                if (key.equals(identifierPropertyName))
                {
                    id = value;
                }
                try
                {
                    setProperty(entity, key, value, depsCache, oldDepsCache);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }

            Map m = !globalCache.containsKey(gKey) ? new HashMap<Object, Object>()
                    : globalCache.get(gKey);
            m.put(id, entity);
            globalCache.put(gKey, m);

        }
        return entity;
    }

    /**
     * Recursively handle setting properties on newly instantiated entities and
     * adding entities to a list
     * 
     * @param entity
     * @param key
     * @param value
     * @param depsCache
     * @throws Exception
     */
    private static void setProperty(Object entity, String key, Object value,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache) throws Exception
    {
        if (value != null)
        {
            int separatorIndex = key.lastIndexOf('.');
            String propertyName = separatorIndex > -1
                    ? key.substring(separatorIndex + 1)
                    : key;
            if (value instanceof Collection)
            {
                setPropertyList(entity, key, (Collection) value, depsCache,
                        oldDepsCache, propertyName);
            }
            else
            {
                setProperty(entity, key, value, depsCache, oldDepsCache,
                        propertyName);
            }
        }
    }

    /**
     * Recursively handle setting properties on a newly instantiated entity
     * 
     * @param entity
     * @param key
     * @param value
     * @param depsCache
     * @param propertyName
     * @throws Exception
     */
    private static void setProperty(Object entity, String key, Object value,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache, String propertyName)
            throws Exception
    {
        Class propertyClass = PropertyUtils.getPropertyType(entity, propertyName);
        if (value.getClass() == OrbisHqlResultSet.class)
        {
            if (Modifier.isAbstract(propertyClass.getModifiers())
                    || Modifier.isInterface(propertyClass.getModifiers()))
            {
                value = null;
            }
            else
            {
                Object convertedValue = null;
                String identifierProperty = null;
                Map<Object, Object> propCache = null;
                Object entityId = null;
                if (depsCache != null && depsCache.containsKey(key))
                {
                    propCache = depsCache.get(key);
                    Metamodel metamodel = PortalUtils.getHt().getSessionFactory().getMetamodel();
                    EntityType<?> entityType = metamodel.entity(propertyClass);
                    identifierProperty = entityType.getId(entityType.getIdType().getJavaType()).getName();

                    entityId = ((OrbisHqlResultSet) value).get(identifierProperty);
                    convertedValue = propCache.get(entityId);
                }
                if (convertedValue == null)
                {
                    convertedValue = propertyClass.newInstance();
                    if (propCache != null && entityId != null)
                    {
                        if (oldDepsCache != null)
                        {
                            Map<Object, Object> oldPropCache = oldDepsCache
                                    .get(key);
                            if (oldPropCache.containsKey(entityId))
                            {
                                convertedValue = oldPropCache.get(entityId);
                            }
                            else
                            {
                                oldPropCache.put(entityId, convertedValue);
                            }
                        }
                        propCache.put(entityId, convertedValue);
                    }
                    for (Map.Entry<String, Object> e : ((OrbisHqlResultSet) value)
                            .entrySet())
                    {
                        setProperty(convertedValue,
                                key.concat(".").concat(e.getKey()), e.getValue(),
                                depsCache, oldDepsCache);
                    }
                }
                value = convertedValue;
            }
        }
        PropertyUtils.setProperty(entity, propertyName, value);
    }

    /**
     * Recursively handle adding entities to a list and adding a new entity from
     * a result set
     * 
     * @param entity
     * @param key
     * @param value
     * @param depsCache
     * @param propertyName
     * @throws Exception
     */
    private static void setPropertyList(Object entity, String key, Collection value,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache, String propertyName)
            throws Exception
    {
        if (!value.isEmpty())
        {
            if (value instanceof List
                    && ((List) value).get(0).getClass() == OrbisHqlResultSet.class)
            {
                setPropertyResultSet(entity, key, value, depsCache, oldDepsCache,
                        propertyName);
            }
            else
            {
                ((Collection) PropertyUtils.getProperty(entity, propertyName))
                        .addAll(value);
            }
        }
    }

    /**
     * Recursively handle instantiating and adding new entities to a list from a
     * result set
     * 
     * @param entity
     * @param key
     * @param value
     * @param depsCache
     * @param propertyName
     * @throws Exception
     */
    private static void setPropertyResultSet(Object entity, String key,
            Collection value, Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache, String propertyName)
            throws Exception
    {
        List values = (List) value;
        Field collectionField = FieldUtils.getDeclaredField(entity.getClass(),
                propertyName, true);
        if (collectionField == null)
        {
            List<Class> superclasses = ClassUtils
                    .getAllSuperclasses(entity.getClass());
            for (Class c : superclasses)
            {
                collectionField = FieldUtils.getDeclaredField(c, propertyName,
                        true);
                if (collectionField != null)
                {
                    break;
                }
            }
        }
        if (collectionField != null)
        {
            Class propertyClass = (Class) ((java.lang.reflect.ParameterizedType) collectionField
                    .getGenericType()).getActualTypeArguments()[0];
            for (int v = 0; v < values.size(); v++)
            {
                Object valueObj = propertyClass.newInstance();
                for (Entry<String, Object> e : ((OrbisHqlResultSet) values.get(v))
                        .entrySet())
                {
                    setProperty(valueObj, key.concat(".").concat(e.getKey()),
                            e.getValue(), depsCache, oldDepsCache);
                }
                values.set(v, valueObj);
            }
            ((Collection) PropertyUtils.getProperty(entity, propertyName))
                    .addAll(values);
        }
    }

    public static void mergeDependenciesIntoResults(List<OrbisHqlResultSet> results,
            OrbisCacheRefreshDependency dep,
            Map<String, Map<Object, Object>> depsCache,
            Map<String, Map<Object, Object>> oldDepsCache, int mode,
            Map<String, Map<Object, Object>> globalCache)
    {
        if (!results.isEmpty())
        {
            boolean isLoad = mode == OrbisCacheRefresh.MODE_LOAD;
            String hql = isLoad ? dep.getLoadHql() : dep.getUpdateHql();
            List<OrbisHqlResultSet> dependencyResults = PortalUtils.getHt().f(hql);
            if (!dependencyResults.isEmpty())
            {
                Map<String, String> joinMappings = dep.getJoinMappings(false);
                List<Object> dependencies;
                String joinToProperty = dep.getJoinToProperty();
                String objProperty;
                String idProperty;
                int separatorIndex = joinToProperty.lastIndexOf('.');
                objProperty = separatorIndex > -1
                        ? joinToProperty.substring(0, separatorIndex)
                        : joinToProperty;
                idProperty = separatorIndex > -1
                        ? joinToProperty.substring(separatorIndex + 1)
                        : null;

                for (OrbisCacheRefreshDependency dep2 : dep.getOuterDependencies())
                {
                    OrbisCacheRefreshHelper.mergeDependenciesIntoResults(
                            dependencyResults, dep2, null, null,
                            OrbisCacheRefresh.MODE_LOAD, globalCache);
                }

                mergeDepResults(dependencyResults, dep, "", Lists.newArrayList());
                fixDepResultsHierarchy(dependencyResults, joinMappings);

                Map<String, Map<Object, Object>> newInnerDepsCache = null;
                if (isLoad)
                {
                    if (dep.getInnerDepsCache() != null)
                    {
                        clearDependencyCache(dep.getInnerDepsCache());
                    }
                }
                else
                {
                    newInnerDepsCache = getDependencyCacheForUpdate(
                            dep.getInnerDepsCache());
                }

                dependencies = getEntitiesForResultSets(dependencyResults,
                        dep.getClazz(),
                        isLoad ? dep.getInnerDepsCache() : newInnerDepsCache,
                        isLoad ? null : dep.getInnerDepsCache(), null, globalCache,
                        dep.getPropertyName());

                for (int d = 0; d < dependencies.size(); d++)
                {
                    Object depEntity = dependencies.get(d);
                    Object curEntity = depEntity;
                    String depJoinEntityProperty = null;
                    String depJoinProperty = dep.getJoinProperty();
                    Object depId = null;
                    try
                    {
                        while ((separatorIndex = depJoinProperty.indexOf('.')) > -1)
                        {
                            depJoinEntityProperty = depJoinProperty.substring(0,
                                    separatorIndex);
                            curEntity = PropertyUtils.getProperty(curEntity,
                                    depJoinEntityProperty);
                            depJoinProperty = depJoinProperty
                                    .substring(separatorIndex + 1);
                        }
                        depId = PropertyUtils.getProperty(curEntity,
                                depJoinProperty);
                        if (depId != null)
                        {
                            String depPropertyName = dep.getPropertyName();
                            if (depsCache != null
                                    && depsCache.containsKey(depPropertyName))
                            {
                                Map<Object, Object> propCache = depsCache
                                        .get(depPropertyName);
                                if (!propCache.containsKey(depId))
                                {
                                    if (oldDepsCache != null)
                                    {
                                        Map<Object, Object> oldPropCache = oldDepsCache
                                                .get(depPropertyName);
                                        if (oldPropCache.containsKey(depId))
                                        {
                                            Object cachedDepEntity = oldPropCache
                                                    .get(depId);
                                            // Update the cached dependency
                                            // entity with the new one's values
                                            // in case
                                            // any have changed
                                            copyDependencyProperties(dep, depEntity,
                                                    cachedDepEntity);
                                            depEntity = cachedDepEntity;
                                        }
                                        else
                                        {
                                            oldPropCache.put(depId, depEntity);
                                        }
                                    }
                                    propCache.put(depId, depEntity);
                                }
                            }
                            if (depJoinEntityProperty != null)
                            {
                                // Null out the join property on the dependency
                                // entity since the dependency entity
                                // is already contained within another instance
                                // of the same entity
                                PropertyUtils.setProperty(depEntity,
                                        depJoinEntityProperty, null);
                            }
                            for (int r = 0; r < results.size(); r++)
                            {
                                OrbisHqlResultSet result = results.get(r);
                                Object resultObject = results.get(r)
                                        .select(objProperty);
                                if (idProperty == null
                                        || resultObject instanceof OrbisHqlResultSet)
                                {
                                    Object resultId = idProperty == null
                                            ? resultObject
                                            : ((OrbisHqlResultSet) resultObject)
                                                    .get(idProperty);
                                    if (depId.equals(resultId))
                                    {
                                        mergeDepIntoResult(depEntity, result, dep);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public static void copyDependencyProperties(OrbisCacheRefreshDependency dep,
            Object sourceEntity, Object destEntity) throws Exception
    {
        String[] depColumns = dep.getColumns();
        String depAlias = dep.getAlias();
        for (int c = 1; c < depColumns.length; c++)
        {
            String column = depColumns[c];
            if (column.startsWith(depAlias + "."))
            {
                boolean isDependency = column.endsWith(".id");
                String propertyName = column.substring(depAlias.length() + 1,
                        isDependency ? column.lastIndexOf('.') : column.length());
                Object newPropertyValue = PropertyUtils.getProperty(sourceEntity,
                        propertyName);
                PropertyUtils.setProperty(destEntity, propertyName,
                        newPropertyValue);
            }
            else
            {
                break;
            }
        }
    }

    private static void mergeDepResults(List<OrbisHqlResultSet> dependencyResults,
            OrbisCacheRefreshDependency dep, String propertyPrefix,
            List<String> joinProperties)
    {
        joinProperties.add(propertyPrefix.concat(dep.getJoinProperty()));
        for (OrbisCacheRefreshDependency d : dep.getInnerDependenciesCached())
        {
            String depProp = d.getPropertyName();
            if (d.isManyToOne())
            {
                OrbisHqlResultSet lastResult = null;
                Iterator<OrbisHqlResultSet> rsIterator = dependencyResults
                        .iterator();
                while (rsIterator.hasNext())
                {
                    OrbisHqlResultSet rs = rsIterator.next();
                    OrbisHqlResultSet rsMerged = rs;
                    OrbisHqlResultSet entity = (OrbisHqlResultSet) rs
                            .select(depProp);

                    boolean useExisting = true;
                    if (lastResult != null)
                    {
                        for (String joinProperty : joinProperties)
                        {
                            if (!Objects.equal(rs.select(joinProperty),
                                    lastResult.select(joinProperty)))
                            {
                                useExisting = false;
                                break;
                            }
                        }
                    }

                    if (useExisting && lastResult != null)
                    {
                        rsMerged = lastResult;
                        rsIterator.remove();
                    }
                    else
                    {
                        rs.selectPut(depProp, new ArrayList<OrbisHqlResultSet>());
                        lastResult = rsMerged = rs;
                    }
                    ((List) rsMerged.select(depProp)).add(entity);
                }
            }
            mergeDepResults(dependencyResults, d, depProp.concat("."),
                    joinProperties);
        }
    }

    private static void fixDepResultsHierarchy(
            List<OrbisHqlResultSet> dependencyResults,
            Map<String, String> joinMappings)
    {
        for (Map.Entry<String, String> e : joinMappings.entrySet())
        {
            String key = e.getKey();
            for (int r = 0; r < dependencyResults.size(); r++)
            {
                OrbisHqlResultSet result = dependencyResults.get(r);
                result.selectPut(e.getValue(), result.get(key));
                result.put(key, null);
            }
        }
    }

    private static void mergeDepIntoResult(Object d, OrbisHqlResultSet result,
            OrbisCacheRefreshDependency dep)
    {
        String property = dep.getPropertyName();
        if (dep.isManyToOne())
        {
            Collection list = (Collection) result.select(property);
            if (list == null)
            {
                list = new ArrayList();
                result.selectPut(property, new ArrayList());
            }
            ((Collection) result.select(property)).add(d);
        }
        else
        {
            int separatorIndex = property.lastIndexOf('.');
            String propertyName = property.substring(separatorIndex + 1);
            if (separatorIndex > -1)
            {
                ((OrbisHqlResultSet) result
                        .select(property.substring(0, separatorIndex)))
                                .put(propertyName, d);
            }
            else
            {
                result.put(propertyName, d);
            }
        }
    }

    /**
     * Merges the many-to-one dependency collections of an updated cache refresh
     * entity with those of the old, removed one.
     * 
     * @param newEntity
     * @param oldEntity
     * @param deps
     */
    public static void updateDependencies(Object newEntity, Object oldEntity,
            OrbisCacheRefreshDependency[] deps)
    {
        for (OrbisCacheRefreshDependency d : deps)
        {
            if (d.isManyToOne())
            {
                String depProperty = d.getJoinToProperty();
                Object newEntityDepParent = null;
                Object newEntityDep = newEntity;
                Object oldEntityDep = oldEntity;
                int separatorIndex = depProperty.indexOf('.');
                try
                {
                    do
                    {
                        String curProperty;
                        if (separatorIndex > -1)
                        {
                            curProperty = depProperty.substring(0, separatorIndex);
                            depProperty = depProperty.substring(separatorIndex + 1);
                        }
                        else
                        {
                            curProperty = depProperty = d.getPropertyName();
                        }
                        newEntityDepParent = newEntityDep;
                        newEntityDep = PropertyUtils.getProperty(newEntityDep,
                                curProperty);
                        oldEntityDep = PropertyUtils.getProperty(oldEntityDep,
                                curProperty);
                    }
                    while ((separatorIndex = depProperty.indexOf('.')) > -1);

                    if (newEntityDep instanceof Collection)
                    {
                        addOrUpdateDepEntities(newEntityDepParent,
                                (Collection) newEntityDep,
                                (Collection) oldEntityDep, depProperty, d);
                    }
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void addOrUpdateDepEntities(Object newEntityDepParent,
            Collection newEntityDep, Collection oldEntityDep, String depProperty,
            OrbisCacheRefreshDependency d) throws Exception
    {
        int separatorIndex;
        for (Object ned : newEntityDep)
        {
            Object newEntityDepId = ned;
            String depIdProperty = d.isLeftJoin() ? d.getJoinToProperty()
                    : d.getJoinProperty();
            separatorIndex = 0;
            while (separatorIndex > -1)
            {
                String curProperty;
                separatorIndex = depIdProperty.indexOf('.');
                if (separatorIndex > -1)
                {
                    curProperty = depIdProperty.substring(0, separatorIndex);
                    depIdProperty = depIdProperty.substring(separatorIndex + 1);
                }
                else
                {
                    curProperty = depIdProperty;
                }
                newEntityDepId = PropertyUtils.getProperty(newEntityDepId,
                        curProperty);
            }
            Object matchingDep = findMatchingDepEntity(oldEntityDep, newEntityDepId,
                    d);
            if (matchingDep == null || matchingDep != ned)
            {
                if (matchingDep != null)
                {
                    oldEntityDep.remove(matchingDep);
                }
                oldEntityDep.add(ned);
            }
        }
        PropertyUtils.setProperty(newEntityDepParent, depProperty, oldEntityDep);
    }

    private static Object findMatchingDepEntity(Collection oldEntityDep,
            Object newEntityDepId, OrbisCacheRefreshDependency d) throws Exception
    {
        Object ret = null;
        int separatorIndex;
        String depIdProperty;
        for (Object oed : oldEntityDep)
        {
            Object oldEntityDepId = oed;
            depIdProperty = d.isLeftJoin() ? d.getJoinToProperty()
                    : d.getJoinProperty();
            separatorIndex = 0;
            while (separatorIndex > -1)
            {
                String curProperty;
                separatorIndex = depIdProperty.indexOf('.');
                if (separatorIndex > -1)
                {
                    curProperty = depIdProperty.substring(0, separatorIndex);
                    depIdProperty = depIdProperty.substring(separatorIndex + 1);
                }
                else
                {
                    curProperty = depIdProperty;
                }
                oldEntityDepId = PropertyUtils.getProperty(oldEntityDepId,
                        curProperty);
            }
            if (oldEntityDepId.equals(newEntityDepId))
            {
                ret = oed;
                break;
            }
        }
        return ret;
    }

    public static void clearDependencyCache(
            Map<String, Map<Object, Object>> depsCache)
    {
        for (Map<Object, Object> c : depsCache.values())
        {
            c.clear();
        }
    }

    public static Map<String, Map<Object, Object>> getDependencyCacheForUpdate(
            Map<String, Map<Object, Object>> depsCache)
    {
        Map<String, Map<Object, Object>> ret = new HashMap();
        for (String key : depsCache.keySet())
        {
            ret.put(key, new HashMap());
        }
        return ret;
    }
    
    public static Map<String, Class<? extends OrbisCacheRefresh>> getCacheRefreshClassesByKey()
    {
        Map<String, Class<? extends OrbisCacheRefresh>> map = new LinkedHashMap<>();
        map.put("postingCacheRefresh", NPostingCacheRefresh.class);
        map.put("apptKioskCacheRefresh", ApptKioskCacheRefresh.class);
        map.put("globalEventCacheRefresh", GlobalEventCacheRefresh.class);
        map.put("projectCacheRefresh", PCProjectCacheRefresh.class);
        map.put("expPostingCacheRefresh", EXPPostingCacheRefresh.class);
        map.put("epPostingCacheRefresh", EPPostingCacheRefresh.class);
        map.put("ecViewCacheRefresh", ECViewCacheRefresh.class);
        map.put("occPostingCacheRefresh", OCCPostingCacheRefresh.class);
        map.put("occMacroMajorCacheRefresh", OCCMacroMajorCacheRefresh.class);
        map.put("occCuratedContentCacheRefresh",
                OCCCuratedContentCacheRefresh.class);
        return map;
    }

    public static boolean isCacheRefreshEnabled(OrbisCacheRefresh cacheRefresh)
    {
        boolean enabled = true;

        try
        {
            JSONObject cacheConfigs = new JSONObject(
                    PortalConfigHelper.getOrbisValue(CACHE_CONFIGURATION));
            JSONObject cacheConfig = cacheConfigs
                    .getJSONObject(cacheRefresh.getClass().getName());
            enabled = cacheConfig.getBoolean("enabled");
        }
        catch (JSONException | NullPointerException e)
        {
            e.printStackTrace();
        }

        return enabled;
    }

    public static void toggleCacheRefresh(OrbisCacheRefresh cache, boolean enabled)
    {
        try
        {
            JSONObject cacheConfigs = new JSONObject(
                    PortalConfigHelper.getOrbisValue(CACHE_CONFIGURATION));
            JSONObject cacheConfig = cacheConfigs
                    .getJSONObject(cache.getClass().getName());
            cacheConfig.put("enabled", enabled);
            PortalConfigHelper.savePortalConfig(CACHE_CONFIGURATION,
                    cacheConfigs.toString());
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
    }
}
