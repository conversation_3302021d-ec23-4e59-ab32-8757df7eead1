package com.orbis.analytics;

import java.util.List;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;

public class AnalyticsHelper
{
    /**
     * Initialises an ABn Campaign to be able to subscribe and track users to
     * different UIs. When used with the ui:abnCampaign tag, you can make sections
     * of html render / not render depending on which campaign users are subscribed
     * to.
     *
     * @param campaignKey
     *            - Unique key for the campaign. This should be defined as a static
     *            final String in ABnCampaign.java
     * @param description
     *            - A description of the campaign and its goal.
     * @param totalNumberOfCases
     *            - Total number of different UIs to be presented to users
     * @return - If campaignKey is found to already be in the database, it will
     *         return the existing ABnCampaign. If the campaignKey is not found, it
     *         will return a newly created ABnCampaign
     */
    public static ABnCampaign initABnCampaign(String campaignKey,
            String description, int totalNumberOfCases)
    {
        List<ABnCampaign> foundCampaigns = PortalUtils.getHt()
                .find("from ABnCampaign where campaignKey = ?", campaignKey);
        ABnCampaign campaign;

        if (foundCampaigns.size() > 0)
        {
            campaign = foundCampaigns.get(0);
        }
        else
        {
            campaign = new ABnCampaign();
            campaign.setCampaignKey(campaignKey);
            campaign.setTotalCases(totalNumberOfCases);
            campaign.setDescription(description);

            PortalUtils.getHt().save(campaign);
        }

        return campaign;
    }

    public static ABnCampaign getCampaign(String campaignKey)
    {
        List<ABnCampaign> foundCampaigns = PortalUtils.getHt()
                .find("from ABnCampaign c where c.campaignKey = ?", campaignKey);

        if (foundCampaigns.size() > 0)
        {
            return foundCampaigns.get(0);
        }

        return null;
    }

    public static Integer initUserABnCampaign(String campaignKey,
            UserDetailsImpl user)
    {
        ABnCampaign campaign = getCampaign(campaignKey);

        if (campaign == null)
        {
            return null;
        }

        return initUserABnCampaign(campaign, user);
    }

    public static Integer initUserABnCampaign(ABnCampaign campaign,
            UserDetailsImpl user)
    {
        if (campaign == null)
        {
            return null;
        }

        Integer foundSubscriptionCaseNumber = getUserCampaignCaseNumber(campaign,
                user);

        // no need to init this user since they already are subscribed to the
        // campaign. Return the case number they're part of.
        if (foundSubscriptionCaseNumber != null)
        {
            return foundSubscriptionCaseNumber;
        }

        Integer lastSavedNumber = PortalUtils.getJt().findInt(
                "select top 1 caseNumber from analytics_abn_campaign_user where campaign = ? order by id desc",
                campaign.getId());

        int userCaseNumber = 1;

        // pull the last case created and set the new user to the next case
        // number so that we can have an even amount of users per case
        if (lastSavedNumber != null)
        {
            userCaseNumber = lastSavedNumber.intValue();
            userCaseNumber++;
        }

        // If the last saved user case number is the max number that the
        // campaign can have, start from the beginning again
        if (userCaseNumber > campaign.getTotalCases())
        {
            userCaseNumber = 1;
        }

        ABnCampaignUser campaignUser = new ABnCampaignUser();
        campaignUser.setCampaign(campaign);
        campaignUser.setCaseNumber(userCaseNumber);
        campaignUser.setSubscribedUser(user);
        PortalUtils.getHt().save(campaignUser);

        return userCaseNumber;

    }

    public static Integer getUserCampaignCaseNumber(String campaignKey,
            UserDetailsImpl user)
    {
        ABnCampaign campaign = getCampaign(campaignKey);

        if (campaign == null)
        {
            return null;
        }

        return getUserCampaignCaseNumber(campaign, user);
    }

    public static Integer getUserCampaignCaseNumber(ABnCampaign campaign,
            UserDetailsImpl user)
    {
        List<Integer> foundSubscriptions = PortalUtils.getHt().find(
                "select cu.caseNumber from ABnCampaignUser cu where cu.campaign = ? and cu.subscribedUser = ?",
                campaign, user);

        if (foundSubscriptions.size() > 0)
        {
            return foundSubscriptions.get(0);
        }

        return null;
    }
}
