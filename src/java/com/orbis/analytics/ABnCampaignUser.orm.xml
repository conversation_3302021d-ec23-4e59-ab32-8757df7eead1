<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.analytics.ABnCampaignUser" cacheable="true">
        <table name="analytics_abn_campaign_user"/>
        <attributes>

            <many-to-one name="campaign" />
            <many-to-one name="subscribedUser" />

            <basic name="caseNumber">
                <column name="caseNumber" nullable="false"/>
            </basic>

        </attributes>
    </entity>
</entity-mappings>
