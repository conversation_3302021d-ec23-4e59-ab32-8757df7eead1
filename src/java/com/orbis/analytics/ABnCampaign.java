package com.orbis.analytics;

import com.orbis.web.content.ContentItem;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Getter
@Setter
public class ABnCampaign extends ContentItem
{
    public static final String KEY_TEST_CONTROLLER = "testController";

    private String campaignKey;

    @ColumnDefault("0")
    private int totalCases;

    private String description;
}
