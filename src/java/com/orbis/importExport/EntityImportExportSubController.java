package com.orbis.importExport;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.util.CellRangeAddressList;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.ReflectionActionController;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.portal.CommandQueryTemplate;
import com.orbis.spring.servlet.ByteArrayDownloadView;
import com.orbis.utils.DBUtils;
import com.orbis.utils.StringUtils;

public class EntityImportExportSubController extends ReflectionActionController
{
    private static final String GENUINE_KEY = "orbisIEExcelFile";

    private static Map<EntityImportExportInterface, EntityImportExportSubController> controllerInstances = new HashMap<EntityImportExportInterface, EntityImportExportSubController>();

    private EntityImportExportInterface entityIOInterface;

    private EntityImportExportSubController(
            EntityImportExportInterface entityIOInterface)
    {
        this.entityIOInterface = entityIOInterface;
    }

    public static EntityImportExportSubController getInstance(
            EntityImportExportInterface entityIOInterface)
    {
        EntityImportExportSubController subController = controllerInstances
                .get(entityIOInterface);

        if (subController == null)
        {
            subController = new EntityImportExportSubController(entityIOInterface);
            controllerInstances.put(entityIOInterface, subController);
        }

        return subController;
    }

    public ModelAndView processRequest(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        try
        {
            mv = invokeNamedMethod(request.getParameter("subAction"), request,
                    response);
        }
        catch (Exception e)
        {
            mv = displayError("An exception has occurred: " + e.getMessage());
            e.printStackTrace();
        }

        return mv;
    }

    public ModelAndView initExcelGenerator(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = this.entityIOInterface
                .getShortCircuitView("importExport/ie_excelGenerator");
        CommandQueryTemplate ht = this.entityIOInterface.getHt();
        try
        {
            String questionEntityName = request.getParameter("ioQuestion");
            Class<? extends EntityImportExportQuestion> questionClass = this.entityIOInterface
                    .getQuestionEntityClass(request.getParameter("ioQuestion"));
            String filterWhereClause = this.entityIOInterface
                    .filterWhereClause(questionEntityName);

            List<EntityImportExportQuestion> questions = ht.find("from "
                    + questionClass.getName() + " "
                    + this.entityIOInterface.hqlAlias(questionEntityName) + " "
                    + (StringUtils.isEmpty(filterWhereClause) ? ""
                            : StringUtils.isEmpty(filterWhereClause))
                    + " " + this.entityIOInterface.orderByHql(questionEntityName));

            mv.addObject("questions", questions);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            displayError(e.getMessage());
        }
        return mv;
    }

    public ModelAndView generateExcel(HttpServletRequest request,
            HttpServletResponse response)
    {
        CommandQueryTemplate ht = this.entityIOInterface.getHt();
        String[] qIds = request.getParameterValues("importQuestion");
        String questionEntityName = request.getParameter("ioQuestion");
        String filterWhereClause = this.entityIOInterface
                .filterWhereClause(questionEntityName);
        Class<? extends EntityImportExportQuestion> questionClass = this.entityIOInterface
                .getQuestionEntityClass(request.getParameter("ioQuestion"));

        List<EntityImportExportQuestion> questions = ht
                .find("from " + questionClass.getName() + " "
                        + this.entityIOInterface.hqlAlias(questionEntityName) + " "
                        + (StringUtils.isEmpty(filterWhereClause) ? " where "
                                : StringUtils.isEmpty(filterWhereClause) + " and ")
                        + this.entityIOInterface.hqlAlias(questionEntityName)
                        + ".id in " + DBUtils.buildInClause(qIds) + " "
                        + this.entityIOInterface.orderByHql(questionEntityName));

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("Import");
        sheet.createFreezePane(0, 1);
        CellStyle headerStyle = getHeaderStyle(workbook);

        Row row = sheet.createRow(0);
        int cellIndex = 0;
        row.setHeight((short) 512);
        for (EntityImportExportQuestion q : questions)
        {
            Cell cell = row.createCell(cellIndex);
            sheet.setColumnWidth(cellIndex, 12000);
            cell.setCellValue(new HSSFRichTextString(q.getQuestionText()));
            cell.setCellStyle(headerStyle);

            if (q.getIEType() == EntityImportExportQuestion.IE_TYPE_DROPDOWN)
            {
                createDropdownConstraint(cellIndex, q.getOptionChoices(), sheet);
            }
            else if (q.getIEType() == EntityImportExportQuestion.IE_TYPE_DATE)
            {
                createDateConstraint(cellIndex, sheet);
            }
            else if (q.getIEType() == EntityImportExportQuestion.IE_TYPE_BOOLEAN)
            {
                // createBooleanConstraint(cellIndex, sheet);
            }

            cellIndex++;
        }

        sheet = workbook.createSheet("Data");
        // This will set the sheet to VERY HIDDEN so that it can only be
        // accessed programatically
        if (StringUtils.isEmpty(request.getParameter("debugMode")))
        {
            workbook.setSheetHidden(1, 2);
        }

        row = sheet.createRow(0);
        cellIndex = 0;
        for (EntityImportExportQuestion q : questions)
        {
            Cell cell = row.createCell(cellIndex);
            cell.setCellValue(new HSSFRichTextString(q.getAnswerMapping()));
            cellIndex++;
        }

        Cell genuineCheckCell = row.createCell(cellIndex);
        genuineCheckCell.setCellValue(new HSSFRichTextString(
                EntityImportExportSubController.GENUINE_KEY));

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try
        {
            workbook.write(baos);
            baos.close();
        }
        catch (Exception e)
        {
            // e.printStackTrace();
        }

        String fileName = "foo.xlsx";

        return new ModelAndView(new ByteArrayDownloadView(fileName, baos));
    }

    private void createDropdownConstraint(int column, List<String> values,
            Sheet sheet)
    {
        CellRangeAddressList addressList = new CellRangeAddressList(1, 65535,
                column, column);

        DVConstraint dvConstraint = DVConstraint
                .createExplicitListConstraint(values.toArray(new String[] {}));
        HSSFDataValidation dataValidation = new HSSFDataValidation(addressList,
                dvConstraint);

        dataValidation.setSuppressDropDownArrow(false);
        sheet.addValidationData(dataValidation);
    }

    private void createDateConstraint(int column, Sheet sheet)
    {
        CellRangeAddressList addressList = new CellRangeAddressList(1, 65535,
                column, column);

        DVConstraint dvConstraint = DVConstraint.createDateConstraint(
                DVConstraint.OperatorType.BETWEEN, "1900-01-01 00:00:00",
                "2999-12-31 00:00:00", DBUtils.DB_DATE_TIME_FORMAT);
        HSSFDataValidation dataValidation = new HSSFDataValidation(addressList,
                dvConstraint);
        sheet.addValidationData(dataValidation);
    }

    // private void createBooleanConstraint(int column, HSSFSheet sheet)
    // {
    // CellRangeAddressList addressList = new CellRangeAddressList(1, 65535,
    // column, column);
    //
    // DVConstraint dvConstraint = DVConstraint.;
    // HSSFDataValidation dataValidation = new HSSFDataValidation(addressList,
    // dvConstraint);
    // sheet.addValidationData(dataValidation);
    // }

    private CellStyle getHeaderStyle(Workbook workbook)
    {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        headerFont.setColor(HSSFColor.WHITE.index);
        headerStyle.setFillBackgroundColor(HSSFColor.BLACK.index);
        headerStyle.setFont(headerFont);
        headerStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        headerStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        headerStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        return headerStyle;
    }

    private ModelAndView displayError(String errorMsg)
    {
        ModelAndView mv = this.entityIOInterface
                .getShortCircuitView("importExport/ie_error");
        mv.addObject("errorMsg", errorMsg);
        return mv;
    }
}
