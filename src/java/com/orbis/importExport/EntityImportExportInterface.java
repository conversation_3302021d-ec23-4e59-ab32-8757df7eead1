package com.orbis.importExport;

import com.orbis.portal.CommandQueryTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.web.OrbisController;

public abstract class EntityImportExportInterface
{
    private OrbisController controller;

    public abstract Class<? extends EntityImportExportQuestion> getQuestionEntityClass(
            String questionEntityName);

    public abstract Class<? extends EntityImportExportAnswer> getAnswerEntityClass(
            String answerEntityName);

    public EntityImportExportInterface(OrbisController controller)
    {
        this.controller = controller;
    }

    public String filterWhereClause(String questionEntityName)
    {
        return "";
    }

    public String hqlAlias(String questionEntityName)
    {
        return "x";
    }

    public String orderByHql(String questionEntityName)
    {
        return "";
    }

    public String fileName(String questionEntityName)
    {
        return questionEntityName + "_import.xlsx";
    }

    public CommandQueryTemplate getHt()
    {
        return this.controller.getHt();
    }

    public ModelAndView getShortCircuitView(String view)
    {
        return this.controller.getShortCircuitView(view);
    }
}