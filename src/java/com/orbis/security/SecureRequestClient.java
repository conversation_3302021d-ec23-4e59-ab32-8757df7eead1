package com.orbis.security;

import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
import org.apache.commons.httpclient.methods.ByteArrayRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.Validate;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.Resource;

import com.orbis.spring.servlet.SecureDispatcherServlet;

/**
 * spring bean, helps make requests that are acceptable to
 * {@link SecureDispatcherServlet}<br>
 * 
 * if this is a satellite server, this class is unusable (since the private key
 * will not be present). use {@link #isReady()} to check.
 *
 */
public class SecureRequestClient implements InitializingBean
{

    private Resource privateKeyFile;

    private PrivateKey privateKey;

    private MultiThreadedHttpConnectionManager connectionManager;

    private HttpClient httpClient;

    @Override
    public void afterPropertiesSet() throws Exception
    {
        if (privateKeyFile == null || !privateKeyFile.exists())
        {
            // this is ok, this means that this is a satellite server
        }
        else
        {
            try (InputStream is = privateKeyFile.getInputStream())
            {
                byte[] privateKeyBytes = IOUtils.toByteArray(is);

                PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(
                        privateKeyBytes);
                KeyFactory keyFactory = KeyFactory.getInstance("RSA");
                privateKey = keyFactory.generatePrivate(keySpec);
            }

            // initialize http stuff
            connectionManager = new MultiThreadedHttpConnectionManager();
            httpClient = new HttpClient(connectionManager);
        }
    }

    /**
     * the private key file to use to sign requests
     * 
     * expected algorithm is RSA, encoding of {@link PKCS8EncodedKeySpec}
     * 
     * @param privateKeyFile
     */
    public Resource getPrivateKeyFile()
    {
        return privateKeyFile;
    }

    public void setPrivateKeyFile(Resource privateKeyFile)
    {
        this.privateKeyFile = privateKeyFile;
    }

    /**
     * @return true if this bean's methods are usable, false otherwise
     */
    public boolean isReady()
    {
        return privateKey != null;
    }

    /**
     * make a secure request to the given uri with the payload
     * 
     * @param uri
     * @param payload
     * @return
     */
    public String makeRequestRaw(String uri, String payload)
    {
        Validate.isTrue(isReady(), "private key not available");

        try
        {
            return doRequest(uri, payload);
        }
        catch (InvalidKeyException | NoSuchAlgorithmException | SignatureException
                | IOException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * make a secure request to the given uri with the payload.
     * 
     * @param uri
     * @param payload
     * @return
     * @throws JSONException
     *             if there is a problem parsing the result
     */
    public JSONObject makeRequest(String uri, JSONObject payload)
            throws JSONException
    {
        return new JSONObject(makeRequestRaw(uri, payload.toString()));
    }

    private String doRequest(String uri, String payload)
            throws NoSuchAlgorithmException, InvalidKeyException,
            SignatureException, IOException, HttpException
    {
        byte[] payloadBytes = payload.getBytes(UTF_8);

        String payloadSignatureBase64 = signPayload(payloadBytes);

        PostMethod post = new PostMethod(uri);

        post.setRequestEntity(
                new ByteArrayRequestEntity(payloadBytes, "application/json"));
        post.setRequestHeader("Orbis-Signature", payloadSignatureBase64);

        String postResponse = null;
        InputStream responseBodyAsStream = null;
        try
        {
            httpClient.executeMethod(post);
            responseBodyAsStream = post.getResponseBodyAsStream();
            postResponse = new String(IOUtils.toByteArray(responseBodyAsStream),
                    ISO_8859_1);
        }
        finally
        {
            IOUtils.closeQuietly(responseBodyAsStream);
            post.releaseConnection();
        }

        return postResponse;
    }

    /**
     * sign the given payload with the private key, using SHA256withRSA
     * 
     * @param payloadBytes
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws SignatureException
     */
    private String signPayload(byte[] payloadBytes)
            throws NoSuchAlgorithmException, InvalidKeyException, SignatureException
    {
        Signature sig = Signature.getInstance("SHA256withRSA");
        sig.initSign(privateKey);
        sig.update(payloadBytes);
        byte[] sign = sig.sign();
        return Base64.getEncoder().encodeToString(sign);
    }

}
