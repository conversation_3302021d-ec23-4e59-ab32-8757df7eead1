package com.orbis.security;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import com.orbis.portal.CommandQueryTemplate;

import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.GroupsAllowed;
import com.orbis.portal.PortalLog;
import com.orbis.portal.PortalUtils;
import com.orbis.web.content.ContentItem;

public class OrbisSecurityUtils
{
    /**
     * indicates that the current user's account has been suspended.
     */
    public static class UserAccountSuspendedException extends RuntimeException
    {
        public UserAccountSuspendedException()
        {

        }
    }

    public enum UnauthorizedAccessType
    {
        Other(UserDetailsImpl.ENABLED_STATUS_REASON_UNKNOWN),
        Document(UserDetailsImpl.ENABLED_STATUS_REASON_DISABLED_DOCUMENT),
        Method(UserDetailsImpl.ENABLED_STATUS_REASON_DISABLED_GROUPS_ALLOWED),
        Entity(UserDetailsImpl.ENABLED_STATUS_REASON_DISABLED_ENTITY);

        private final int enabledStatusReason;

        public int getEnabledStatusReason()
        {
            return enabledStatusReason;
        }

        UnauthorizedAccessType(int enabledStatusReason)
        {
            this.enabledStatusReason = enabledStatusReason;
        }
    }

    /**
     * disables the given non 'Portal Staff' user.
     * 
     * @param request
     * @param user
     * @param type
     */
    public static void disableNonStaffUser(HttpServletRequest request,
            UserDetailsImpl user, UnauthorizedAccessType type)
            throws UserAccountSuspendedException
    {
        if (user == null)
            return;

        if (type == null)
            type = UnauthorizedAccessType.Other;

        if (user.isPortalStaff())
        {
            // don't disable
        }
        else
        {
            disableUser(user, type);

            Optional.ofNullable(request).map(r -> r.getSession(false))
                    .ifPresent(session -> session.invalidate());

            throw new UserAccountSuspendedException();
        }
    }

    private static void disableUser(UserDetailsImpl user,
            UnauthorizedAccessType type)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();

        user.setUserStatus(UserDetailsImpl.USER_STATUS_INACTIVE);
        user.setEnabled(false);
        user.setAccountNonExpired(false);
        user.setEnabledStatusReason(type.getEnabledStatusReason());

        ht.update(user);
    }

    /**
     * used to create a new {@link PortalLog} entity if the user fails the
     * {@link GroupsAllowed} check
     * 
     * @param user
     * @param targetClass
     * @param targetMethod
     * @param groupsAllowed
     */
    public static void logUnauthorizedMethodAccess(UserDetailsImpl user,
            Class<?> targetClass, Method targetMethod, String[] groupsAllowed)
    {
        final List<String> groupsUserIsIn = Optional.ofNullable(user)
                .map(UserDetailsImpl::getGroups).orElse(Collections.emptySet())
                .stream().map(PersonGroup::getName).sorted()
                .collect(Collectors.toList());

        JSONObject eventData = new JSONObject();

        try
        {
            eventData.put("targetClass", targetClass.getName());
            eventData.put("targetMethod", targetMethod.getName());
            eventData.put("groupsAllowed", ArrayUtils.nullToEmpty(groupsAllowed));
            eventData.put("groupsUserIsIn", groupsUserIsIn);
        }
        catch (JSONException e)
        {
            // shouldn't happen
        }

        PortalLog portalLog = new PortalLog();
        portalLog.setI18nEventType(PortalLog.PL_ACCESS_TO_METHOD_DENIED);
        portalLog.setEventData(eventData.toString());
        portalLog.setEventUser(user);

        PortalUtils.getHt().save(portalLog);
    }

    public static void logUnauthorizedAccess(UserDetailsImpl user,
            ContentItem contentItem, String eventTypeCode)
    {
        logUnauthorizedAccess(user, contentItem.getClass(), contentItem.getId(),
                eventTypeCode);
    }

    public static void logUnauthorizedAccess(UserDetailsImpl user,
            Class<?> entityClass, Integer entityId, String eventTypeCode)
    {
        PortalUtils.getHt().save(createUnauthorizedAccessAttemptLog(user,
                entityClass, entityId, eventTypeCode));
    }

    private static PortalLog createUnauthorizedAccessAttemptLog(
            UserDetailsImpl user, Class<?> entityClass, Integer entityId,
            String eventTypeCode)
    {
        JSONObject eventData = new JSONObject();

        try
        {
            eventData.put("class", entityClass.getName());
            eventData.put("id", entityId);
            eventData.put("stackTrace",
                    Arrays.toString(Thread.currentThread().getStackTrace()));
        }
        catch (JSONException e)
        {
            // shouldn't happen
        }

        PortalLog portalLog = new PortalLog();
        portalLog.setI18nEventType(
                !StringUtils.isEmpty(eventTypeCode) ? eventTypeCode
                        : PortalLog.PL_UNAUTHORIZED_ACCESS_ATTEMPT);
        portalLog.setEventData(eventData.toString());
        portalLog.setEventUser(user);

        return portalLog;
    }

    public static boolean isCaptchaSolved(HttpServletRequest request)
    {
        RecaptchaAuthentication recaptchaAuthentication = PortalUtils
                .getSpringBean("recaptchaAuthentication");
        return recaptchaAuthentication.isCaptchaSolved(
                RecaptchaAuthentication.getRecaptchaClientResponse(request));
    }
}
