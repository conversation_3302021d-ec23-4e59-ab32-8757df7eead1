package com.orbis.security;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.ConnectionTrustModifier;
import com.orbis.utils.UrlResponse;
import org.springframework.security.authentication.AuthenticationServiceException;


/**
 *
 */
public class RecaptchaAuthentication
{
    private static final String RECAPTCHA_API_SITEVERIFY = "https://www.google.com/recaptcha/api/siteverify";

    /**
     * the name of the request parameter that contains the captcha response.
     */
    private static final String G_RECAPTCHA_RESPONSE = "g-recaptcha-response";

    private final Log logger = LogFactory.getLog(RecaptchaAuthentication.class);

    /**
     * threadsafe
     *
     * @param recaptchaClientResponse
     *            the captcha response submitted by the client ( use
     *            {@link RecaptchaAuthentication#getRecaptchaClientResponse(HttpServletRequest)}
     *            )
     * @return
     */
    public boolean isCaptchaSolved(String recaptchaClientResponse)
    {
        String recaptchaSecretKey = Optional
                .ofNullable(PortalConfigHelper
                        .getPortalConfig(PortalConfig.RECAPTCHA_SECRET_KEY))
                .map(PortalConfig::getOrbisValue).orElse("");

        boolean enableRecaptchaV3 = PortalUtils.isRecaptchaV3();
        String recaptchaV3ScoreThreshold = PortalUtils
                .getRecaptchaV3ScoreThreshold();

        if (StringUtils.isEmpty(recaptchaSecretKey))
        {
            // EARLY EXIT -- recaptcha has been disabled
            return true;
        }

        if (StringUtils.isEmpty(recaptchaClientResponse))
        {
            return false;
        }

        UrlResponse response = getUrlResponse(RECAPTCHA_API_SITEVERIFY,
                recaptchaSecretKey, recaptchaClientResponse);
        String recaptchaServerResponse = response.toString();

        if (recaptchaServerResponse != null)
        {
            JSONObject resp = null;
            try
            {
                resp = new JSONObject(recaptchaServerResponse);
            }
            catch (JSONException e)
            {
                throw new AuthenticationServiceException(
                        "error parsing recaptcha response", e);
            }

            JSONArray errorCodes = resp.optJSONArray("error-codes");
            if (errorCodes != null)
            {
                logger.warn("recaptcha response error codes: " + errorCodes);
            }

            if (resp != null && resp.optBoolean("success", false))
            {
                if (enableRecaptchaV3)
                {
                    double score = resp.optDouble("score", 0.0);
                    if (score > Double.parseDouble(recaptchaV3ScoreThreshold))
                    {
                        return true;
                    }
                }
                else
                {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * extract the recaptcha client response from the given request
     *
     * @param request
     * @return
     */
    public static String getRecaptchaClientResponse(HttpServletRequest request)
    {
        return Optional.ofNullable(request)
                .map(r -> r.getParameter(G_RECAPTCHA_RESPONSE)).orElse("");
    }

    private UrlResponse getUrlResponse(String urlStr, String recaptchaSecretKey,
            String recaptchaClientResponse)
    {
        UrlResponse ret = new UrlResponse();
        StringBuffer buffer = ret.getBuffer();
        HttpURLConnection connection = null;
        BufferedReader in = null;
        try
        {
            URL url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();
            ConnectionTrustModifier.relaxHostChecking(connection);
            connection.setDoOutput(true);

            String postDataString = "secret=" + recaptchaSecretKey + "&response="
                    + recaptchaClientResponse;
            byte[] postData = postDataString.getBytes(StandardCharsets.UTF_8);
            int postDataLength = postData.length;

            connection.setRequestMethod("POST");

            connection.setRequestProperty("Content-Type",
                    "application/x-www-form-urlencoded");

            connection.setRequestProperty("Content-Length",
                    Integer.toString(postDataLength));

            try (DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream()))
            {
                wr.write(postData);
            }

            connection.setConnectTimeout(5000);
            connection.connect();

            in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));

            String decodedString;

            while ((decodedString = in.readLine()) != null)
            {
                buffer.append(decodedString);
            }

            in.close();
            ret.setStatus(UrlResponse.STATUS_SUCCESS);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            ret.setStatus(UrlResponse.STATUS_FAIL);
        }
        finally
        {
            if (in != null)
            {
                try
                {
                    in.close();
                }
                catch (IOException e)
                {
                }
            }
            if (connection != null)
            {
                connection.disconnect();
            }

        }
        return ret;
    }
}
