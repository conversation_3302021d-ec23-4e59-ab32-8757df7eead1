package com.orbis.security;

import static com.orbis.portal.PortalUtils.getUserLoggedIn;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.switchuser.SwitchUserGrantedAuthority;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;

import com.novell.ldap.util.Base64;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.lv.LastViewedHelper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.val;

public class AuthenticationUtils
{

    private static final SecurityContextRepository securityContextRepository = new HttpSessionSecurityContextRepository();

    /**
     * attempt to change the current logged in user to the user with the given
     * username and cleartextPassword
     * 
     * @param request
     * @param response
     *            TODO
     * @param username
     * @param cleartextPassword
     */
    public static void setUserLoggedIn(HttpServletRequest request,
            HttpServletResponse response, String username, String cleartextPassword)
    {
        Optional<UserDetailsImpl> found = PortalUtils.getHt().findFirst(
                "select u from UserDetailsImpl u where username=?", username);

        OrbisUserDetails userDetails = new OrbisUserDetails(found.orElse(null),
                false);

        val user = UsernamePasswordAuthenticationToken.authenticated(userDetails,
                cleartextPassword, userDetails.getAuthorities());
        user.setDetails(
                new AuthenticationDetails(true, request.getRemoteAddr(), true));
        initializeSession(request, authenticate(user));
        securityContextRepository.saveContext(SecurityContextHolder.getContext(),
                request, response);
        LocaleUtils.changeSiteLocale(request, response, request.getServletContext(),
                PortalUtils.getUserLoggedIn(request).getUserLocale());
    }

    /**
     * @param response
     *            TODO
     * @see {@link #setUserLoggedIn(HttpServletRequest, HttpServletResponse, UserDetailsImpl, boolean)}
     */
    public static void setUserLoggedIn(HttpServletRequest request,
            UserDetailsImpl user, HttpServletResponse response)
    {
        setUserLoggedIn(request, response, user, true);

    }

    /**
     * change the current logged in user to the provided user.<br>
     * 
     * WARNING: this method should not be used unless you are sure that the provided
     * user has already been properly authenticated. no authentication checks are
     * done.
     * 
     * this method also fires an AuthenticationSuccessEvent, since it bypasses some
     * logic in {@link OrbisPasswordDaoAuthenticationProvider}.
     * 
     * @param request
     * @param response
     *            TODO
     * @param user
     * @param updateLoginStatistics
     *            whether or not to update database login statistics. if unsure, set
     *            to true
     */
    public static void setUserLoggedIn(HttpServletRequest request,
            HttpServletResponse response, UserDetailsImpl user,
            boolean updateLoginStatistics)
    {
        val targetUser = new OrbisUserDetails(user, false);
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(
                targetUser, targetUser.getPassword(), targetUser.getAuthorities());
        token.setDetails(new AuthenticationDetails(true, request.getRemoteAddr(),
                updateLoginStatistics));
        Authentication authentication = authenticate(token);
        setAuthentication(request, response, authentication, user);
    }

    public static void switchUser(HttpServletRequest request, UserDetailsImpl user,
            HttpServletResponse response)
    {
        switchUser(request, response, user, true);

    }

    public static void switchUser(HttpServletRequest request,
            HttpServletResponse response, UserDetailsImpl user,
            boolean updateLoginStatistics)
    {
        Authentication current = SecurityContextHolder.getContext()
                .getAuthentication();
        Authentication sourceAuthentication = getSourceAuthentication(current,
                user);
        Authentication authentication = sourceAuthentication != null
                ? sourceAuthentication
                : buildSwitchUserAuthentication(request, user, updateLoginStatistics);
        setAuthentication(request, response, authentication, user);
    }
    
    private static void setAuthentication(HttpServletRequest request,
            HttpServletResponse response, Authentication authentication,
            UserDetailsImpl user)
    {
        initializeSession(request, authentication);

        securityContextRepository.saveContext(SecurityContextHolder.getContext(),
                request, response);
        LocaleUtils.changeSiteLocale(request, response, request.getServletContext(),
                user.getUserLocale());
        AcrmHelper.populateLinkedUsers(user);

        // authenticating with a UserDetails as principal bypasses a lot of
        // logic, so we need to fire the event manually
        ApplicationContext applicationContext = PortalUtils.getApplicationContext();

        if (applicationContext != null)
        {
            applicationContext
                    .publishEvent(new AuthenticationSuccessEvent(authentication));
        }
    }

    public static Authentication getSourceAuthentication(Authentication current,
            UserDetailsImpl user)
    {
        Authentication sourceAuthentication = getSourceAuthenticationBySwitchUserAuthority(
                current);
        if (sourceAuthentication != null
                && !((OrbisUserDetails) sourceAuthentication.getPrincipal())
                        .getOriginalUser().equals(user))
        {
            sourceAuthentication = getSourceAuthenticationBySwitchUserAuthority(
                    sourceAuthentication);
        }
        return sourceAuthentication;
    }

    public static Authentication getSourceAuthenticationBySwitchUserAuthority(
            Authentication current)
    {
        Authentication original = null;
        Collection<? extends GrantedAuthority> authorities = current
                .getAuthorities();
        for (GrantedAuthority auth : authorities)
        {
            if (auth instanceof SwitchUserGrantedAuthority source)
            {
                original = source.getSource();
            }
        }
        return original;
    }

    public static Authentication buildSwitchUserAuthentication(HttpServletRequest request,
            UserDetailsImpl user, boolean updateLoginStatistics)
    {
        UsernamePasswordAuthenticationToken targetUserRequest;

        Authentication currentAuthentication = SecurityContextHolder.getContext()
                .getAuthentication();

        GrantedAuthority switchAuthority = new SwitchUserGrantedAuthority(
                "SWITCH_USER", currentAuthentication);

        val targetUser = new OrbisUserDetails(user, false);
        Collection<? extends GrantedAuthority> orig = targetUser.getAuthorities();

        List<GrantedAuthority> newAuths = new ArrayList<>(orig);
        newAuths.add(switchAuthority);
        targetUserRequest = UsernamePasswordAuthenticationToken
                .authenticated(targetUser, targetUser.getPassword(), newAuths);
        targetUserRequest.setDetails(new AuthenticationDetails(true,
                request.getRemoteAddr(), updateLoginStatistics));
        return targetUserRequest;
    }

    /**
     * initialize the session timeout for the given request based on the currently
     * logged in user's primary group
     * 
     * @param request
     */
    public static void initializeSessionTimeout(HttpServletRequest request)
    {
        initializeSessionTimeout(request, getUserLoggedIn(request));
    }

    /**
     * initialize the session timeout for the given request based on the given user
     * 
     * @param request
     * @param user
     */
    public static void initializeSessionTimeout(HttpServletRequest request,
            UserDetailsImpl user)
    {
        Optional.ofNullable(user).map(UserDetailsImpl::getPrimaryGroup)
                .ifPresent(group -> {
                    int timeoutSeconds = group.getTimeoutMinutes() * 60 - 1;

                    request.getSession().setMaxInactiveInterval(timeoutSeconds);
                    request.getSession().setAttribute("orbisTimeout",
                            timeoutSeconds);
                });
    }

    /**
     * authenticate the given token based on the configured authenticationManager
     * bean
     * 
     * @param token
     * @return
     */
    private static Authentication authenticate(
            UsernamePasswordAuthenticationToken token)
    {
        AuthenticationManager authenticationManager = PortalUtils
                .getSpringBean("noPasswordCheckAuthenticationManager");

        return authenticationManager.authenticate(token);
    }

    /**
     * initialize required session variables based on the current authentication
     * 
     * @param request
     * @param authentication
     */
    private static void initializeSession(HttpServletRequest request,
            Authentication authentication)
    {
        String redirectAfterLoginURL = Optional
                .ofNullable(request.getSession().getAttribute(
                        RequestUtils.OVERRIDE_REDIRECT_AFTER_LOGIN_KEY))
                .map(s -> (String) s).orElse(null);
        request.getSession().invalidate();

        HttpSession session = request.getSession();

        OrbisUserDetails user = (OrbisUserDetails) authentication.getPrincipal();

        session.setAttribute(
                UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY,
                user.getUsername());
        session.setAttribute(PortalUtils.ORBIS_SECURITY_USER_ID,
                user.getUsername());
        session.removeAttribute(LastViewedHelper.SESSION_KEY);

        if (redirectAfterLoginURL != null)
        {
            request.getSession().setAttribute(
                    RequestUtils.OVERRIDE_REDIRECT_AFTER_LOGIN_KEY,
                    redirectAfterLoginURL);
        }
        SecurityContext context = SecurityContextHolder.getContext();

        context.setAuthentication(authentication);

        initializeSessionTimeout(request);
    }

    public static String generateBasicAuth(String username, String password)
    {
        return "Basic " + Base64.encode(username + ":" + password);
    }
}
