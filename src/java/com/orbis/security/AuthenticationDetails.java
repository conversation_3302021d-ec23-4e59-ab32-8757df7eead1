package com.orbis.security;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.security.core.Authentication;

/**
 * used in {@link Authentication#getDetails()}
 */
public class AuthenticationDetails
{
    private final boolean captchaSolved;

    private final String remoteAddress;

    private final boolean updateLoginStatistics;

    /**
     * @param captchaSolved
     *            if unsure, set to true
     * @param remoteAddress
     *            {@link HttpServletRequest#getRemoteAddr()}
     * @param updateLoginStatistics
     *            if true, update user login statistics. if unsure, set to true
     */
    public AuthenticationDetails(boolean captchaSolved, String remoteAddress,
            boolean updateLoginStatistics)
    {
        this.captchaSolved = captchaSolved;
        this.remoteAddress = remoteAddress;
        this.updateLoginStatistics = updateLoginStatistics;
    }

    public boolean isCaptchaSolved()
    {
        return captchaSolved;
    }

    public String getRemoteAddress()
    {
        return remoteAddress;
    }

    public boolean isUpdateLoginStatistics()
    {
        return updateLoginStatistics;
    }
}