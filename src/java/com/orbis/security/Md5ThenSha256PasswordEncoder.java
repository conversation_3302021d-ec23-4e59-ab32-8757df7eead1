package com.orbis.security;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Hex;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * performs md5 on the given password then sha256 on the result of that
 *
 */
public class Md5ThenSha256PasswordEncoder implements PasswordEncoder
{

    private static byte[] doMd5ThenSha256(final String text)
    {
        final MessageDigest md5Digest;
        final MessageDigest sha256Digest;
        try
        {
            md5Digest = MessageDigest.getInstance("MD5");
            sha256Digest = MessageDigest.getInstance("SHA-256");
        }
        catch (NoSuchAlgorithmException e)
        {
            throw new RuntimeException(e);
        }

        final byte[] textBytes = text.getBytes(StandardCharsets.UTF_8);
        return sha256Digest.digest(md5Digest.digest(textBytes));
    }

    @Override
    public String encode(CharSequence rawPassword)
    {
        final byte[] doTwoStepHash = doMd5ThenSha256(rawPassword.toString());
        return String.valueOf(Hex.encodeHex(doTwoStepHash));
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword)
    {
        String pass2 = encode(rawPassword);
        return encodedPassword.equals(pass2);
    }
}