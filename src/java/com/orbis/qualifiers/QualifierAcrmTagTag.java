package com.orbis.qualifiers;

import com.orbis.web.content.ContentItem;
import com.orbis.web.content.acrm.AcrmTag;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class QualifierAcrmTagTag extends ContentItem
{
    private static final long serialVersionUID = 6121506394207918815L;

    private QualifierAcrmTagCategory qualifierTagCategory;

    private AcrmTag tag;

    public QualifierAcrmTagCategory getQualifierTagCategory()
    {
        return qualifierTagCategory;
    }

    public void setQualifierTagCategory(
            QualifierAcrmTagCategory qualifierTagCategory)
    {
        this.qualifierTagCategory = qualifierTagCategory;
    }

    public AcrmTag getTag()
    {
        return tag;
    }

    public void setTag(AcrmTag tag)
    {
        this.tag = tag;
    }
}
