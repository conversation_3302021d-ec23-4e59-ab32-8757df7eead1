<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.qualifiers.Qualifier" name="Qualifier">
        <table name="qualifier"/>
        <inheritance strategy="SINGLE_TABLE"/>
        <discriminator-value>com.orbis.qualifiers.Qualifier</discriminator-value>
        <discriminator-column name="qualifierType"/>

        <attributes>


            <basic name="name"/>

        </attributes>
    </entity>
    <entity class="com.orbis.qualifiers.QualifierProperty">

        <discriminator-value>com.orbis.qualifiers.QualifierProperty</discriminator-value>
        <attributes>
            <basic name="property"/>
            <basic name="value"/>
            <basic name="operation"/>
        </attributes>
    </entity>
    <entity class="com.orbis.qualifiers.QualifierAcrmTag">

        <discriminator-value>com.orbis.qualifiers.QualifierAcrmTag</discriminator-value>
        <attributes>
        </attributes>
    </entity>
</entity-mappings>