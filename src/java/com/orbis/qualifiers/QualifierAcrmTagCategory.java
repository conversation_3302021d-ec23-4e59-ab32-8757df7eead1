package com.orbis.qualifiers;

import org.hibernate.annotations.ColumnDefault;

import com.orbis.web.content.ContentItem;
import com.orbis.web.content.acrm.AcrmRegModuleTagCategory;

public class QualifierAcrmTagCategory extends ContentItem
{
    private static final long serialVersionUID = -6583876741569283490L;

    private QualifierAcrmTag qualifier;

    private AcrmRegModuleTagCategory category;

    @ColumnDefault("0")
    private double gpa;

    public QualifierAcrmTag getQualifier()
    {
        return qualifier;
    }

    public void setQualifier(QualifierAcrmTag qualifier)
    {
        this.qualifier = qualifier;
    }

    public AcrmRegModuleTagCategory getCategory()
    {
        return category;
    }

    public void setCategory(AcrmRegModuleTagCategory category)
    {
        this.category = category;
    }

    public double getGpa()
    {
        return gpa;
    }

    public void setGpa(double gpa)
    {
        this.gpa = gpa;
    }

}
