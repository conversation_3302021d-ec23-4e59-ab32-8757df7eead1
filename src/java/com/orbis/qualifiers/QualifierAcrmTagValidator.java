package com.orbis.qualifiers;

import java.util.Iterator;
import java.util.List;

import com.orbis.portal.CommandQueryTemplate;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;

public class QualifierAcrmTagValidator implements QualifierValidator
{
    @Override
    public boolean validate(UserDetailsImpl user, Qualifier qualifier)
    {
        boolean qualifies = false;
        CommandQueryTemplate ht = PortalUtils.getHt();

        QualifierAcrmTag q = (QualifierAcrmTag) qualifier;
        List<Object[]> qatcs = PortalUtils.getHt().find(
                "select c.id, c.gpa from QualifierAcrmTagCategory c where c.qualifier=?",
                q);
        for (Object[] qc : qatcs)
        {
            final boolean hasTag = (Integer) ht.find(
                    "select count(aut) from AcrmUserTag aut where aut.user=? and aut.tag.id in "
                            + "(select att.tag.id from QualifierAcrmTagTag att where att.qualifierTagCategory.id=?)",
                    new Object[] { user, qc[0] }).get(0) > 0;
            final boolean gpaMatchesRequirement = user.getGpa() >= (Double) qc[1];

            if (PortalUtils
                    .isSiteCode(PortalUtils.NORTHEASTERN_UNIVERSITY_SITE_CODE))
            {
                // special case, treats zero gpa as free pass
                qualifies |= hasTag
                        && (gpaMatchesRequirement || user.getGpa() == 0d);
            }
            else
            {
                qualifies |= hasTag && gpaMatchesRequirement;
            }
        }

        return qualifies;
    }

    @Override
    public String getWhereClause(UserDetailsImpl user, Qualifier qualifier,
            String hqlAlias)
    {
        QualifierAcrmTag q = (QualifierAcrmTag) qualifier;
        StringBuilder sb = new StringBuilder(" ");

        List<Object[]> qatcs = PortalUtils.getHt().find(
                "select c.id, c.gpa from QualifierAcrmTagCategory c where c.qualifier=?",
                q);
        for (Iterator iterator = qatcs.iterator(); iterator.hasNext();)
        {
            Object[] qatc = (Object[]) iterator.next();
            final Integer qualifierId = (Integer) qatc[0];
            final Double qualifierGpa = (Double) qatc[1];
            sb.append(" ( ");
            if (PortalUtils
                    .isSiteCode(PortalUtils.NORTHEASTERN_UNIVERSITY_SITE_CODE))
            {
                // special case, treats zero gpa as free pass
                sb.append(String.format(" ( %s.gpa >= %s or %s.gpa = 0 ) ",
                        hqlAlias, qualifierGpa, hqlAlias));
            }
            else
            {
                sb.append(String.format(" ( %s.gpa >= %s ) ", hqlAlias,
                        qualifierGpa));
            }
            sb.append(" and ");
            sb.append(String.format(
                    " ( %s.id in ( select aut.user.id from AcrmUserTag aut where aut.tag.id in ( select att.tag.id from QualifierAcrmTagTag att where att.qualifierTagCategory.id = %s ) ) ) ",
                    hqlAlias, qualifierId));
            sb.append(" ) ");
            sb.append(iterator.hasNext() ? " or " : "");
        }

        return sb.toString();
    }

}
