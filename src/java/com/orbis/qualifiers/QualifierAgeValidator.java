package com.orbis.qualifiers;

import java.util.Date;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.utils.DateUtils;
import com.orbis.utils.StringUtils;

public class QualifierAgeValidator implements QualifierValidator
{
    @Override
    public boolean validate(UserDetailsImpl user, Qualifier qualifier)
    {
        QualifierAge q = (QualifierAge) qualifier;
        return DateUtils.getYear(DateUtils.subtractYears(new Date(),
                Integer.valueOf(q.getAge()))) == DateUtils.getYear(user.getDob());
    }

    @Override
    public String getWhereClause(UserDetailsImpl user, Qualifier qualifier,
            String hqlAlias)
    {
        QualifierAge q = (QualifierAge) qualifier;
        StringBuilder sb = new StringBuilder(" ");

        if (StringUtils.isInteger(q.getAge()))
        {
            Integer ageYear = DateUtils.getYear(
                    DateUtils.subtractYears(new Date(), Integer.valueOf(q.getAge())));
            sb.append("YEAR(").append(hqlAlias).append(".dob) = ").append(ageYear)
                    .append(" ");
        }

        return sb.toString();
    }
}
