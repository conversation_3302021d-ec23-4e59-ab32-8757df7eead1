package com.orbis.qualifiers;

import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Transient;

@Access(value = AccessType.FIELD)
public class Qualifier extends ContentItem
{
    private static final long serialVersionUID = 3678491755210651915L;

    private String name;

    @Transient
    private Class validator;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public Class getValidator()
    {
        return validator;
    }

    public void setValidator(Class validator)
    {
        this.validator = validator;
    }
}
