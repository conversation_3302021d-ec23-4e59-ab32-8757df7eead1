package com.orbis.qualifiers;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class QualifierProperty extends Qualifier
{
    public final static int OPERATION_EQUAL = 0;

    public final static int OPERATION_NOT_EQUAL = 1;

    public final static int OPERATION_GREATER_THAN = 2;

    public final static int OPERATION_LESS_THAN = 3;

    public final static int OPERATION_GREATER_THAN_EQUAL = 4;

    public final static int OPERATION_LESS_THAN_EQUAL = 5;

    private String property;

    private String value;

    private int operation;

    public String getProperty()
    {
        return property;
    }

    public void setProperty(String property)
    {
        this.property = property;
    }

    public int getOperation()
    {
        return operation;
    }

    public void setOperation(int operation)
    {
        this.operation = operation;
    }

    public Class getValidator()
    {
        return QualifierPropertyValidator.class;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }
}
