<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.qualifiers.QualifierAcrmTagTag" name="QualifierAcrmTagTag">
        <table name="qualifier_acrm_tag_tag"/>
        <attributes>


            <many-to-one name="qualifierTagCategory"/>
            <many-to-one name="tag">
                <join-column name="tagg"/>
            </many-to-one>

        </attributes>
    </entity>
</entity-mappings>