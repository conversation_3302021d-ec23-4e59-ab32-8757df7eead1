<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.qualifiers.QualifierAcrmTagCategory" name="QualifierAcrmTagCategory">
        <table name="qualifier_acrm_tag_category"/>
        <attributes>


            <many-to-one name="qualifier"/>
            <many-to-one name="category">
                <join-column name="tagCategoryy"/>
            </many-to-one>

            <basic name="gpa">
                <column name="gpa" nullable="false" />
            </basic>

        </attributes>
    </entity>
</entity-mappings>