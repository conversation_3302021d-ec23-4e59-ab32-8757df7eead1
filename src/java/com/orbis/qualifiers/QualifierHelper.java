package com.orbis.qualifiers;

import java.util.List;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;

public class QualifierHelper
{
    private QualifierHelper()
    {
    }

    public static boolean userQualifies(List<Qualifier> qualifiers,
            UserDetailsImpl user)
    {
        boolean qualifies = true;

        try
        {
            for (Qualifier qualifier : qualifiers)
            {
                qualifies &= ((QualifierValidator) qualifier.getValidator()
                        .newInstance()).validate(user, qualifier);
            }
        }
        catch (Exception e)
        {
        }

        return qualifies;
    }
}
