package com.orbis.qualifiers;

import java.util.Arrays;
import java.util.List;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.utils.DBUtils;

public class QualifierPropertyValidator implements QualifierValidator
{
    @Override
    public boolean validate(UserDetailsImpl user, Qualifier qualifier)
    {
        boolean qualifies = false;

        QualifierProperty q = (QualifierProperty) qualifier;

        if ("Citizenship Status".equals(q.getName()))
        {
            List l = Arrays.asList(q.getValue().split("\\|"));
            qualifies = l.contains(user.getCitizenStatus());
        }

        return qualifies;
    }

    @Override
    public String getWhereClause(UserDetailsImpl user, Qualifier qualifier,
            String hqlAlias)
    {
        QualifierProperty q = (QualifierProperty) qualifier;
        StringBuilder sb = new StringBuilder(" ");
        if ("Citizenship Status".equals(q.getName()))
        {
            String[] l = q.getValue().split("\\|");
            sb.append(hqlAlias + "." + q.getProperty()).append(" in ")
                    .append(DBUtils.buildInClauseWithQuotes(l));
        }
        else
        {

            if (!"au.gpa".equals(q.getProperty()))
            {
                sb.append(hqlAlias + "." + q.getProperty());
            }
            else
            {
                sb.append(q.getProperty());
            }
            if (q.getOperation() == QualifierProperty.OPERATION_EQUAL)
                sb.append(" = '").append(q.getValue()).append("' ");
            else if (q.getOperation() == QualifierProperty.OPERATION_NOT_EQUAL)
                sb.append(" <> '").append(q.getValue()).append("' ");
            else if (q.getOperation() == QualifierProperty.OPERATION_GREATER_THAN)
                sb.append(" > ").append(q.getValue()).append(" ");
            else if (q
                    .getOperation() == QualifierProperty.OPERATION_GREATER_THAN_EQUAL)
                sb.append(" >= ").append(q.getValue()).append(" ");
            else if (q.getOperation() == QualifierProperty.OPERATION_LESS_THAN)
                sb.append(" < ").append(q.getValue()).append(" ");
            else if (q
                    .getOperation() == QualifierProperty.OPERATION_LESS_THAN_EQUAL)
                sb.append(" <= ").append(q.getValue()).append(" ");
        }

        if (user != null)
        {
            sb.append(" and ").append(hqlAlias).append(".id=").append(user.getId());
        }

        return sb.toString();
    }
}
