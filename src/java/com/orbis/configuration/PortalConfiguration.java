package com.orbis.configuration;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.MapFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import com.orbis.acegi.acl.basic.hibernate.PermissionManager;
import com.orbis.configuration.common.ValidationUtils;
import com.orbis.portal.CkLicenseManager;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.OrbisJdbcTemplate;
import com.orbis.portal.PortalUtils;
import com.orbis.web.site.DelegatedRestrictedSiteManager;
import com.orbis.web.site.SiteManager;
import com.orbis.web.site.SiteManagerImpl;

import lombok.SneakyThrows;
import lombok.val;

@Configuration
public class PortalConfiguration
{

    public PortalConfiguration(@Value("${ckfinder.license-key:}")
    String ckFinderLicense, @Value("${ckeditor.license-key:}")
    String ckEditorLicense)
    {
        CkLicenseManager.setCkFinderLicense(ckFinderLicense);
        CkLicenseManager.setCkEditorLicense(ckEditorLicense);
    }

    @Bean
    @DependsOn({"justForwardDomainEventPublisher", "aclRecipientCreatedListener"})
    public PortalUtils portalUtils(DataSource dataSource,
            CommandQueryTemplate hibernateTemplate, @Autowired(required = false)
            @Qualifier("readOnlyHibernateTemplate")
            CommandQueryTemplate readOnlyHibernateTemplate,
            JdbcTemplate jdbcTemplate, @Autowired(required = false)
            @Qualifier("readOnlyJdbcTemplate")
            OrbisJdbcTemplate readOnlyJdbcTemplate,
            PlatformTransactionManager transactionManager,
            PermissionEvaluator permissionEvaluator)
    {
        val portalUtils = new PortalUtils();
        portalUtils.setDataSource(dataSource);
        portalUtils.setHibernateTemplate(hibernateTemplate);
        portalUtils.setHtReadOnly(readOnlyHibernateTemplate);
        portalUtils.setJdbcTemplate(jdbcTemplate);
        portalUtils.setJtReadOnly(readOnlyJdbcTemplate);
        portalUtils.setTransactionManager(transactionManager);
        portalUtils.setPermissionEvaluator(permissionEvaluator);
        return portalUtils;
    }

    @Bean
    public ValidationUtils validationUtils(
            LocalValidatorFactoryBean localValidatorFactoryBean)
    {
        return new ValidationUtils(localValidatorFactoryBean.getValidator());
    }

    @Bean
    public LocalValidatorFactoryBean localValidatorFactoryBean()
    {
        return new LocalValidatorFactoryBean();
    }
}
