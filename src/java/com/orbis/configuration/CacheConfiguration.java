package com.orbis.configuration;

import java.io.IOException;

import javax.cache.CacheManager;
import javax.cache.Caching;
import javax.cache.spi.CachingProvider;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;

@Configuration
@EnableCaching
public class CacheConfiguration
{

    @Bean
    @Primary
    public org.springframework.cache.CacheManager cacheManager() throws IOException
    {
        return new JCacheCacheManager(springCacheManager());
    }

    @Bean
    public CacheManager springCacheManager() throws IOException
    {
        CachingProvider cachingProvider = Caching
                .getCachingProvider("org.ehcache.jsr107.EhcacheCachingProvider");
        return cachingProvider.getCacheManager(
                new ClassPathResource("spring-ehcache.xml",
                        this.getClass().getClassLoader()).getURI(),
                getClass().getClassLoader());
    }
}
