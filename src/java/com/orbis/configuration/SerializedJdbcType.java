package com.orbis.configuration;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Objects;

import org.apache.commons.lang3.SerializationUtils;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.UserType;

public class SerializedJdbcType implements UserType<Serializable>
{
    @Override
    public int getSqlType()
    {
        return Types.VARBINARY;
    }

    @Override
    public Class<Serializable> returnedClass()
    {
        return Serializable.class;
    }

    @Override
    public boolean equals(Serializable x, Serializable y)
    {
        return Objects.equals(x, y);
    }

    @Override
    public int hashCode(Serializable x)
    {
        return x.hashCode();
    }

    @Override
    public Serializable nullSafeGet(ResultSet rs, int position,
            SharedSessionContractImplementor session, Object owner)
            throws SQLException
    {
        byte[] bytes = rs.getBytes(position);
        return bytes != null ? SerializationUtils.deserialize(rs.getBytes(position))
                : null;
    }

    @Override
    public void nullSafeSet(PreparedStatement st, Serializable value, int index,
            SharedSessionContractImplementor session) throws SQLException
    {
        if (value != null)
        {
            st.setBytes(index, SerializationUtils.serialize(value));
        }
        else
        {
            st.setNull(index, getSqlType());
        }
    }

    @Override
    public Serializable deepCopy(Serializable value)
    {
        return value;
    }

    @Override
    public boolean isMutable()
    {
        return false;
    }

    @Override
    public Serializable disassemble(Serializable value)
    {
        return value;
    }

    @Override
    public Serializable assemble(Serializable cached, Object owner)
    {
        return cached;
    }
}
