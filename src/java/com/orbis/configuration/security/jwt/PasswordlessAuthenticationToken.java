package com.orbis.configuration.security.jwt;

import java.util.Collections;
import java.util.Map;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;

import lombok.Getter;

@Getter
public class PasswordlessAuthenticationToken extends AbstractAuthenticationToken implements Attributable
{

    private Object principal;

    private final Object credentials;

    private Map<String, Object> attributes;

    public PasswordlessAuthenticationToken(String token)
    {
        super(null);
        this.credentials = token;
    }

    public PasswordlessAuthenticationToken(UserDetails principal,
            Object credentials, Map<String, Object> attr)
    {
        super(principal.getAuthorities());
        this.principal = principal;
        this.credentials = credentials;
        setAuthenticated(true);
        attributes = Collections.unmodifiableMap(attr);
    }

}
