package com.orbis.configuration.security.jwt;

import java.net.URI;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.nimbusds.jose.JOSEObjectType;
import org.springframework.stereotype.Service;

import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.jwk.source.JWKSourceBuilder;
import com.nimbusds.jose.proc.DefaultJOSEObjectTypeVerifier;
import org.apache.commons.lang3.StringUtils;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.JWSVerificationKeySelector;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimNames;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.jwt.proc.BadJWTException;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class TokenVerificationServiceImpl implements TokenVerificationService
{

    public static final String ACTOR_ATTRIBUTE = "act";

    public static final String JWKS_PATH = "/v2.0/keys";


    @Override
    public Map<String, Object> verify(String token) throws Exception
    {
        SignedJWT signedJWT = SignedJWT.parse(token);
        if (signedJWT.getHeader().getKeyID() == null)
        {
            throw new BadJWTException("Missing key ID");
        }
        JWTClaimsSet unverifiedClaims = signedJWT.getJWTClaimsSet();
        String issuer = unverifiedClaims.getIssuer();

        List<String> issuers = Arrays.stream(
                PortalConfigHelper.getPortalConfig(PortalConfig.JWT_LOGIN_ISSUERS)
                        .getOrbisValue().split(","))
                .toList();

        if (!issuers.contains(issuer))
        {
            throw new BadJWTException("Invalid issuer");
        }

        String jwksUrl = issuer + JWKS_PATH;
        JWKSource<SecurityContext> keySource = JWKSourceBuilder
                .create(new URI(jwksUrl).toURL()).retrying(true).build();

        ConfigurableJWTProcessor<SecurityContext> jwtProcessor = new DefaultJWTProcessor<>();
        jwtProcessor.setJWSTypeVerifier(new DefaultJOSEObjectTypeVerifier<>());

        JWSKeySelector<SecurityContext> keySelector = new JWSVerificationKeySelector<>(
                signedJWT.getHeader().getAlgorithm(), keySource);
        jwtProcessor.setJWSKeySelector(keySelector);

        List<String> requiredClaims = Arrays.asList(JWTClaimNames.SUBJECT,
                JWTClaimNames.ISSUED_AT, JWTClaimNames.NOT_BEFORE,
                JWTClaimNames.EXPIRATION_TIME, JWTClaimNames.AUDIENCE,
                ACTOR_ATTRIBUTE);

        final String AUDIENCE = "https://"
                + PortalConfigHelper.getPortalConfig("FQDN").getOrbisValue();

        jwtProcessor.setJWTClaimsSetVerifier(new DefaultJWTClaimsVerifier<>(
                new JWTClaimsSet.Builder().audience(AUDIENCE).build(),
                new HashSet<>(requiredClaims)));
        jwtProcessor.setJWSTypeVerifier(
                new DefaultJOSEObjectTypeVerifier<>(JOSEObjectType.JWT));
        JWTClaimsSet verifiedClaims = jwtProcessor.process(token, null);

        Map<String, Object> actor = verifiedClaims.getJSONObjectClaim(ACTOR_ATTRIBUTE);
        if (actor == null)
        {
            throw new BadJWTException("Invalid actor");
        }
        String actorSubject = (String) actor.get(JWTClaimNames.SUBJECT);
        if (StringUtils.isBlank(actorSubject) || !actorSubject.matches(".+@.+"))
        {
            throw new BadJWTException("Invalid actor subject");
        }
        String actorName = (String) actor.get("name");
        if (StringUtils.isBlank(actorName))
        {
            throw new BadJWTException("Invalid actor name");
        }

        if (verifiedClaims.getIssueTime() != null
                && verifiedClaims.getIssueTime().after(new java.util.Date()))
        {
            throw new BadJWTException("Invalid issued-at");
        }

        return verifiedClaims.getClaims();
    }
}
