package com.orbis.configuration.security.jwt;

import java.util.Map;

import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.Assert;

import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.val;
import lombok.extern.apachecommons.CommonsLog;

@CommonsLog
@RequiredArgsConstructor
public class PasswordlessAuthenticationProvider implements AuthenticationProvider
{

    @Setter
    private String usernameAttribute = "sub";

    private final UserDetailsService userDetailsService;

    private final TokenVerificationService tokenVerificationService;

    @Setter
    private UserDetailsChecker preAuthenticationChecks = new AccountStatusUserDetailsChecker();

    protected MessageSourceAccessor messages = SpringSecurityMessageSource
            .getAccessor();

    private Map<String, Object> getAttributes(
            PasswordlessAuthenticationToken authentication)
    {
        String token = (String) authentication.getCredentials();
        if (token == null)
        {
            throw new BadCredentialsException("Token is missed");
        }
        try
        {
            return tokenVerificationService.verify(token);
        }
        catch (Exception e)
        {
            throw new BadCredentialsException(e.getMessage());
        }
    }

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException
    {
        Assert.isInstanceOf(PasswordlessAuthenticationToken.class, authentication,
                () -> this.messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.onlySupports",
                        "Only PasswordlessAuthenticationToken is supported"));
        val auth = (PasswordlessAuthenticationToken) authentication;
        val attributes = getAttributes(auth);
        val username = (String) attributes.get(usernameAttribute);
        UserDetails user;
        try
        {
            user = userDetailsService.loadUserByUsername(username);
        }
        catch (UsernameNotFoundException ex)
        {
            log.debug("Failed to find user '" + username + "'");
            throw new BadCredentialsException(this.messages.getMessage(
                    "AbstractUserDetailsAuthenticationProvider.badCredentials",
                    "Bad credentials"));
        }
        Assert.notNull(user,
                "retrieveUser returned null - a violation of the interface contract");

        this.preAuthenticationChecks.check(user);

        return new PasswordlessAuthenticationToken(user,
                authentication.getCredentials(), attributes);
    }

    @Override
    public boolean supports(Class<?> authentication)
    {
        return PasswordlessAuthenticationToken.class
                .isAssignableFrom(authentication);
    }

}
