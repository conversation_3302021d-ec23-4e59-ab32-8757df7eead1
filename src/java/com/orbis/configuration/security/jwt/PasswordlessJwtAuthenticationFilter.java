package com.orbis.configuration.security.jwt;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.val;

@Setter
public class PasswordlessJwtAuthenticationFilter
        extends AbstractAuthenticationProcessingFilter
{

    private boolean postOnly = true;

    public PasswordlessJwtAuthenticationFilter(String defaultFilterProcessesUrl)
    {
        super(defaultFilterProcessesUrl);
    }

    @Override
    @SneakyThrows
    public Authentication attemptAuthentication(HttpServletRequest request,
            HttpServletResponse response)
    {
        if (this.postOnly && !request.getMethod().equals("POST"))
        {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }

        val header = request.getHeader("Authorization");
        String authToken = null;
        String contentType = request.getContentType();
        if (header != null && StringUtils.startsWith(header, "Bearer "))
        {
            authToken = header.substring("Bearer ".length());
        }
        else if (contentType != null
                && contentType.contains("application/x-www-form-urlencoded"))
        {
            authToken = request.getParameter("token");
        }
        return this.getAuthenticationManager()
                .authenticate(new PasswordlessAuthenticationToken(authToken));
    }

}
