package com.orbis.configuration.security.acl.listeners;

import java.util.List;

import org.springframework.context.ApplicationListener;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.orbis.acegi.providers.dao.hibernate.events.AclRecipientDeleted;
import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.configuration.security.acl.repository.SiteElementAclRepository;
import com.orbis.configuration.security.acl.service.AclServiceUtils;
import com.orbis.web.site.SiteElement;

import lombok.RequiredArgsConstructor;

/**
 * Listener for {@link AclRecipientDeleted} event. Removes all permissions for
 * the recipient.
 */
@Component
@RequiredArgsConstructor
public class AclRecipientDeletedListener
        implements ApplicationListener<AclRecipientDeleted>
{
    private final AclServiceUtils aclServiceUtils;

    private final SiteElementAclRepository siteElementAclRepository;

    @Override
    @Transactional
    public void onApplicationEvent(AclRecipientDeleted event)
    {
        AclSid sid = (AclSid) event.getRecipient().toSid();
        List<SiteElement> elementsList = siteElementAclRepository
                .getElementsList(sid);
        for (SiteElement siteElement : elementsList)
        {
            aclServiceUtils
                    .deleteSidPermissions(new ObjectIdentityImpl(siteElement), sid);
        }
        aclServiceUtils.deleteAclSid(sid);
    }
}
