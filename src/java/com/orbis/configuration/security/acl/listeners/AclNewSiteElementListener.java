package com.orbis.configuration.security.acl.listeners;

import org.springframework.context.ApplicationListener;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.security.acls.jdbc.JdbcMutableAclService;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.MutableAcl;
import org.springframework.security.acls.model.ObjectIdentity;
import org.springframework.stereotype.Component;

import com.orbis.web.site.event.SiteElementAddedEvent;

/**
 * Listener for new site element creation. Creates new ACL for the site element.
 */
@Component
public class AclNewSiteElementListener
        implements ApplicationListener<SiteElementAddedEvent>
{

    private final JdbcMutableAclService aclService;

    public AclNewSiteElementListener(JdbcMutableAclService aclService)
    {
        this.aclService = aclService;
    }

    @Override
    public void onApplicationEvent(SiteElementAddedEvent event)
    {
        ObjectIdentityImpl parent = new ObjectIdentityImpl(
                event.getSiteElement().getParent());
        Acl parrentAcl = aclService.readAclById(parent);
        ObjectIdentity child = new ObjectIdentityImpl(event.getSiteElement());
        MutableAcl childAcl = aclService.createAcl(child);
        if (parrentAcl != null)
        {
            childAcl.setParent(parrentAcl);
            aclService.updateAcl(childAcl);
        }
    }
}
