package com.orbis.configuration.security.acl.listeners;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.orbis.acegi.providers.dao.hibernate.events.AclRecipientUpdated;
import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.configuration.security.acl.entity.AclSidModel;
import com.orbis.configuration.security.acl.service.AclServiceUtils;

import lombok.RequiredArgsConstructor;

/**
 * Listener for {@link AclRecipientUpdated} event. Updates the recipient's
 * {@link AclSid} in the database.
 */
@Component
@RequiredArgsConstructor
public class AclRecipientUpdatedListener
        implements ApplicationListener<AclRecipientUpdated>
{

    private final AclServiceUtils aclServiceUtils;

    @Override
    @Transactional
    public void onApplicationEvent(AclRecipientUpdated event)
    {
        AclSid sid = (AclSid) event.getRecipient().toSid();
        AclSidModel sidEntity = aclServiceUtils.getSidEntity(sid);
        if (sidEntity != null)
        {
            sidEntity.setSid(sid.getAuthority());
            aclServiceUtils.saveSid(sidEntity);
        }
    }
}
