package com.orbis.configuration.security.acl.listeners;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.orbis.acegi.providers.dao.hibernate.events.AclRecipientCreated;
import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.configuration.security.acl.service.AclServiceUtils;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AclRecipientCreatedListener
        implements ApplicationListener<AclRecipientCreated>
{
    private final AclServiceUtils aclServiceUtils;

    @Override
    public void onApplicationEvent(AclRecipientCreated event)
    {
        AclSid sid = (AclSid) event.getRecipient().toSid();
        aclServiceUtils.createSid(sid);
    }
}
