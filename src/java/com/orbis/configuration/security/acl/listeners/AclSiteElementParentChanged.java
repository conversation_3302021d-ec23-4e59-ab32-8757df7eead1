package com.orbis.configuration.security.acl.listeners;

import org.springframework.context.ApplicationListener;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.MutableAcl;
import org.springframework.stereotype.Component;

import com.orbis.configuration.security.acl.service.AclServiceUtils;
import com.orbis.web.site.SiteElement;
import com.orbis.web.site.event.SiteElementParentChanged;

/**
 * Listener for {@link SiteElementParentChanged} event. Changes parent ACL for
 * the site element.
 */
@Component
public class AclSiteElementParentChanged
        implements ApplicationListener<SiteElementParentChanged>
{

    private final AclServiceUtils aclUtils;

    public AclSiteElementParentChanged(AclServiceUtils aclUtils)
    {
        this.aclUtils = aclUtils;
    }

    @Override
    public void onApplicationEvent(SiteElementParentChanged event)
    {
        MutableAcl acl = aclUtils.readAclById(new ObjectIdentityImpl(
                SiteElement.class, event.getSiteElementId()));
        Acl parentAcl = aclUtils.readAclById(
                new ObjectIdentityImpl(SiteElement.class, event.getNewParentId()));

        if (acl == null && parentAcl == null)
        {
            return;
        }
        if (acl == null)
        {
            acl = aclUtils.createAcl(new ObjectIdentityImpl(SiteElement.class,
                    event.getSiteElementId()));
        }
        acl.setParent(parentAcl);
        aclUtils.updateAcl(acl);
    }
}
