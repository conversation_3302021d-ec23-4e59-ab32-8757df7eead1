package com.orbis.configuration.security.acl;

import com.orbis.configuration.security.acl.repository.SiteElementAclRepository;
import com.orbis.configuration.security.acl.service.AclServiceUtils;
import com.orbis.portal.CommandQueryTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.acls.model.AclCache;

import com.orbis.acegi.acl.basic.hibernate.PermissionManager;
import com.orbis.acegi.acl.basic.hibernate.PermissionManagerImpl;
import com.orbis.web.content.userAdmin.permissions.PermissionProcessor;

@Configuration
public class CommonConfiguration
{

    @Bean
    public PermissionProcessor permissionProcessor(
            PermissionManager permissionManager,
            SiteElementAclRepository siteElementAclRepository)
    {
        return new PermissionProcessor(permissionManager,
                (siteElementAclRepository));
    }

    @Bean
    public SiteElementAclRepository siteElementAclRepository(
            CommandQueryTemplate hibernateTemplate)
    {
        return new SiteElementAclRepository(hibernateTemplate);
    }

    @Bean
    public PermissionManager permissionManager(AclCache aclCache,
            AclServiceUtils aclServiceUtils)
    {
        return new PermissionManagerImpl(aclServiceUtils, aclCache);
    }
}
