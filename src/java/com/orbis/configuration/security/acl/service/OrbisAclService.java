package com.orbis.configuration.security.acl.service;

import com.orbis.configuration.security.acl.core.AclGroupSid;
import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.configuration.security.acl.core.AclUserSid;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.acls.domain.PrincipalSid;
import org.springframework.security.acls.jdbc.JdbcMutableAclService;
import org.springframework.security.acls.jdbc.LookupStrategy;
import org.springframework.security.acls.model.*;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.util.List;

/**
 * Custom implementation of {@link JdbcMutableAclService} that allows to use
 * {@link AclUserSid} and {@link AclGroupSid} as {@link Sid} in ACLs.
 */
public class OrbisAclService extends JdbcMutableAclService
{

    public OrbisAclService(JdbcTemplate jdbcTemplate, LookupStrategy lookupStrategy,
            AclCache aclCache)
    {
        super(jdbcTemplate.getDataSource(), lookupStrategy, aclCache);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected Long createOrRetrieveSidPrimaryKey(Sid sid, boolean allowCreate)
    {
        Assert.notNull(sid, "Sid required");

        String sidName;
        Integer sidId = null;
        boolean sidIsPrincipal = true;

        if (sid instanceof AclUserSid aclUserSid)
        {
            sidName = aclUserSid.getUsername();
            sidId = aclUserSid.getId();

        }
        else if (sid instanceof AclGroupSid aclGroupSid)
        {
            sidName = aclGroupSid.getName();
            sidId = aclGroupSid.getId();
            sidIsPrincipal = false;
        }
        else if (sid instanceof PrincipalSid principalSid)
        {
            sidName = principalSid.getPrincipal();
        }
        else
        {
            throw new IllegalArgumentException("Unsupported implementation of Sid");
        }

        List<Long> sidIds;
        if (sid instanceof PrincipalSid)
        {
            sidIds = jdbcOperations.queryForList(
                    "select id from acl_sid where principal=1 and sid=?",
                    new Object[] { sidName }, Long.class);
        }
        else
        {
            sidIds = jdbcOperations.queryForList(
                    "select id from acl_sid where principal=? and sid=? and sidId=?",
                    new Object[] { sidIsPrincipal, sidName, sidId }, Long.class);
        }

        if (!sidIds.isEmpty())
        {
            return sidIds.get(0);
        }

        if (allowCreate)
        {
            Assert.isTrue(
                    TransactionSynchronizationManager.isSynchronizationActive(),
                    "Transaction must be running");
            return jdbcOperations.queryForObject(
                    "insert into acl_sid (principal, sid,  sidId) OUTPUT inserted.id values (?, ?, ?)",
                    Long.class, sidIsPrincipal, sidName, sidId);
        }
        return null;
    }

    public void deleteAclSid(AclSid sid)
    {
        jdbcOperations.update("delete from acl_sid where sidId=?", sid.getId());
    }
}
