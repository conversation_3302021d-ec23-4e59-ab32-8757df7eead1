package com.orbis.configuration.security.acl.service;

import java.util.List;

import org.springframework.security.acls.model.AccessControlEntry;
import org.springframework.security.acls.model.AclCache;
import org.springframework.security.acls.model.MutableAcl;
import org.springframework.security.acls.model.NotFoundException;
import org.springframework.security.acls.model.ObjectIdentity;
import org.springframework.security.acls.model.Sid;
import org.springframework.transaction.annotation.Transactional;

import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.configuration.security.acl.entity.AclSidModel;
import com.orbis.portal.CommandQueryTemplate;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class AclServiceUtils
{

    private final OrbisAclService jdbcMutableAclService;

    private final CommandQueryTemplate hibernateTemplate;

    private final AclCache aclCache;

    public MutableAcl readAclById(ObjectIdentity objectIdentity)
    {
        try
        {
            return (MutableAcl) jdbcMutableAclService.readAclById(objectIdentity);
        }
        catch (NotFoundException e)
        {
            return null;
        }
    }

    public MutableAcl updateAcl(MutableAcl mutableAcl)
    {
        return jdbcMutableAclService.updateAcl(mutableAcl);
    }

    public MutableAcl createAcl(ObjectIdentity objectIdentity)
    {
        return jdbcMutableAclService.createAcl(objectIdentity);
    }

    public void deleteAcl(ObjectIdentity objectIdentity)
    {
        jdbcMutableAclService.deleteAcl(objectIdentity, true);
    }

    @Transactional
    public void createSid(Sid sid)
    {
        jdbcMutableAclService.createOrRetrieveSidPrimaryKey(sid, true);
    }

    @Transactional
    public void deleteAclSid(AclSid aclSid)
    {
        jdbcMutableAclService.deleteAclSid(aclSid);
        aclCache.clearCache();
    }

    @Transactional(readOnly = true)
    public AclSidModel getSidEntity(AclSid aclSid)
    {
        return (AclSidModel) hibernateTemplate.executeFind((session) -> session
                .createQuery(
                        "from AclSidModel where sidId = :sidId and principal = :principal")
                .setParameter("sidId", aclSid.getId())
                .setParameter("principal", aclSid.isPrincipal()).getSingleResult());
    }

    @Transactional
    public void saveSid(AclSidModel aclSid)
    {
        hibernateTemplate.save(aclSid);
        aclCache.clearCache();
    }

    public void deleteSidPermissions(ObjectIdentity objectIdentity, AclSid sid)
    {
        final MutableAcl acl = readAclById(objectIdentity);
        if (acl != null)
        {
            List<AccessControlEntry> entries = acl.getEntries();
            for (int i = 0; i < acl.getEntries().size(); i++)
            {
                final AccessControlEntry entry = entries.get(i);
                if (entry.getSid().equals(sid))
                {
                    acl.deleteAce(i);
                    updateAcl(acl);
                    break;
                }
            }
        }
    }

}
