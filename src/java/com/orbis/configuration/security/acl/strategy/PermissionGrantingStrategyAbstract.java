package com.orbis.configuration.security.acl.strategy;

import java.util.List;

import org.springframework.security.acls.domain.AuditLogger;
import org.springframework.security.acls.model.AccessControlEntry;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.NotFoundException;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.acls.model.PermissionGrantingStrategy;
import org.springframework.security.acls.model.Sid;
import org.springframework.util.Assert;

/**
 * Abstract base class for implementing the {@link PermissionGrantingStrategy}
 * interface. This class provides common functionality for checking permissions
 * and auditing.
 */
public abstract class PermissionGrantingStrategyAbstract
        implements PermissionGrantingStrategy
{

    private final AuditLogger auditLogger;

    protected PermissionGrantingStrategyAbstract(AuditLogger auditLogger)
    {
        Assert.notNull(auditLogger, "auditLogger cannot be null");
        this.auditLogger = auditLogger;
    }

    /**
     * Checks if a given ACL, list of permissions, and list of security identities
     * are granted access, taking into consideration administrative mode. This class
     * is copy of
     * {@link org.springframework.security.acls.domain.DefaultPermissionGrantingStrategy}
     * except customization of parent acl check {@code isParentGranted}
     * 
     * @param acl
     *            the ACL to check
     * @param permission
     *            a list of permissions to check
     * @param sids
     *            a list of security identities to check
     * @param administrativeMode
     *            indicates whether administrative mode is enabled
     * @return true if the access is granted, false otherwise
     * @throws NotFoundException
     *             if a matching access control entry (ACE) cannot be found
     */
    @Override
    public boolean isGranted(Acl acl, List<Permission> permission, List<Sid> sids,
            boolean administrativeMode) throws NotFoundException
    {
        List<AccessControlEntry> aces = acl.getEntries();
        AccessControlEntry firstRejection = null;
        for (Permission p : permission)
        {
            for (Sid sid : sids)
            {
                // Attempt to find exact match for this permission mask and SID
                boolean scanNextSid = true;
                for (AccessControlEntry ace : aces)
                {
                    if (isGranted(ace, p) && ace.getSid().equals(sid))
                    {
                        // Found a matching ACE, so its authorization decision will
                        // prevail
                        if (ace.isGranting())
                        {
                            // Success
                            if (!administrativeMode)
                            {
                                this.auditLogger.logIfNeeded(true, ace);
                            }
                            return true;
                        }

                        // Failure for this permission, so stop search
                        // We will see if they have a different permission
                        // (this permission is 100% rejected for this SID)
                        if (firstRejection == null)
                        {
                            // Store first rejection for auditing reasons
                            firstRejection = ace;
                        }
                        scanNextSid = false; // helps break the loop

                        break; // exit aces loop
                    }
                }
                if (!scanNextSid)
                {
                    break; // exit SID for loop (now try next permission)
                }
            }
        }

        if (firstRejection != null)
        {
            // We found an ACE to reject the request at this point, as no
            // other ACEs were found that granted a different permission
            if (!administrativeMode)
            {
                this.auditLogger.logIfNeeded(false, firstRejection);
            }
            return false;
        }

        // No matches have been found so far
        if (acl.isEntriesInheriting() && (acl.getParentAcl() != null))
        {
            // We have a parent, so let them try to find a matching ACE
            return isParentGranted(acl.getParentAcl(), permission, sids, false);
        }

        // We either have no parent, or we're the uppermost parent
        throw new NotFoundException(
                "Unable to locate a matching ACE for passed permissions and SIDs");
    }

    /**
     * Checks if the given {@link AccessControlEntry} is granted the specified
     * {@link Permission}.
     *
     * @param ace
     *            The {@link AccessControlEntry} to check.
     * @param p
     *            The {@link Permission} to check against.
     * @return {@code true} if the access is granted, {@code false} otherwise.
     */
    protected boolean isGranted(AccessControlEntry ace, Permission p)
    {
        return ace.getPermission().getMask() >= p.getMask();
    }

    /**
     * Checks if the parent ACL, list of permissions, and list of security
     * identities are granted access, taking into consideration administrative mode.
     *
     * @param acl
     *            the ACL to check
     * @param permission
     *            a list of permissions to check
     * @param sids
     *            a list of security identities to check
     * @param administrativeMode
     *            indicates whether administrative mode is enabled
     * @return true if the access is granted, false otherwise
     * @throws NotFoundException
     *             if a matching access control entry (ACE) cannot be found
     */
    public abstract boolean isParentGranted(Acl acl, List<Permission> permission,
            List<Sid> sids, boolean administrativeMode);

}
