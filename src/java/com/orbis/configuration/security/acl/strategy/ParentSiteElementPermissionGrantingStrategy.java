package com.orbis.configuration.security.acl.strategy;

import java.util.List;

import org.springframework.security.acls.domain.AuditLogger;
import org.springframework.security.acls.model.AccessControlEntry;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.acls.model.Sid;

import com.orbis.configuration.security.acl.core.AclPermission;

/**
 * This class is a concrete implementation of the {@link org.springframework.security.acls.model.PermissionGrantingStrategy} interface.
 * It extends the abstract class {@link PermissionGrantingStrategyAbstract}.
 * This class is responsible for checking if a parent site element is granted access based on the ACL,
 * list of permissions, and list of security identities.
 */
public class ParentSiteElementPermissionGrantingStrategy
        extends PermissionGrantingStrategyAbstract
{
    public ParentSiteElementPermissionGrantingStrategy(AuditLogger auditLogger)
    {
        super(auditLogger);
    }

    /**
     * Checks if the parent ACL, list of permissions, and list of security identities are granted access,
     * taking into consideration administrative mode.
     *
     * @param acl the ACL to check
     * @param permission a list of permissions to check
     * @param sids a list of security identities to check
     * @param administrativeMode indicates whether administrative mode is enabled
     * @return true if the access is granted, false otherwise
     */
    @Override
    public boolean isParentGranted(Acl acl, List<Permission> permission,
            List<Sid> sids, boolean administrativeMode)
    {
        return this.isGranted(acl, permission, sids, administrativeMode);
    }

    /**
     * Checks if the given access control entry is granted the specified permission.
     *
     * @param ace the access control entry to check
     * @param p the permission to check against
     * @return true if the access is granted, false otherwise
     */
    @Override
    protected boolean isGranted(AccessControlEntry ace, Permission p)
    {
        return (ace.getPermission().getMask() & ~AclPermission.READ.getMask()) >= p
                .getMask();
    }
}
