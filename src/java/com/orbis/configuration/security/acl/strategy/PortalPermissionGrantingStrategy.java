package com.orbis.configuration.security.acl.strategy;

import java.util.List;

import org.springframework.security.acls.domain.AuditLogger;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.acls.model.PermissionGrantingStrategy;
import org.springframework.security.acls.model.Sid;

/**
 * The PortalPermissionGrantingStrategy class extends the PermissionGrantingStrategyAbstract class
 * and implements the isParentGranted method. It provides functionality for checking permissions
 * and auditing in a portal environment.
 */
public class PortalPermissionGrantingStrategy
        extends PermissionGrantingStrategyAbstract
{

    private final PermissionGrantingStrategy permissionGrantingStrategy;

    public PortalPermissionGrantingStrategy(AuditLogger auditLogger)
    {
        super(auditLogger);
        permissionGrantingStrategy = new ParentSiteElementPermissionGrantingStrategy(
                auditLogger);

    }

    /**
     * Checks if the parent ACL, list of permissions, and list of security
     * identities are granted access, taking into consideration administrative mode.
     *
     * @param acl the ACL to check
     * @param permission a list of permissions to check
     * @param sids a list of security identities to check
     * @param administrativeMode indicates whether administrative mode is enabled
     * @return true if the access is granted, false otherwise
     */
    @Override
    public boolean isParentGranted(Acl acl, List<Permission> permission,
            List<Sid> sids, boolean administrativeMode)
    {
        return permissionGrantingStrategy.isGranted(acl, permission, sids,
                administrativeMode);
    }
}
