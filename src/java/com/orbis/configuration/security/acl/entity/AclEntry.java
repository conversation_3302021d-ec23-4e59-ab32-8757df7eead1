package com.orbis.configuration.security.acl.entity;

import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.GenericGenerator;

import com.orbis.web.content.PersistableBase;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AclEntry implements PersistableBase<Long>
{

    @Id
    @GeneratedValue(generator = "native")
    @GenericGenerator(name = "native", strategy = "native")
    private Long id;

    private AclObjectIdentity objectIdentity;

    private Integer order;

    private AclSidModel sid;

    @ColumnDefault("0")
    private Integer mask;

    @ColumnDefault("0")
    private Boolean granting;

    private Boolean auditSuccess;

    private Boolean auditFailure;
}
