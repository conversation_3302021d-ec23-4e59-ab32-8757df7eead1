<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.configuration.security.acl.entity.AclObjectIdentity" name="AclObjectIdentity">
        <table name="acl_object_identity">
            <unique-constraint>
                <column-name>object_id_identity</column-name>
                <column-name>object_id_class</column-name>
            </unique-constraint>
        </table>
        <attributes>
            <basic name="objectIdentity">
                <column name="object_id_identity" nullable="false"/>
            </basic>

            <basic name="inheriting">
                <column name="entries_inheriting" nullable="false"/>
            </basic>

            <many-to-one name="aclClass">
                <join-column name="object_id_class"/>
            </many-to-one>
            <many-to-one name="parent">
                <join-column name="parent_object"/>
            </many-to-one>
            <many-to-one name="sid">
                <join-column name="owner_sid"/>
            </many-to-one>

        </attributes>
    </entity>
</entity-mappings>