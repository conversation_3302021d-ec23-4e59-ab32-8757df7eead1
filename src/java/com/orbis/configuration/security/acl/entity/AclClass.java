package com.orbis.configuration.security.acl.entity;

import org.hibernate.annotations.GenericGenerator;

import com.orbis.web.content.PersistableBase;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AclClass implements PersistableBase<Long>
{
    @Id
    @GeneratedValue(generator = "native")
    @GenericGenerator(name = "native", strategy = "native")
    private Long id;

    private String type;
}
