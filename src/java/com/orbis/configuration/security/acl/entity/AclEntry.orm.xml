<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.configuration.security.acl.entity.AclEntry" name="AclEntry">
        <table name="acl_entry">
            <unique-constraint>
                <column-name>ace_order</column-name>
                <column-name>acl_object_identity</column-name>
            </unique-constraint>
        </table>
        <attributes>
            <basic name="order">
                <column name="ace_order" nullable="false"/>
            </basic>
            <basic name="mask">
                <column name="mask" nullable="false" />
            </basic>
            <basic name="granting">
                <column name="granting" nullable="false" />
            </basic>
            <basic name="auditSuccess">
                <column name="audit_success" nullable="false"/>
            </basic>
            <basic name="auditFailure">
                <column name="audit_failure" nullable="false"/>
            </basic>

            <many-to-one name="objectIdentity">
                <join-column name="acl_object_identity"/>
            </many-to-one>
            <many-to-one name="sid">
                <join-column name="sid"/>
            </many-to-one>

        </attributes>
    </entity>
</entity-mappings>