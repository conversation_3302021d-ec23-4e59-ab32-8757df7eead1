package com.orbis.configuration.security.acl;

import com.orbis.configuration.security.acl.strategy.PortalPermissionGrantingStrategy;
import com.orbis.portal.CommandQueryTemplate;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.acls.AclPermissionEvaluator;
import org.springframework.security.acls.domain.AclAuthorizationStrategy;
import org.springframework.security.acls.domain.AclAuthorizationStrategyImpl;
import org.springframework.security.acls.domain.SpringCacheBasedAclCache;
import org.springframework.security.acls.jdbc.JdbcMutableAclService;
import org.springframework.security.acls.jdbc.LookupStrategy;
import org.springframework.security.acls.model.AclCache;
import org.springframework.security.acls.model.PermissionGrantingStrategy;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import com.orbis.configuration.security.acl.core.AclLookupStrategy;
import com.orbis.configuration.security.acl.core.NoAuditLoggerImpl;
import com.orbis.configuration.security.acl.core.OrbisPermissionFactory;
import com.orbis.configuration.security.acl.core.OrbisSidRetrievalStrategy;
import com.orbis.configuration.security.acl.service.AclServiceUtils;
import com.orbis.configuration.security.acl.service.OrbisAclService;

import lombok.val;

/**
 * Class that configures ACL.
 */
@Configuration
public class AclConfiguration
{

    public AclConfiguration()
    {
        SecurityContextHolder
                .setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    }

    @Bean
    public OrbisAclService aclService(JdbcTemplate jdbcTemplate)
    {
        return new OrbisAclService(jdbcTemplate, lookupStrategy(null),
                aclCache(null));
    }

    @Bean
    public AclAuthorizationStrategy aclAuthorizationStrategy()
    {
        return new AclAuthorizationStrategyImpl(
                new SimpleGrantedAuthority("ROLE_USER"));
    }

    @Bean
    public PermissionGrantingStrategy permissionGrantingStrategy()
    {
        return new PortalPermissionGrantingStrategy(new NoAuditLoggerImpl());
    }

    @Bean
    public AclPermissionEvaluator aclPermissionEvaluator()
    {
        val permissionEvaluator = new AclPermissionEvaluator(aclService(null));
        permissionEvaluator
                .setSidRetrievalStrategy(new OrbisSidRetrievalStrategy());
        return permissionEvaluator;
    }

    @Bean
    public AclCache aclCache(CacheManager cacheManager)
    {
        return new SpringCacheBasedAclCache(cacheManager.getCache("acl-cache"),
                permissionGrantingStrategy(), aclAuthorizationStrategy());
    }

    @Bean
    public LookupStrategy lookupStrategy(JdbcTemplate jdbcTemplate)
    {
        AclLookupStrategy aclLookupStrategy = new AclLookupStrategy(jdbcTemplate,
                aclCache(null), aclAuthorizationStrategy(),
                permissionGrantingStrategy());
        aclLookupStrategy.setPermissionFactory(new OrbisPermissionFactory());
        return aclLookupStrategy;
    }

    @Bean
    public AclServiceUtils aclUtils(CommandQueryTemplate hibernateTemplate)
    {
        return new AclServiceUtils(aclService(null), hibernateTemplate,
                aclCache(null));
    }
}
