package com.orbis.configuration.security.acl.core;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.configuration.security.core.PersonGroupGrantedAuthority;
import org.springframework.security.access.hierarchicalroles.NullRoleHierarchy;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.acls.domain.GrantedAuthoritySid;
import org.springframework.security.acls.model.Sid;
import org.springframework.security.acls.model.SidRetrievalStrategy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Implementation of {@link SidRetrievalStrategy} that uses the
 * {@link RoleHierarchy} to determine the {@link Sid}s for the current
 * {@link Authentication}.
 */
public class OrbisSidRetrievalStrategy implements SidRetrievalStrategy
{

    private final RoleHierarchy roleHierarchy = new NullRoleHierarchy();

    public OrbisSidRetrievalStrategy()
    {
    }

    @Override
    public List<Sid> getSids(Authentication authentication)
    {
        Collection<? extends GrantedAuthority> authorities = roleHierarchy
                .getReachableGrantedAuthorities(authentication.getAuthorities());
        List<Sid> sids = new ArrayList<>(authorities.size() + 1);

        if (authentication.getPrincipal() instanceof OrbisUserDetails userDetails)
        {
            UserDetailsImpl user = userDetails.getOriginalUser();

            sids.add(new AclUserSid(user));

            for (GrantedAuthority authority : authorities)
            {
                if (authority instanceof PersonGroupGrantedAuthority group)
                {
                    sids.add(new AclGroupSid(group.id(), group.getAuthority()));
                }
                else
                {
                    sids.add(new GrantedAuthoritySid(authority));
                }
            }
        }
        return sids;
    }
}
