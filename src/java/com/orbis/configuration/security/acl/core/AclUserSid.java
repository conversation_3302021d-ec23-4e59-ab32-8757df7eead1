package com.orbis.configuration.security.acl.core;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import lombok.Getter;

import java.util.Objects;

/**
 * Implementation of {@link AclSid} that uses the {@link UserDetailsImpl} to determine the
 * {@link AclSid}s for the current {@link UserDetailsImpl}.
 */
@Getter
public class AclUserSid implements AclSid
{

    private final Integer id;

    private final String username;

    private final String firstName;

    private final String lastName;

    public AclUserSid(Integer id, String username, String firstName,
            String lastName)
    {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    public AclUserSid(UserDetailsImpl userDetails)
    {
        this.id = userDetails.getId();
        this.username = userDetails.getUsername();
        this.firstName = userDetails.getFirstName();
        this.lastName = userDetails.getLastName();
    }

    public String getFullNameWithUsername()
    {
        return firstName + " " + lastName + " (" + username + ")";
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        AclUserSid that = (AclUserSid) o;
        return Objects.equals(id, that.id)
                && Objects.equals(username, that.username);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(id, username);
    }

    @Override
    public String getAuthority()
    {
        return username;
    }

    @Override
    public Boolean isPrincipal()
    {
        return true;
    }

    @Override
    public String toString()
    {
        return "AclUserSid[" + this.username + ":" + id + "]";
    }
}
