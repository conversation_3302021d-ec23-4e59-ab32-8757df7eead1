package com.orbis.configuration.security.acl.core;

import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Implementation of {@link AclSid} that uses the {@link PersonGroup} to determine the
 * {@link AclSid}s for the current {@link PersonGroup}.
 */
@Getter
@RequiredArgsConstructor
@EqualsAndHashCode
public class AclGroupSid implements AclSid
{

    private final Integer id;

    private final String name;

    public AclGroupSid(PersonGroup group)
    {
        this.id = group.getId();
        this.name = group.getName();
    }

    @Override
    public String getAuthority()
    {
        return name;
    }

    @Override
    public Boolean isPrincipal()
    {
        return false;
    }

    @Override
    public String toString() {
        return "AclGroupSid[" + this.name + ":" + id + "]";
    }
}
