package com.orbis.configuration.security.acl.core;

import org.springframework.security.acls.domain.PermissionFactory;
import org.springframework.security.acls.model.Permission;

import java.util.List;

/**
 * Factory for creating {@link AclPermission} instances.
 */
public class OrbisPermissionFactory implements PermissionFactory
{
    @Override
    public Permission buildFromMask(int mask)
    {
        return AclPermission.of(mask);
    }

    @Override
    public Permission buildFromName(String name)
    {

        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<Permission> buildFromNames(List<String> names)
    {
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
