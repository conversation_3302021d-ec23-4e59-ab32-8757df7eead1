package com.orbis.configuration.security.acl.core;

import org.springframework.security.acls.domain.AbstractPermission;

/**
 * Represents the permissions for the ACL.
 */
public class AclPermission extends AbstractPermission
{
    public static final AclPermission ADMINISTRATION = new AclPermission(
            (int) Math.pow(2.0, 0.0), 'A');

    public static final AclPermission READ = new AclPermission(
            (int) Math.pow(2.0, 1.0), 'R');

    public static final AclPermission WRITE = new AclPermission(
            (int) Math.pow(2.0, 2.0), 'W');

    public static final AclPermission CREATE = new AclPermission(
            (int) Math.pow(2.0, 3.0), 'C');

    public static final AclPermission DELETE = new AclPermission(
            (int) Math.pow(2.0, 4.0), 'D');


    protected AclPermission(int mask)
    {
        super(mask);
    }

    protected AclPermission(int mask, char code)
    {
        super(mask, code);
    }

    public static AclPermission of(int mask)
    {
        return new AclPermission(mask);
    }
}
