package com.orbis.configuration.security.acl.repository;

import com.orbis.configuration.security.acl.core.AclPermission;
import com.orbis.configuration.security.acl.core.AclSid;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.web.site.SiteElement;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
public class SiteElementAclRepository
{

    public final CommandQueryTemplate hibernateTemplate;

    @Transactional(readOnly = true)
    public List<SiteElement> getElementsList(AclSid sid, AclPermission permission)
    {
        return (List<SiteElement>) hibernateTemplate.executeFind((session) ->
                session.createQuery(
                                """
                                        select se from SiteElement se where exists (select ae.id from AclEntry ae join ae.sid sid join
                                        ae.objectIdentity.aclClass clazz where sid.sidId = :sidId and sid.sid=:authority and sid.principal=:principal
                                        and ae.mask = :permission and clazz.type='com.orbis.web.site.SiteElement'
                                        and ae.objectIdentity.objectIdentity = se.id)
                                        """)
                        .setParameter("sidId", sid.getId())
                        .setParameter("authority", sid.getAuthority())
                        .setParameter("principal", sid.isPrincipal())
                        .setParameter("permission", permission.getMask())
                        .getResultList()
        );
    }

    @Transactional(readOnly = true)
    public List<SiteElement> getElementsList(AclSid sid)
    {
        return (List<SiteElement>) hibernateTemplate.executeFind((session) ->
                session.createQuery(
                                """
                                        select se from SiteElement se where exists (select ae.id from AclEntry ae join ae.sid sid join
                                        ae.objectIdentity.aclClass clazz where sid.sidId = :sidId and sid.sid=:authority and sid.principal=:principal
                                        and ae.objectIdentity.objectIdentity = se.id)
                                        """)
                        .setParameter("sidId", sid.getId())
                        .setParameter("authority", sid.getAuthority())
                        .setParameter("principal", sid.isPrincipal())
                        .getResultList());
    }

}
