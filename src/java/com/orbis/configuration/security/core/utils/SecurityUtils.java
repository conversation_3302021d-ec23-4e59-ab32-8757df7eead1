package com.orbis.configuration.security.core.utils;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.configuration.security.core.mfa.GoogleAuthenticatorDevice;
import com.orbis.portal.PortalUtils;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Date;
import java.util.Optional;

@UtilityClass
public class SecurityUtils
{

    public static OrbisUserDetails getCurrentUserDetails()
    {
        return Optional
                .ofNullable(SecurityContextHolder.getContext().getAuthentication())
                .map(it -> (OrbisUserDetails) it.getPrincipal()).orElse(null);
    }

    public static void lockUser(String username) {
        Date accountLockedUntil = DateUtils.addMinutes(new Date(), 10);
        PortalUtils.getJt().update(
                "update user_details set accountLockedUntil = ? where username = ?",
                accountLockedUntil, username);
        SecurityContextHolder.getContext().setAuthentication(null);
    }

    public static void saveSecuritySettings(UserDetailsImpl user,
            boolean mfaEnabled)
    {
        user.setMfaEnabled(mfaEnabled);
        generateUserMfaSecret(user, mfaEnabled);
    }

    public static void generateUserMfaSecret(UserDetailsImpl user,
            boolean mfaEnabled)
    {
        if (mfaEnabled && StringUtils.isEmpty(user.getMfaSecret()))
        {
            user.setMfaSecret(new GoogleAuthenticatorDevice().secret());
        }
        PortalUtils.getHt().update(user);
    }
}
