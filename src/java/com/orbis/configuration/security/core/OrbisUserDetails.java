package com.orbis.configuration.security.core;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.mfa.OneTimePasswordDevice;
import com.orbis.configuration.security.core.mfa.UniqueGenerateGoogleAuthenticatorDevice;

import lombok.Getter;

/**
 * This class represents the security user details for the application. Wraps
 * the UserDetailsImpl class e.g. original user The main goal of this class is
 * decoupling the security user details from the database.
 */

@Getter
public class OrbisUserDetails implements UserDetails
{

    private final UserDetailsImpl originalUser;

    private OneTimePasswordDevice device;

    public OrbisUserDetails(UserDetailsImpl originalUser)
    {
        this(originalUser, originalUser.isMfaEnabled());
    }


    public OrbisUserDetails(UserDetailsImpl originalUser, boolean requiresMfa)
    {
        this.originalUser = originalUser;
        if (requiresMfa)
        {
            device = new UniqueGenerateGoogleAuthenticatorDevice();
        }
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities()
    {
        List<GrantedAuthority> collect = originalUser.getGroups().stream().map(
                it -> new PersonGroupGrantedAuthority(it.getId(), it.getName()))
                .collect(Collectors.toList());
        collect.add(new SimpleGrantedAuthority("ROLE_USER"));
        return collect;
    }

    @Override
    public String getPassword()
    {
        return originalUser.getPassword();
    }

    @Override
    public String getUsername()
    {
        return originalUser.getUsername();
    }

    @Override
    public boolean isAccountNonExpired()
    {
        return originalUser.isAccountNonExpired();
    }

    @Override
    public boolean isAccountNonLocked()
    {
        Date accountLockedUntil = originalUser.getAccountLockedUntil();
        return accountLockedUntil == null || new Date().after(accountLockedUntil);
    }

    @Override
    public boolean isCredentialsNonExpired()
    {
        return originalUser.isCredentialsNonExpired();
    }

    @Override
    public boolean isEnabled()
    {
        return originalUser.isEnabled();
    }

    public boolean requiresMfa()
    {
        return device != null && !device.confirmed();
    }
}
