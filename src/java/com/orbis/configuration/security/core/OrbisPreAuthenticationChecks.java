package com.orbis.configuration.security.core;

import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.userdetails.UserDetails;

import com.orbis.acegi.providers.dao.hibernate.NotApprovedException;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;

import lombok.RequiredArgsConstructor;

/**
 * This class is used to check the user details before authentication.
 */
@RequiredArgsConstructor
public class OrbisPreAuthenticationChecks extends AccountStatusUserDetailsChecker
{

    @Override
    public void check(UserDetails toCheck)
    {
        UserDetailsImpl user = ((OrbisUserDetails) toCheck).getOriginalUser();
        if (user.isRoot())
        {
            return; // bypass checks
        }
        super.check(toCheck);

        if (user.isProspectEmployer())
        {
            throw new BadCredentialsException("Not allowed to log in directly.");
        }

        if (!toCheck.isEnabled()
                || !UserDetailsImpl.USER_STATUS_ACTIVE.equals(user.getUserStatus()))
        {
            if (user.isEmployer() && user
                    .getApprovalStatus() == UserDetailsImpl.APPROVAL_STATUS_PENDING)
            {
                throw new NotApprovedException("User account is in pending status");
            }
            throw new DisabledException("User account is disabled");
        }
        else
        {
            PersonGroup userPrimaryGroup = user.getPrimaryGroup();

            if (userPrimaryGroup == null)
            {
                throw new DisabledException(
                        "Unable to determine user's primary group");
            }
            else if (userPrimaryGroup.isExternalGroup())
            {
                throw new BadCredentialsException(
                        "Not allowed to log in directly.");
            }
        }

    }

}
