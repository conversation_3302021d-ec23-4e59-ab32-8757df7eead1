package com.orbis.configuration.security.core;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsChecker;

import lombok.Setter;
import lombok.val;

/**
 * This Authentication Manager is used to switch the user in the current session.
 * This manager does not perform password checks.
 */
@Setter
public class SwitchUserAuthenticationManager implements AuthenticationManager
{

    private UserDetailsChecker userDetailsChecker;

    private SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
            .getContextHolderStrategy();

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException
    {
        val details = authentication.getPrincipal();
        if (userDetailsChecker != null && details instanceof UserDetails ud)
        {
            userDetailsChecker.check(ud);
        }
        SecurityContext emptyContext = securityContextHolderStrategy.createEmptyContext();
        emptyContext.setAuthentication(authentication);
        this.securityContextHolderStrategy.setContext(emptyContext);
        return authentication;
    }
}
