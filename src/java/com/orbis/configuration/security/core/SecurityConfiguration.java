package com.orbis.configuration.security.core;

import java.util.List;
import java.util.Objects;

import com.orbis.configuration.security.core.csrf.CustomOrbisCsrfFilter;
import com.orbis.configuration.security.core.csrf.RequireCsrfProtectionMatcher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.DefaultAuthenticationEventPublisher;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.DelegatingPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.access.channel.ChannelProcessor;
import org.springframework.security.web.access.intercept.AuthorizationFilter;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;

import com.orbis.acegi.acl.basic.hibernate.PermissionManager;
import com.orbis.acegi.providers.dao.hibernate.UserLoginAttemptsService;
import com.orbis.acegi.providers.dao.hibernate.UserManager;
import com.orbis.acegi.providers.dao.hibernate.UserManagerImpl;
import com.orbis.acegi.ui.webapp.OrbisAuthenticationProcessingFilterEntryPoint;
import com.orbis.configuration.security.core.csrf.ApplicationCsrfTokenProviderFilter;
import com.orbis.configuration.security.core.csrf.CsrfStrategy;
import com.orbis.configuration.security.core.csrf.JustLogCsrfAccessDeniedHandler;
import com.orbis.configuration.security.core.mfa.MfaPageResolver;
import com.orbis.configuration.security.core.mfa.OneTimePasswordAuthFilter;
import com.orbis.configuration.security.core.mfa.OneTimePasswordAuthProvider;
import com.orbis.configuration.security.core.mfa.PageResolver;
import com.orbis.configuration.security.core.mfa.TwoFactorAuthenticationHandler;
import com.orbis.configuration.security.core.repository.UserRepository;
import com.orbis.configuration.security.jwt.PasswordlessAuthenticationProvider;
import com.orbis.configuration.security.jwt.PasswordlessJwtAuthenticationFilter;
import com.orbis.configuration.security.jwt.TokenVerificationService;
import com.orbis.security.AuthenticationDetails;
import com.orbis.security.Md5ThenSha256PasswordEncoder;
import com.orbis.security.RecaptchaAuthentication;
import com.orbis.spring.servlet.ProtectResourcesFilter;
import com.orbis.utils.CryptoUtils;
import com.orbis.web.content.disruptive.DisruptiveRequestFilter;
import com.orbis.web.site.RequestParserFilter;
import com.orbis.web.site.RequestSecurityFilter;
import com.orbis.web.site.SiteManager;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.springframework.web.filter.OncePerRequestFilter;

@EnableWebSecurity
@Configuration
@RequiredArgsConstructor
public class SecurityConfiguration
{

    @Value("${orbis.security.csrf.strategy}")
    private CsrfStrategy csrfStrategy;

    public static final String DEFAULT_TARGET_URL = "/myAccount.htm";

    public static final String REDIRECT_TARGET_URL_PARAMETER = "securityRedirectTargetUrl";

    public static final String DEFAULT_LOGIN_PAGE_URL = "/notLoggedIn";

    public static final String LOGIN_PROCESS_URL = "/j_spring_security_check";

    public final RequestParserFilter requestParserFilter;

    @Bean
    @Order(2)
    protected SecurityFilterChain securityFilterChain(HttpSecurity http,
            List<ChannelProcessor> channelProcessors,
            UserLoginAttemptsService loginAttemptsService) throws Exception
    {
        http.csrf(AbstractHttpConfigurer::disable)
                .addFilterBefore(protectResourcesFilter(),
                        ChannelProcessingFilter.class)
                .addFilterBefore(multipartFilter(), CsrfFilter.class)
                .addFilterBefore(requestParserFilter, AuthorizationFilter.class)
                .addFilterBefore(csrfFilter(), AuthorizationFilter.class)
                .addFilterBefore(applicationCsrfTokenProviderFilter(),
                        AuthorizationFilter.class)
                .addFilterAfter(requestSecurityFilter(null),
                        AuthorizationFilter.class)
                .addFilterAfter(disruptiveRequestFilter(),
                        RequestSecurityFilter.class)
                .addFilterAt(jwtAuthenticationFilter(null),
                        ChannelProcessingFilter.class)
                .anonymous(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(
                        authorizeHttpRequests -> authorizeHttpRequests
                                .requestMatchers(
                                        new SiteElementPermittedRequestMatcher())
                                .permitAll()
                                .requestMatchers(
                                        new SiteElementRestrictedRequestMatcher())
                                .authenticated())
                .requiresChannel(channelRegistry -> channelRegistry
                        .channelProcessors(channelProcessors))
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED))
                .formLogin(formLogin -> formLogin.loginPage(DEFAULT_LOGIN_PAGE_URL)
                        .usernameParameter("j_username")
                        .passwordParameter("j_password")
                        .loginProcessingUrl(LOGIN_PROCESS_URL)
                        .failureHandler(authenticationFailureHandler(null))
                        .authenticationDetailsSource(
                                authenticationDetailsSource(null))
                        .successHandler(twoFactorAuthenticationSuccessHandler(null,
                                loginAttemptsService)))
                .logout(AbstractHttpConfigurer::disable)
                .addFilterAfter(
                        oneTimePasswordAuthFilter(null, loginAttemptsService),
                        UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling(eh -> eh.authenticationEntryPoint(
                        orbisAuthenticationProcessingFilterEntryPoint(null)))
                .cors(AbstractHttpConfigurer::disable)
                .headers(AbstractHttpConfigurer::disable)
                .authenticationManager(authenticationManager());
        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager()
    {
        ProviderManager providerManager = new ProviderManager(
                daoAuthenticationProvider(),
                passwordlessAuthenticationProvider(null));
        providerManager
                .setAuthenticationEventPublisher(authenticationEventPublisher());
        return providerManager;
    }

    protected static final String[] PROTECTED_RESOURCES = { "/site/private.*",
            "/content/private.*", "/content/documents/mergedpfs.*",
            "/content/documents/dcProOrig.*", "/dcProIn.*",
            "/content/documents/dcProOut.*", "/content/documents/dcProError.*",
            "/content/documents/docuploads.*", "/content/documents/jqGridExports.*",
            "/content/documents/ptUploads.*", "/content/documents/fpUploads.*",
            "/content/documents/fpBundles.*", "/content/documents/fpMergedDocs.*" };

    @Bean
    public TwoFactorAuthenticationHandler twoFactorAuthenticationSuccessHandler(
            SiteManager siteManager, UserLoginAttemptsService loginAttemptsService)
    {
        PageResolver pageResolver = new MfaPageResolver(siteManager);
        return new TwoFactorAuthenticationHandler(pageResolver,
                authenticationSuccessHandler(), loginAttemptsService);
    }

    @Bean
    public OneTimePasswordAuthFilter oneTimePasswordAuthFilter(
            SiteManager siteManager, UserLoginAttemptsService loginAttemptsService)
    {
        return new OneTimePasswordAuthFilter(oneTimePasswordAuthProvider(),
                twoFactorAuthenticationSuccessHandler(null, loginAttemptsService));
    }

    @Bean
    public OneTimePasswordAuthProvider oneTimePasswordAuthProvider()
    {
        return new OneTimePasswordAuthProvider();
    }

    @Bean
    public ProtectResourcesFilter protectResourcesFilter()
    {
        return new ProtectResourcesFilter(PROTECTED_RESOURCES);
    }

    @Bean
    public DisruptiveRequestFilter disruptiveRequestFilter()
    {
        return new DisruptiveRequestFilter();
    }

    @Bean
    public RequestSecurityFilter requestSecurityFilter(SiteManager siteManager)
    {
        return new RequestSecurityFilter(siteManager);
    }

    @Bean
    public AuthenticationEntryPoint orbisAuthenticationProcessingFilterEntryPoint(
            SiteManager siteManager)
    {
        return new OrbisAuthenticationProcessingFilterEntryPoint(siteManager);
    }

    @Bean
    public AuthenticationSuccessHandler authenticationSuccessHandler()
    {
        OrbisAuthenticationSuccessHandler successHandler = new OrbisAuthenticationSuccessHandler();
        successHandler.setDefaultTargetUrl(DEFAULT_TARGET_URL);
        successHandler.setTargetUrlParameter(REDIRECT_TARGET_URL_PARAMETER);
        successHandler.setAlwaysUseDefaultTargetUrl(false);
        return successHandler;
    }

    @Bean
    public AuthenticationFailureHandler authenticationFailureHandler(
            SiteManager siteManager)
    {
        return new OrbisAuthenticationFailureHandler(siteManager);
    }

    @Bean
    public AuthenticationEventPublisher authenticationEventPublisher()
    {
        return new DefaultAuthenticationEventPublisher();
    }

    @Bean
    public UserDetailsService userDetailsService(UserRepository userRepository)
    {
        return new OrbisUserDetailsService(userRepository);
    }

    @Bean
    public AuthenticationProvider daoAuthenticationProvider()
    {
        val authenticationManager = new OrbisDaoAuthenticationProvider();
        authenticationManager.setPasswordEncoder(passwordEncoder());
        authenticationManager.setUserDetailsService(userDetailsService(null));
        authenticationManager
                .setPreAuthenticationChecks(new OrbisPreAuthenticationChecks());
        return authenticationManager;
    }

    @Bean
    public UserDetailsChecker orbisPreAuthenticationChecks()
    {
        return new OrbisPreAuthenticationChecks();
    }

    @Bean
    public AuthenticationDetailsSource<HttpServletRequest, AuthenticationDetails> authenticationDetailsSource(
            RecaptchaAuthentication recaptchaAuthentication)
    {
        return new OrbisAuthenticationDetailsSource(recaptchaAuthentication);
    }

    @Bean
    public PasswordEncoder passwordEncoder()
    {
        DelegatingPasswordEncoder delegatingPasswordEncoder = (DelegatingPasswordEncoder) PasswordEncoderFactories
                .createDelegatingPasswordEncoder();
        delegatingPasswordEncoder.setDefaultPasswordEncoderForMatches(
                new Md5ThenSha256PasswordEncoder());
        return delegatingPasswordEncoder;
    }

    /**
     * This is a special authentication manager that does not check the password.
     * Authentication manager doesn't check password and should be used only for the
     * switch user functionality.
     *
     * @return authentication manager
     */
    @Bean
    public AuthenticationManager noPasswordCheckAuthenticationManager()
    {
        val authenticationManager = new SwitchUserAuthenticationManager();
        authenticationManager.setUserDetailsChecker(orbisPreAuthenticationChecks());
        return authenticationManager;
    }

    @Bean
    public RecaptchaAuthentication recaptchaAuthentication()
    {
        return new RecaptchaAuthentication();
    }

    @Bean
    public CryptoUtils cryptoUtils(ResourceLoader resourceLoader)
    {
        val cryptoUtils = new CryptoUtils();
        cryptoUtils.setKeyStore(
                resourceLoader.getResource("/WEB-INF/conf/keystore.jceks"));
        return cryptoUtils;
    }

    @Bean
    public UserManager userManager(PermissionManager permissionManager,
            PasswordEncoder passwordEncoder)
    {
        val userManager = new UserManagerImpl();
        userManager.setPermissionManager(permissionManager);
        userManager.setPasswordEncoder(passwordEncoder);
        return userManager;
    }

    @Bean
    public PasswordlessJwtAuthenticationFilter jwtAuthenticationFilter(
            SiteManager siteManager)
    {
        PasswordlessJwtAuthenticationFilter filter = new PasswordlessJwtAuthenticationFilter(
                "/auth/passwordless");
        filter.setAuthenticationSuccessHandler(authenticationSuccessHandler());
        filter.setAuthenticationFailureHandler(
                authenticationFailureHandler(siteManager));
        filter.setAuthenticationManager(authenticationManager());
        filter.setSecurityContextRepository(
                new HttpSessionSecurityContextRepository());
        return filter;
    }

    @Bean
    public AuthenticationProvider passwordlessAuthenticationProvider(
            TokenVerificationService tokenVerificationService)
    {
        PasswordlessAuthenticationProvider provider = new PasswordlessAuthenticationProvider(
                userDetailsService(null), tokenVerificationService);
        provider.setPreAuthenticationChecks(new OrbisPreAuthenticationChecks());
        return provider;
    }

    @Bean
    public CsrfTokenRepository csrfTokenRepository()
    {
        return new HttpSessionCsrfTokenRepository();
    }

    @Bean
    public OncePerRequestFilter csrfFilter()
    {
        Objects.requireNonNull(csrfStrategy);
        if (csrfStrategy == CsrfStrategy.LOGGER)
        {
            val csrfFilter = new CustomOrbisCsrfFilter(csrfTokenRepository());
            csrfFilter.setRequireCsrfProtectionMatcher(
                    new RequireCsrfProtectionMatcher());
            csrfFilter.setAccessDeniedHandler(new JustLogCsrfAccessDeniedHandler());
            return csrfFilter;
        }
        else
        {
            val csrfFilter = new CsrfFilter(csrfTokenRepository());
            csrfFilter.setRequireCsrfProtectionMatcher(
                    new RequireCsrfProtectionMatcher());
            csrfFilter.setAccessDeniedHandler(new AccessDeniedHandlerImpl());
            return csrfFilter;
        }
    }

    @Bean
    public ApplicationCsrfTokenProviderFilter applicationCsrfTokenProviderFilter()
    {
        return new ApplicationCsrfTokenProviderFilter();
    }

    @Bean
    public MultipartProcessingFilter multipartFilter()
    {
        return new MultipartProcessingFilter();
    }
}
