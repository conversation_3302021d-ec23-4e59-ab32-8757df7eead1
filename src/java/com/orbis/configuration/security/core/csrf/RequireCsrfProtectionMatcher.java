package com.orbis.configuration.security.core.csrf;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.Action;
import com.orbis.utils.ActionHelper;
import com.orbis.web.site.SiteController;
import com.orbis.web.site.SiteElement;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * A Spring Security {@link RequestMatcher} implementation that determines which
 * requests require CSRF protection.
 * <p>
 * This matcher excludes "safe" HTTP methods (GET, HEAD, TRACE, OPTIONS) from
 * CSRF protection, as these methods should not modify state. Additionally, it
 * exempts certain whitelisted actions from CSRF checks based on
 * application-specific logic.
 * </p>
 */
public class RequireCsrfProtectionMatcher implements RequestMatcher
{

    /**
     * Set of HTTP methods that are considered "safe" and do not require CSRF
     * protection.
     * <p>
     * These methods (GET, HEAD, TRACE, OPTIONS) are read-only by definition and
     * should not change application state, thus not requiring CSRF protection.
     * </p>
     */
    private final Set<String> allowedMethods = Set.of("GET", "HEAD", "TRACE",
            "OPTIONS");

    /**
     * Determines whether a request requires CSRF protection.
     * <p>
     * A request requires CSRF protection when:
     * <ul>
     *   <li>The HTTP method is not one of the safe methods (not in allowedMethods)</li>
     *   <li>AND either:</li>
     *   <ul>
     *     <li>The request has no "action" parameter, OR</li>
     *     <li>The action specified is not whitelisted for the current element</li>
     *   </ul>
     * </ul>
     * </p>
     *
     * @param request the HTTP request to check
     * @return true if the request requires CSRF protection, false otherwise
     */
    @Override
    public boolean matches(HttpServletRequest request)
    {
        return !this.allowedMethods.contains(request.getMethod())
                && (!StringUtils.hasLength(request.getParameter("action"))
                        || !isActionWhitelisted(request));
    }

    private boolean isActionWhitelisted(HttpServletRequest request)
    {

        SiteElement currentSiteElement = resolveCurrentSiteElement(request);

        if (currentSiteElement == null)
        {
            return false;
        }

        return SiteController.isWhitelisted(currentSiteElement,
                ActionHelper.decryptAction(request.getParameter("action"))
                        .map(Action::getAction).orElse(""));
    }

    private SiteElement resolveCurrentSiteElement(HttpServletRequest request)
    {
        List<SiteElement> uriElements = PortalUtils.getUriElements(request);
        return uriElements.stream().reduce((first, second) -> second).orElse(null);
    }

    @Override
    public String toString()
    {
        return "CsrfNotRequired " + this.allowedMethods;
    }
}
