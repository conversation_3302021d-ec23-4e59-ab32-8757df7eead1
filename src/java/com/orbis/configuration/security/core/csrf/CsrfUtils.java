package com.orbis.configuration.security.core.csrf;

import org.springframework.security.web.csrf.CsrfToken;

import lombok.experimental.UtilityClass;

/**
 * Utility class providing helper methods for CSRF token operations.
 * <p>
 * This class contains static methods to facilitate common CSRF-related tasks
 * in web applications, such as generating HTML form fields for CSRF protection.
 * </p>
 */
@UtilityClass
public class CsrfUtils
{

    /**
     * Generates an HTML hidden input field containing the CSRF token.
     * <p>
     * This method creates an HTML input element that can be included in forms
     * to provide CSRF protection. The input's name attribute will be set to the
     * parameter name specified by the CSRF token, and its value will be set to
     * the token value.
     * </p>
     *
     * @param csrfToken the CSRF token to render as a hidden input field, may be null
     * @return an HTML string containing a hidden input field with the CSRF token,
     *         or null if the provided csrfToken is null
     */
    public static String renderHiddenInput(CsrfToken csrfToken)
    {
        return csrfToken != null
                ? String.format("<input type=\"hidden\" name=\"%s\" value=\"%s\"/>",
                        csrfToken.getParameterName(), csrfToken.getToken())
                : null;
    }
}
