package com.orbis.configuration.security.core.csrf;

import java.io.IOException;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashSet;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.core.log.LogMessage;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.crypto.codec.Utf8;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRequestHandler;
import org.springframework.security.web.csrf.DeferredCsrfToken;
import org.springframework.security.web.csrf.InvalidCsrfTokenException;
import org.springframework.security.web.csrf.MissingCsrfTokenException;
import org.springframework.security.web.csrf.XorCsrfTokenRequestAttributeHandler;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.apachecommons.CommonsLog;

/**
 * Custom CSRF filter implementation that doesn't stop filter execution when
 * CSRF is invalid or missing. Unlike the standard Spring Security CsrfFilter,
 * this filter will continue the filter chain execution even after detecting an
 * invalid or missing CSRF token and invoking the accessDeniedHandler. This
 * filter is a copy of Spring {@link CsrfFilter} which should be preferred
 * instead of this and was created to not interrupt request
 */
@CommonsLog
public final class CustomOrbisCsrfFilter extends OncePerRequestFilter
{
    public static final RequestMatcher DEFAULT_CSRF_MATCHER = new DefaultRequiresCsrfMatcher();

    private static final String SHOULD_NOT_FILTER = "SHOULD_NOT_FILTER"
            + CsrfFilter.class.getName();

    private final Log logger = LogFactory.getLog(getClass());

    private final CsrfTokenRepository tokenRepository;

    private RequestMatcher requireCsrfProtectionMatcher = DEFAULT_CSRF_MATCHER;

    private AccessDeniedHandler accessDeniedHandler = new AccessDeniedHandlerImpl();

    private CsrfTokenRequestHandler requestHandler = new XorCsrfTokenRequestAttributeHandler();

    public CustomOrbisCsrfFilter(CsrfTokenRepository tokenRepository)
    {
        Assert.notNull(tokenRepository, "tokenRepository cannot be null");
        this.tokenRepository = tokenRepository;
    }

    protected boolean shouldNotFilter(HttpServletRequest request)
    {
        return Boolean.TRUE.equals(request.getAttribute(SHOULD_NOT_FILTER));
    }

    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        DeferredCsrfToken deferredCsrfToken = this.tokenRepository
                .loadDeferredToken(request, response);
        request.setAttribute(DeferredCsrfToken.class.getName(), deferredCsrfToken);
        this.requestHandler.handle(request, response, deferredCsrfToken::get);
        if (!this.requireCsrfProtectionMatcher.matches(request))
        {
            if (this.logger.isTraceEnabled())
            {
                this.logger.trace(
                        "Did not protect against CSRF since request did not match "
                                + this.requireCsrfProtectionMatcher);
            }
            filterChain.doFilter(request, response);
            return;
        }
        CsrfToken csrfToken = deferredCsrfToken.get();
        String actualToken = this.requestHandler.resolveCsrfTokenValue(request,
                csrfToken);
        if (!equalsConstantTime(csrfToken.getToken(), actualToken))
        {
            boolean missingToken = deferredCsrfToken.isGenerated();
            this.logger.debug(LogMessage.of(() -> "Invalid CSRF token found for "
                    + UrlUtils.buildFullRequestUrl(request)));
            AccessDeniedException exception = (!missingToken)
                    ? new InvalidCsrfTokenException(csrfToken, actualToken)
                    : new MissingCsrfTokenException(actualToken);
            this.accessDeniedHandler.handle(request, response, exception);
        }
        filterChain.doFilter(request, response);
    }

    public static void skipRequest(HttpServletRequest request)
    {
        request.setAttribute(SHOULD_NOT_FILTER, Boolean.TRUE);
    }

    public void setRequireCsrfProtectionMatcher(
            RequestMatcher requireCsrfProtectionMatcher)
    {
        Assert.notNull(requireCsrfProtectionMatcher,
                "requireCsrfProtectionMatcher cannot be null");
        this.requireCsrfProtectionMatcher = requireCsrfProtectionMatcher;
    }

    public void setAccessDeniedHandler(AccessDeniedHandler accessDeniedHandler)
    {
        Assert.notNull(accessDeniedHandler, "accessDeniedHandler cannot be null");
        this.accessDeniedHandler = accessDeniedHandler;
    }

    public void setRequestHandler(CsrfTokenRequestHandler requestHandler)
    {
        Assert.notNull(requestHandler, "requestHandler cannot be null");
        this.requestHandler = requestHandler;
    }

    private static boolean equalsConstantTime(String expected, String actual)
    {
        if (expected == actual)
        {
            return true;
        }
        else if (expected != null && actual != null)
        {
            byte[] expectedBytes = Utf8.encode(expected);
            byte[] actualBytes = Utf8.encode(actual);
            return MessageDigest.isEqual(expectedBytes, actualBytes);
        }
        else
        {
            return false;
        }
    }

    private static final class DefaultRequiresCsrfMatcher implements RequestMatcher
    {
        private final HashSet<String> allowedMethods = new HashSet(
                Arrays.asList("GET", "HEAD", "TRACE", "OPTIONS"));

        private DefaultRequiresCsrfMatcher()
        {
        }

        public boolean matches(HttpServletRequest request)
        {
            return !this.allowedMethods.contains(request.getMethod());
        }

        public String toString()
        {
            return "CsrfNotRequired " + this.allowedMethods;
        }
    }
}
