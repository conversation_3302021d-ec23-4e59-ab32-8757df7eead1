package com.orbis.configuration.security.core.csrf;

import lombok.experimental.UtilityClass;
import org.springframework.security.web.csrf.CsrfToken;

/**
 * Utility class that provides thread-local storage for CSRF tokens.
 * <p>
 * This class maintains a thread-local variable to hold the current
 * {@link CsrfToken} during request processing. It enables components across the
 * application to access the CSRF token without passing it explicitly through method
 * parameters.
 * </p>
 * <p>
 * It uses {@link InheritableThreadLocal} to ensure that child threads spawned
 * during request processing inherit the same CSRF token context.
 * </p>
 */
@UtilityClass
public class CsrfTokenHolder {

    private static final ThreadLocal<CsrfToken> CSRF_TOKEN_HOLDER =
            new InheritableThreadLocal<>();

    public static void setCsrfToken(final CsrfToken clientInfo) {
        CSRF_TOKEN_HOLDER.set(clientInfo);
    }

    public static CsrfToken getCsrfToken() {
        return CSRF_TOKEN_HOLDER.get();
    }

    public static void clear() {
        CSRF_TOKEN_HOLDER.remove();
    }
}
