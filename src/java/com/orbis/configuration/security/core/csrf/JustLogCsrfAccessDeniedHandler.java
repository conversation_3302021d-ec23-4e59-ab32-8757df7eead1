package com.orbis.configuration.security.core.csrf;

import java.io.IOException;
import java.util.Optional;

import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import com.orbis.utils.Action;
import com.orbis.utils.ActionHelper;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.apachecommons.CommonsLog;

/**
 * A Spring Security {@link AccessDeniedHandler} implementation that logs CSRF
 * protection violations without redirecting the user.
 * <p>
 * When a CSRF token validation fails, this handler logs details about the
 * request that was denied, including URI, method, and remote address. Unlike
 * other handlers that might redirect to an error page, this implementation only
 * logs the violation without modifying the response.
 * </p>
 */
@CommonsLog
public class JustLogCsrfAccessDeniedHandler implements AccessDeniedHandler
{

    /**
     * <PERSON>les access denied exceptions by logging information about the denied
     * request without modifying the HTTP response.
     * <p>
     * This method logs a warning with basic request information and, if debug
     * logging is enabled, additional details including request headers.
     * </p>
     *
     * @param request the HTTP request that was denied due to CSRF protection
     * @param response the HTTP response (not modified by this handler)
     * @param accessDeniedException the exception that triggered the access denial
     * @throws IOException if an I/O error occurs during handling
     * @throws ServletException if a servlet error occurs during handling
     */
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
            AccessDeniedException accessDeniedException)
            throws IOException, ServletException
    {

        Optional<Action> action = ActionHelper
                .decryptAction(request.getParameter("action"));

        log.warn("CSRF protection denied access to request: "
                + request.getRequestURI() + ", method: " + request.getMethod()
                + ", remote address: " + request.getRemoteAddr()
                + ", exception message: " + accessDeniedException.getMessage()
                + ", action: " + action.map(Action::getAction).orElse(null)
                + ", subAction: " + action.map(Action::getSubAction).orElse(null));

        if (log.isDebugEnabled())
        {
            log.debug("Additional request details - Headers: "
                    + getHeadersInfo(request) + ", Referrer: "
                    + request.getHeader("referer"));
        }

    }

    /**
     * Helper method to collect request header information for debugging
     */
    private String getHeadersInfo(HttpServletRequest request)
    {
        StringBuilder headerInfo = new StringBuilder("{");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements())
        {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerInfo.append(headerName).append(": ").append(headerValue);

            if (headerNames.hasMoreElements())
            {
                headerInfo.append(", ");
            }
        }

        headerInfo.append("}");
        return headerInfo.toString();
    }
}
