package com.orbis.configuration.security.core.csrf;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Filter that extracts CSRF tokens from HTTP requests and makes them available
 * throughout the application context via a thread-local holder.
 * <p>
 * This filter extends {@link OncePerRequestFilter} to ensure it's only applied once
 * per request. It extracts the CSRF token from each request using the configured
 * CSRF token repository and stores it in a thread-local variable through
 * {@link CsrfTokenHolder} for access by other components during request
 * processing.
 * </p>
 */
public class ApplicationCsrfTokenProviderFilter extends OncePerRequestFilter
{

    /**
     * Processes an HTTP request to extract and make the CSRF token available to the
     * application, then continues the filter chain.
     * <p>
     * The method retrieves the CSRF token from the request using the configured
     * repository and stores it in {@link CsrfTokenHolder}. After the filter chain
     * completes, the token is cleared from the holder to prevent memory leaks.
     * </p>
     *
     * @param request
     *         the HTTP request being processed
     * @param response
     *         the HTTP response being generated
     * @param filterChain
     *         the filter chain for executing other filters
     * @throws ServletException
     *         if a servlet exception occurs during processing
     * @throws IOException
     *         if an I/O exception occurs during processing
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        try
        {
            CsrfToken token = (CsrfToken) request.getAttribute(CsrfToken.class.getName());
            CsrfTokenHolder.setCsrfToken(token);
            filterChain.doFilter(request, response);
        }
        finally
        {
            CsrfTokenHolder.clear();
        }
    }
}
