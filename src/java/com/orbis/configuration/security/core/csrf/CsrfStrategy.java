package com.orbis.configuration.security.core.csrf;

/**
 * Enumeration of available CSRF access denied strategies.
 * <p>
 * This enum defines the different approaches that can be used when handling
 * CSRF token validation failures in the application.
 * </p>
 */
public enum CsrfStrategy
{
    /**
     * Strategy that logs CSRF access denied events without modifying the response.
     * <p>
     * When this strategy is selected, CSRF validation failures are logged but do
     * not result in redirects or error pages. This is useful for monitoring CSRF
     * attempts without disrupting user experience.
     * </p>
     */
    LOGGER,

    /**
     * Strategy that responds to CSRF access denied events with an error page.
     * <p>
     * When this strategy is selected, CSRF validation failures result in
     * redirecting the user to an error page or returning an error response. This
     * provides clear feedback to users when a request fails CSRF validation.
     * </p>
     */
    ERROR
}