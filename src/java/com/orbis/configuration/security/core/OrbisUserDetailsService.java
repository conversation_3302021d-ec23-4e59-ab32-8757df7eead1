package com.orbis.configuration.security.core;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.repository.UserRepository;
import com.orbis.web.content.acrm.AcrmHelper;

import jakarta.persistence.NoResultException;
import lombok.RequiredArgsConstructor;

/**
 * This class is used to load the security user details from the database.
 */
@RequiredArgsConstructor
public class OrbisUserDetailsService implements UserDetailsService
{

    private final UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String username)
            throws UsernameNotFoundException
    {
        UserDetailsImpl u;
        try
        {
            u = userRepository.findByUsername(username);
            AcrmHelper.populateLinkedUsers(u);
        }
        catch (NoResultException e)
        {
            throw new UsernameNotFoundException("user not found or deleted");
        }
        return new OrbisUserDetails(u);
    }
}
