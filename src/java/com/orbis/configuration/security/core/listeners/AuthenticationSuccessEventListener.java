package com.orbis.configuration.security.core.listeners;

import java.util.Map;
import java.util.Optional;

import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.acegi.providers.dao.hibernate.UserLoggedIn;
import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.configuration.security.jwt.Attributable;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationDetails;
import com.orbis.web.content.disruptive.DisruptiveHelper;

/**
 * This listener reset failed user login attepts and updates the user login
 * statistics after successful login.
 */
@Component
public class AuthenticationSuccessEventListener
        implements ApplicationEventListener<AuthenticationSuccessEvent>
{

    @EventListener
    public void handle(AuthenticationSuccessEvent event)
    {
        UserDetailsImpl user = ((OrbisUserDetails) event.getAuthentication()
                .getPrincipal()).getOriginalUser();

        Optional<AuthenticationDetails> details = Optional
                .ofNullable(event.getAuthentication())
                .map(Authentication::getDetails)
                .filter(AuthenticationDetails.class::isInstance)
                .map(AuthenticationDetails.class::cast);

        if (user != null && details
                .map(AuthenticationDetails::isUpdateLoginStatistics).orElse(true))
        {
            UserLoggedIn uli = new UserLoggedIn();
            uli.setPortalUser(user);
            details.ifPresent(d -> uli.setRemoteAddress(d.getRemoteAddress()));
            Authentication authentication = event.getAuthentication();
            if (authentication instanceof Attributable attributable)
            {
                Map<String, Object> attributes = attributable.getAttributes();
                Object act = attributes.get("act");
                if (act instanceof Map<?, ?> actor)
                {
                    String name = (String) actor.get("name");
                    String subject = (String) actor.get("sub");
                    uli.setPrincipal(name + '(' + subject + ')');
                }

            }
            PortalUtils.getHt().save(uli);
            try
            {
                DisruptiveHelper.afterUserLoggedIn(user);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }
}
