package com.orbis.configuration.security.core;

import com.orbis.security.AuthenticationDetails;
import com.orbis.security.RecaptchaAuthentication;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationDetailsSource;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Represents the source of authentication details.
 */
@RequiredArgsConstructor
public class OrbisAuthenticationDetailsSource implements
        AuthenticationDetailsSource<HttpServletRequest, AuthenticationDetails>
{

    private final RecaptchaAuthentication recaptchaAuthentication;

    @Override
    public AuthenticationDetails buildDetails(HttpServletRequest context)
    {
        boolean captchaSolved = recaptchaAuthentication.isCaptchaSolved(
                RecaptchaAuthentication.getRecaptchaClientResponse(context));
        return new AuthenticationDetails(captchaSolved, context.getRemoteAddr(),
                true);
    }
}
