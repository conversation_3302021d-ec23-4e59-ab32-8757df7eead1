package com.orbis.configuration.security.core;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;

import java.io.IOException;

public class MultipartProcessingFilter extends OncePerRequestFilter
{

    @Autowired
    private MultipartResolver multipartResolver;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        if (multipartResolver.isMultipart(request))
        {
            try
            {
                MultipartHttpServletRequest multipartRequest = multipartResolver
                        .resolveMultipart(request);
                filterChain.doFilter(multipartRequest, response);
            }
            catch (Exception e)
            {
                logger.error("Failed to process multipart request", e);
                filterChain.doFilter(request, response);
            }
        }
        else
        {
            filterChain.doFilter(request, response);
        }
    }
}