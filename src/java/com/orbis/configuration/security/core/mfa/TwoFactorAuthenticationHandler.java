package com.orbis.configuration.security.core.mfa;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.RedirectAuthenticationFailureHandler;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.acegi.providers.dao.hibernate.UserLoginAttemptsService;
import com.orbis.acegi.providers.dao.hibernate.UserLoginFailed;
import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.configuration.security.core.utils.SecurityUtils;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.LocaleUtils;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class TwoFactorAuthenticationHandler
        implements AuthenticationSuccessHandler, AuthenticationFailureHandler
{

    private final AuthenticationSuccessHandler primarySuccessHandler;

    private final AuthenticationSuccessHandler secondarySuccessHandler;

    private final AuthenticationFailureHandler failureHandler;

    private final UserLoginAttemptsService loginAttemptsService;

    public TwoFactorAuthenticationHandler(PageResolver pageResolver,
            AuthenticationSuccessHandler primarySuccessHandler,
            UserLoginAttemptsService loginAttemptsService)
    {
        this.primarySuccessHandler = primarySuccessHandler;
        this.secondarySuccessHandler = new DynamicUrlAuthenticationSuccessHandler(
                pageResolver);
        this.failureHandler = new RedirectAuthenticationFailureHandler(
                pageResolver);
        this.loginAttemptsService = loginAttemptsService;
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request,
            HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException
    {
        OrbisUserDetails accountUserDetails = (OrbisUserDetails) authentication
                .getPrincipal();
        if (accountUserDetails.requiresMfa())
        {
            this.secondarySuccessHandler.onAuthenticationSuccess(request, response,
                    authentication);
        }
        else
        {
            this.primarySuccessHandler.onAuthenticationSuccess(request, response,
                    authentication);
        }
    }

    @Override
    public void onAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws ServletException, IOException
    {
        OrbisUserDetails userDetails = SecurityUtils.getCurrentUserDetails();
        String username = userDetails.getUsername();
        UserLoginFailed loginFailedPortalLog = createLoginFailedPortalLog(request,
                userDetails.getOriginalUser());

        loginAttemptsService.createUpdateUserMfaLoginAttempts(username);
        if (loginAttemptsService.ifMfaAttemptsLimitReached(username))
        {
            loginAttemptsService.lockUserAndResetAttempts(username);

            request.getSession().setAttribute(
                    WebAttributes.AUTHENTICATION_EXCEPTION,
                    new LockedException("Account is locked"));

            loginFailedPortalLog.setReasonOfFailure(new I18nLabel(
                    "i18n.TwoFactorAuthenticationHandler.MFAAttempt3373213261584042")
                    .getTranslation(LocaleUtils.getDefaultLocale()));
            loginFailedPortalLog.setL2ReasonOfFailure(new I18nLabel(
                    "i18n.TwoFactorAuthenticationHandler.MFAAttempt3373213261584042")
                    .getTranslation(LocaleUtils.getSecondaryLocale()));

            response.sendRedirect(response.encodeRedirectURL("/notLoggedIn.htm"));
        }
        else
        {
            loginFailedPortalLog.setReasonOfFailure(exception.getMessage());
            loginFailedPortalLog.setL2ReasonOfFailure(exception.getMessage());
            failureHandler.onAuthenticationFailure(request, response, exception);
        }
        PortalUtils.getHt().save(loginFailedPortalLog);
    }

    private UserLoginFailed createLoginFailedPortalLog(HttpServletRequest request,
            UserDetailsImpl user)
    {
        String browserSignature = request.getHeader("user-agent");
        UserLoginFailed loginFailedLog = new UserLoginFailed();
        loginFailedLog.setUserName(user.getUsername());
        loginFailedLog.setRemoteAddress(request.getRemoteAddr());
        loginFailedLog.setBrowserSignature(browserSignature);
        loginFailedLog.setPortalUser(user);
        return loginFailedLog;
    }
}
