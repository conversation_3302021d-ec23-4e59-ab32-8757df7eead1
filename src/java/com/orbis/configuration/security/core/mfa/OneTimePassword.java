package com.orbis.configuration.security.core.mfa;

import java.util.Collection;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

public record OneTimePassword(Authentication initial,
        String code) implements Authentication {

    @Override
    public Object getCredentials()
    {
        return this.code;
    }

    @Override
    public String getName()
    {
        return initial.getName();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities()
    {
        return initial.getAuthorities();
    }

    @Override
    public Object getDetails()
    {
        return initial.getDetails();
    }

    @Override
    public Object getPrincipal()
    {
        return initial.getPrincipal();
    }

    @Override
    public boolean isAuthenticated()
    {
        return initial.isAuthenticated();
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated)
            throws IllegalArgumentException
    {
        initial.setAuthenticated(isAuthenticated);
    }
}
