package com.orbis.configuration.security.core.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;

@RequiredArgsConstructor
public class DynamicUrlAuthenticationSuccessHandler
        extends SimpleUrlAuthenticationSuccessHandler
{

    private final PageResolver pageResolver;

    @Override
    protected String determineTargetUrl(HttpServletRequest request,
            HttpServletResponse response)
    {
        return pageResolver.resolvePage();
    }
}
