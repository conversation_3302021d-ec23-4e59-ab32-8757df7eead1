package com.orbis.configuration.security.core.mfa;

import lombok.RequiredArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

@Component
@RequiredArgsConstructor
@CommonsLog
public class StaticEmailTemplateResolver implements EmailTemplateResolver
{

    private static final Resource TEMPLATES_FOLDER = new ClassPathResource(
            "templates/");

    @Override
    @Cacheable("static-template")
    public String resolve(String name)
    {
        try
        {
            Resource resource = TEMPLATES_FOLDER.createRelative(name);
            try (InputStream inputStream = resource.getInputStream())
            {
                return new String(inputStream.readAllBytes());
            }
        }
        catch (IOException e)
        {
            throw new IllegalArgumentException("Email template not found: " + name,
                    e);
        }
    }
}
