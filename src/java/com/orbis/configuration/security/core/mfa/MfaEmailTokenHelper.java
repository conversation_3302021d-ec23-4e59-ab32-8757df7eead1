package com.orbis.configuration.security.core.mfa;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.utils.DateUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MfaEmailTokenHelper
{

    private static final String YEAR_TOKEN = "YEAR";

    // Pattern for [text with <TOKEN>]
    private static final Pattern CONDITIONAL_BLOCK_PATTERN =
            Pattern.compile("(\\s*)\\[([^\\[\\]]*<([^<>]+)>[^\\[\\]]*)](\\s*)");

    // Pattern for <TOKEN>
    private static final Pattern TOKEN_PATTERN =
            Pattern.compile("<([^<>]+)>");

    public static String processTemplate(String template)
    {
        Map<String, String> tokenValues = Map.of(
                PortalConfig.INSTITUTION_NAME,
                PortalConfigHelper.getOrbisValue(PortalConfig.INSTITUTION_NAME),
                PortalConfig.SITE_NAME,
                PortalConfigHelper.getOrbisValue(PortalConfig.SITE_NAME),
                YEAR_TOKEN,
                String.valueOf(DateUtils.getCurrentYear()));

        String result = template;

        result = processConditionalBlocks(result, tokenValues);

        result = replaceTokens(result, tokenValues);

        result = fixSpaces(result);

        return result;
    }

    private static String processConditionalBlocks(String text,
            Map<String, String> tokenValues)
    {
        Matcher matcher = CONDITIONAL_BLOCK_PATTERN.matcher(text);
        StringBuilder sb = new StringBuilder();

        while (matcher.find())
        {
            String leadingSpace = matcher.group(1);   // Space before [
            String blockContent = matcher.group(2);   // Content inside []
            String tokenName = matcher.group(3);      // Token name inside <>
            String trailingSpace = matcher.group(4);  // Space after ]

            String tokenValue = tokenValues.get(tokenName);

            if (isValidValue(tokenValue))
            {
                // Token has value, keep the block with one space padding
                String processedBlock = blockContent.replace("<" + tokenName + ">",
                        tokenValue);

                // Add appropriate spacing: single space if there was any space before/after
                String replacement = "";
                if (!leadingSpace.isEmpty())
                {
                    replacement += " ";
                }
                replacement += processedBlock;
                if (!trailingSpace.isEmpty())
                {
                    replacement += " ";
                }

                matcher.appendReplacement(sb,
                        Matcher.quoteReplacement(replacement));
            }
            else
            {
                // Token is null/empty, remove entire block including surrounding spaces
                matcher.appendReplacement(sb, "");
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private static String replaceTokens(String text,
            Map<String, String> tokenValues)
    {
        Matcher matcher = TOKEN_PATTERN.matcher(text);
        StringBuilder sb = new StringBuilder();

        while (matcher.find())
        {
            String tokenName = matcher.group(1);
            String tokenValue = tokenValues.getOrDefault(tokenName, "");
            matcher.appendReplacement(sb, Matcher.quoteReplacement(tokenValue));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private static boolean isValidValue(String value)
    {
        return value != null && !value.trim().isEmpty();
    }

    private static String fixSpaces(String text)
    {
        return text.replaceAll(" {2,}", " ");
    }
}