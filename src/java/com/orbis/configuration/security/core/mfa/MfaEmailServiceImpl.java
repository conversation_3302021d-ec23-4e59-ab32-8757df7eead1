package com.orbis.configuration.security.core.mfa;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.utils.EmailMessage;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.email.sendgrid.EmailSendGridHelper;

import lombok.RequiredArgsConstructor;

import java.util.Locale;

@Service
@RequiredArgsConstructor
public class MfaEmailServiceImpl implements MfaEmailService
{

    @Autowired
    private final EmailTemplateResolver emailTemplateResolver;

    private static final String SEND_CODE_TEMPLATE = "/mfa_code.html";

    private static final String MFA_ENABLED_TEMPLATE = "/mfa_enabled.html";

    private static final String MFA_DISABLED_TEMPLATE = "/mfa_disabled.html";

    private static final String EMAIL_FROM = "<EMAIL>";

    private static final String SENDER_NAME_EN = "Orbis Outcome [on behalf of <INSTITUTION_NAME>]";

    private static final String SENDER_NAME_FR = "Orbis Outcome [pour le compte de <INSTITUTION_NAME>]";

    @Override
    public void sendMfaCode(Locale locale, UserDetailsImpl user, String code)
    {
        String senderName = getProcessedSenderName(locale);
        String subject = getProcessedSubject(locale,
                "i18n.MfaEmailServiceImpl.Verificati6933790826725762");

        String body = getProcessedBody(locale, SEND_CODE_TEMPLATE);
        body = body.replace("${code}",
                EmailUtils.PASSWORD_START + code + EmailUtils.PASSWORD_END);

        EmailSendGridHelper.sendEmail(new EmailMessage(EMAIL_FROM, senderName, user,
                user.getEmailAddress(), user.getUsername(), subject, body, body,
                null, false, false));
    }

    @Override
    public void sendMfaEnabledDisabledEmail(Locale locale, UserDetailsImpl user,
            boolean mfaEnabled)
    {
        if (mfaEnabled)
        {
            sendMfaEnabledEmail(locale, user);
        }
        else
        {
            sendMfaDisabledEmail(locale, user);
        }
    }

    private void sendMfaEnabledEmail(Locale locale, UserDetailsImpl user)
    {
        String senderName = getProcessedSenderName(locale);
        String subject = getProcessedSubject(locale,
                "i18n.MfaEmailServiceImpl.Multifacto8882573889599899");
        String body = getProcessedBody(locale, MFA_ENABLED_TEMPLATE);

        EmailSendGridHelper.sendEmail(new EmailMessage(EMAIL_FROM, senderName, user,
                user.getEmailAddress(), user.getUsername(), subject, body, body,
                null, false, false));
    }

    private void sendMfaDisabledEmail(Locale locale, UserDetailsImpl user)
    {
        String senderName = getProcessedSenderName(locale);
        String subject = getProcessedSubject(locale,
                "i18n.MfaEmailServiceImpl.Multifacto5645869991677867");
        String body = getProcessedBody(locale, MFA_DISABLED_TEMPLATE);

        EmailSendGridHelper.sendEmail(new EmailMessage(EMAIL_FROM, senderName, user,
                user.getEmailAddress(), user.getUsername(), subject, body, body,
                null, false, false));
    }

    private String appendLanguagePath(Locale locale, String path)
    {
        return locale.getLanguage() + path;
    }

    private String getProcessedSubject(Locale locale, String i18n)
    {
        String siteName = PortalConfigHelper.getOrbisValue(PortalConfig.SITE_NAME);
        String subject = new I18nLabel(i18n, siteName).getTranslation(locale);
        if (StringUtils.isEmpty(siteName))
        {
            subject = subject.substring(subject.indexOf(" ") + 1);
        }
        return subject;
    }

    private String getProcessedSenderName(Locale locale)
    {
        String senderTemplate = LocaleUtils.isL1(locale)
                ? SENDER_NAME_EN
                : SENDER_NAME_FR;
        return MfaEmailTokenHelper.processTemplate(senderTemplate);
    }

    private String getProcessedBody(Locale locale, String templatePath)
    {
        String template = emailTemplateResolver.resolve(
                appendLanguagePath(locale, templatePath));
        return MfaEmailTokenHelper.processTemplate(template);
    }
}
