package com.orbis.configuration.security.core.mfa;

import java.util.concurrent.TimeUnit;

import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorConfig;

public class GoogleAuthenticatorDevice implements OneTimePasswordDevice
{

    public static final long TIME_STEP_SIZE_MS = TimeUnit.MINUTES.toMillis(10);

    protected String secret;

    private boolean confirmed;

    protected final GoogleAuthenticator authenticator;

    public GoogleAuthenticatorDevice()
    {
        GoogleAuthenticatorConfig build = new GoogleAuthenticatorConfig
                .GoogleAuthenticatorConfigBuilder()
                .setTimeStepSizeInMillis(TIME_STEP_SIZE_MS).build();
        this.authenticator = new GoogleAuthenticator(build);
        this.secret = authenticator.createCredentials().getKey();
        this.confirmed = false;
    }

    public GoogleAuthenticatorDevice(String secret)
    {
        GoogleAuthenticatorConfig build = new GoogleAuthenticatorConfig
                .GoogleAuthenticatorConfigBuilder()
                .setTimeStepSizeInMillis(TIME_STEP_SIZE_MS).build();
        this.authenticator = new GoogleAuthenticator(build);
        this.secret = secret;
        this.confirmed = false;
    }

    @Override
    public String secret()
    {
        return secret;
    }

    @Override
    public boolean confirm(String code)
    {
        confirmed = accepts(code);
        return confirmed;
    }

    @Override
    public boolean confirmed()
    {
        return confirmed;
    }

    @Override
    public boolean accepts(String code)
    {
        try {
            return authenticator.authorize(secret, Integer.parseInt(code));
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    public String generateCode()
    {
        return String.format("%06d", authenticator.getTotpPassword(secret));
    }
}
