package com.orbis.configuration.security.core.mfa;

import com.orbis.configuration.security.core.OrbisUserDetails;
import com.orbis.configuration.security.core.exception.InvalidOTPException;
import lombok.val;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.util.Optional;

public class OneTimePasswordAuthProvider implements AuthenticationProvider
{

    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException
    {
        val auth = (OneTimePassword) authentication;
        val user = (OrbisUserDetails) authentication.getPrincipal();
        val device = Optional.ofNullable(user.getDevice())
                .orElseThrow(() -> new IllegalStateException("No device attached"));

        if (user.requiresMfa() && !device.confirm(auth.getCredentials().toString()))
        {
            throw new InvalidOTPException(
                    "i18n.OneTimePasswordAuthProvider.IncorrectO6663635410078102");
        }
        return auth.initial();
    }

    @Override
    public boolean supports(Class<?> authentication)
    {
        return authentication == OneTimePassword.class;
    }
}
