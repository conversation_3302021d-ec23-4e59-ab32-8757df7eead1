package com.orbis.configuration.security.core.mfa;

import org.springframework.security.core.Authentication;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.val;

@RequiredArgsConstructor
public class OneTimePasswordConverter
{

    private static final String OTP_HEADER_NAME = "code";

    private final Authentication initial;

    public OneTimePassword convert(HttpServletRequest request)
    {
        val otp = request.getParameter(OTP_HEADER_NAME);

        if (otp == null || otp.isBlank())
        {
            return null;
        }

        return new OneTimePassword(initial, otp);
    }
}
