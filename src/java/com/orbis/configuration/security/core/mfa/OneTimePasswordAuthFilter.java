package com.orbis.configuration.security.core.mfa;

import java.io.IOException;

import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import com.orbis.configuration.security.core.OrbisUserDetails;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.val;
import lombok.extern.apachecommons.CommonsLog;

@CommonsLog
public class OneTimePasswordAuthFilter extends OncePerRequestFilter
{

    private final AuthenticationProvider authenticationProvider;

    private final TwoFactorAuthenticationHandler authenticationHandler;

    public OneTimePasswordAuthFilter(AuthenticationProvider authenticationProvider,
            TwoFactorAuthenticationHandler authenticationHandler)
    {
        this.authenticationProvider = authenticationProvider;
        this.authenticationHandler = authenticationHandler;

    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        val authentication = SecurityContextHolder.getContext().getAuthentication();
        if (request.getRequestURI().equals("/j_orbis_mfa_check")
                && authentication != null && authentication.isAuthenticated())
        {
            OrbisUserDetails principal = (OrbisUserDetails) authentication
                    .getPrincipal();
            if (principal.requiresMfa())
            {
                val otp = new OneTimePasswordConverter(authentication)
                        .convert(request);
                if (otp == null || otp.code().isBlank())
                {
                    log.debug("No OTP code provided. Username: "
                            + principal.getUsername());
                    authenticationHandler.onAuthenticationFailure(request, response,
                            new BadCredentialsException(
                                    "i18n.OneTimePasswordAuthFilter.NoOTPcodep5713592361944956"));
                    return;
                }
                try
                {
                    Authentication authenticate = authenticationProvider
                            .authenticate(otp);
                    SecurityContextHolder.getContext()
                            .setAuthentication(authenticate);
                    this.authenticationHandler.onAuthenticationSuccess(request,
                            response, authenticate);
                    return;
                }
                catch (AuthenticationException cause)
                {
                    authenticationHandler.onAuthenticationFailure(request, response,
                            new BadCredentialsException(cause.getMessage()));
                    return;
                }
            }
            else
            {
                log.info("User " + principal.getUsername() + " doesn't require a token.");
            }
        }

        filterChain.doFilter(request, response);
    }
}
