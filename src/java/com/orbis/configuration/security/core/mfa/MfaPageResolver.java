package com.orbis.configuration.security.core.mfa;

import com.orbis.portal.PortalUtils;
import com.orbis.web.site.SiteManager;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class MfaPageResolver implements PageResolver
{

    private final SiteManager siteManager;

    @Override
    public String resolvePage()
    {
        return PortalUtils.getMfaControllerElement(siteManager)
                .getFullPathWithFQDN();
    }
}
