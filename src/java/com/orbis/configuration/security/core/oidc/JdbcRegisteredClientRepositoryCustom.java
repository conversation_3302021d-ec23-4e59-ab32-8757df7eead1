package com.orbis.configuration.security.core.oidc;

import java.util.List;

import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.security.oauth2.server.authorization.client.JdbcRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;

public class JdbcRegisteredClientRepositoryCustom
        extends JdbcRegisteredClientRepository
{
    /**
     * Constructs a {@code JdbcRegisteredClientRepository} using the provided
     * parameters.
     *
     * @param jdbcOperations
     *            the JDBC operations
     */
    public JdbcRegisteredClientRepositoryCustom(JdbcOperations jdbcOperations)
    {
        super(jdbcOperations);
    }

    public List<RegisteredClient> findAll()
    {
        return getJdbcOperations().query("SELECT * from oauth2_registered_client",
                (rs, rowNum) -> getRegisteredClientRowMapper().mapRow(rs, rowNum));
    }

    public int delete(String id)
    {
        return getJdbcOperations()
                .update("delete from oauth2_registered_client where id=?", id);
    }

    public RegisteredClient findByClientSecret(String clientSecret)
    {
        try
        {
            return getJdbcOperations().queryForObject(
                    "SELECT * from oauth2_registered_client where client_secret=?",
                    (rs, rowNum) -> getRegisteredClientRowMapper().mapRow(rs,
                            rowNum),
                    clientSecret);
        }
        catch (EmptyResultDataAccessException e)
        {
            return null;
        }
    }

}
