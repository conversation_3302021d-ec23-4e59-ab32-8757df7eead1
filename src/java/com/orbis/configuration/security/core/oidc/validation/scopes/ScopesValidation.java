package com.orbis.configuration.security.core.oidc.validation.scopes;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.List;

@RequiredArgsConstructor
public class ScopesValidation
        implements ConstraintValidator<ValidScopes, List<String>>
{

    @Override
    public void initialize(ValidScopes scopes)
    {
    }

    @Override
    public boolean isValid(List<String> scopes, ConstraintValidatorContext cxt)
    {
        return scopes.stream().allMatch(ScopesValidation::validateScope);
    }

    private static boolean validateScope(String scope)
    {
        return scope != null && StringUtils.hasLength(scope.trim());
    }

}
