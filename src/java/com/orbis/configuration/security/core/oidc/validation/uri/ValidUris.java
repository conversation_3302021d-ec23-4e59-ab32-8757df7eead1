package com.orbis.configuration.security.core.oidc.validation.uri;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Constraint(validatedBy = UrisValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidUris {

    String message() default "i18n.ValidUris.Urisisinva6158846297041087";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
