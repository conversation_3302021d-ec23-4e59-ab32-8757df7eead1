package com.orbis.configuration.security.core.oidc.validation.scopes;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Constraint(validatedBy = ScopesValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidScopes {

    String message() default "i18n.ValidScopes.Scopesisin8942125684396272";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
