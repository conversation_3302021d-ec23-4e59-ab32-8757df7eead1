package com.orbis.configuration.security.core.oidc.validation.clientId;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Constraint(validatedBy = UniqueClientIdValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueClientId {

    String message() default "i18n.UniqueClientId.Notuniquec3549434754906226";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
