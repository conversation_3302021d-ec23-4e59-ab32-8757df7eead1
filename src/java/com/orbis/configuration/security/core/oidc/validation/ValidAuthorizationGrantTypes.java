package com.orbis.configuration.security.core.oidc.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Constraint(validatedBy = AuthorizationGrantTypesValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidAuthorizationGrantTypes {

    String message() default "i18n.ValidOAuth2Client.IfAuthoriz9791768566409150";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
