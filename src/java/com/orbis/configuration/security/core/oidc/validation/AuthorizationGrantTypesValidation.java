package com.orbis.configuration.security.core.oidc.validation;

import com.orbis.configuration.security.core.oidc.OAuth2ClientDTO;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class AuthorizationGrantTypesValidation implements
        ConstraintValidator<ValidAuthorizationGrantTypes, OAuth2ClientDTO>
{

    @Override
    public void initialize(ValidAuthorizationGrantTypes OAuth2Client)
    {
    }

    @Override
    public boolean isValid(OAuth2ClientDTO oAuth2Client,
            ConstraintValidatorContext cxt)
    {
        return !(oAuth2Client.getAuthorizationGrantTypes().contains(
                "authorization_code") && oAuth2Client.getRedirectUris().isEmpty());
    }
}
