package com.orbis.configuration.security.core.oidc;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.configuration.security.core.OrbisUserDetails;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationContext;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.function.Function;

@RequiredArgsConstructor
public class UserInfoResponseCustomizer
        implements Function<OidcUserInfoAuthenticationContext, OidcUserInfo>
{
    private final UserDetailsService userDetailsService;

    @Override
    public OidcUserInfo apply(
            OidcUserInfoAuthenticationContext oidcUserInfoAuthenticationContext)
    {
        final var authToken = ((JwtAuthenticationToken) SecurityContextHolder
                .getContext().getAuthentication());
        final var resourceOwnerUsername = authToken.getName();
        final var builder = OidcUserInfo.builder().subject(resourceOwnerUsername);
        final var userWithEmail = (OrbisUserDetails) userDetailsService
                .loadUserByUsername(resourceOwnerUsername);
        UserDetailsImpl originalUser = userWithEmail.getOriginalUser();
        if (authToken.getAuthorities().stream().anyMatch(
                authority -> authority.getAuthority().equals("SCOPE_email")))
        {
            builder.email(originalUser.getEmail());
        }

        // The profile scope requests access to the default profile claims,
        // which are: name, family_name,
        // given_name, middle_name, nickname,
        // preferred_username, profile, picture, website,
        // gender, birthdate, zoneinfo, locale, updated_at.
        if (authToken.getAuthorities().stream().anyMatch(
                authority -> authority.getAuthority().equals("SCOPE_profile")))
        {
            builder.email(originalUser.getEmail());
            builder.name(originalUser.getFirstName());
            builder.middleName(originalUser.getMiddleName());
            builder.gender(originalUser.getGender());
            builder.locale(originalUser.getUserLocale());
            // builder.updatedAt(originalUser.getDateUpdated());
            builder.zoneinfo(originalUser.getPreferredAddress());
        }

        if (authToken.getAuthorities().stream().anyMatch(
                authority -> authority.getAuthority().equals("SCOPE_phone")))
        {
            builder.phoneNumber(originalUser.getPhoneNumber());
        }
        return builder.build();
    }
}