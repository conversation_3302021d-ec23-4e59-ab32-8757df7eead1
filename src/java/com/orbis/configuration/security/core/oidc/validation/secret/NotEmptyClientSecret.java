package com.orbis.configuration.security.core.oidc.validation.secret;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Target(ElementType.TYPE)
@Constraint(validatedBy = NotEmptyClientSecretValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface NotEmptyClientSecret {

    String message() default "i18n.OAuth2ClientDTO.ClientSecr7327881885186248";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
