package com.orbis.configuration.security.core.oidc;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import com.orbis.configuration.common.ValidationException;
import com.orbis.configuration.common.ValidationUtils;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class OAuth2ClientServiceImpl implements OAuth2ClientService
{

    private final JdbcRegisteredClientRepositoryCustom jdbcRegisteredClientRepositoryCustom;

    private final ValidationUtils validator;

    @Override
    public OAuth2ClientDTO save(OAuth2ClientDTO oAuth2Client)
            throws ValidationException
    {
        validator.validate(oAuth2Client);

        if (oAuth2Client.getId() == null)
        {
            oAuth2Client.setId(UUID.randomUUID().toString());
        }

        RegisteredClient registeredClient = jdbcRegisteredClientRepositoryCustom
                .findById(oAuth2Client.getId());

        String clientSecret = registeredClient != null
                ? registeredClient.getClientSecret()
                : "{noop}" + oAuth2Client.getClientSecret();

        RegisteredClient.Builder builder = RegisteredClient
                .withId(oAuth2Client.getId()).clientId(oAuth2Client.getClientId())
                .clientName(oAuth2Client.getClientName()).clientSecret(clientSecret)
                .clientSettings(ClientSettings.builder()
                        .requireAuthorizationConsent(true).build());

        oAuth2Client.getRedirectUris().forEach(builder::redirectUri);
        oAuth2Client.getClientAuthenticationMethods().stream()
                .map(ClientAuthenticationMethod::new).collect(Collectors.toSet())
                .forEach(builder::clientAuthenticationMethod);

        oAuth2Client.getAuthorizationGrantTypes().stream()
                .map(AuthorizationGrantType::new).collect(Collectors.toSet())
                .forEach(builder::authorizationGrantType);

        oAuth2Client.getScopes().stream()
                .filter(org.springframework.util.StringUtils::hasLength)
                .forEach(it -> builder.scope(it.trim()));
        jdbcRegisteredClientRepositoryCustom.save(builder.build());
        return oAuth2Client;
    }

    @Override
    public List<OAuth2ClientDTO> findAll()
    {
        return jdbcRegisteredClientRepositoryCustom.findAll().stream()
                .map(OAuth2ClientDTO::of).toList();
    }

    @Override
    public void delete(String id)
    {
        Optional.ofNullable(findById(id)).ifPresent(
                it -> jdbcRegisteredClientRepositoryCustom.delete(it.getId()));
    }

    @Override
    public OAuth2ClientDTO findById(String id)
    {
        return Optional
                .ofNullable(jdbcRegisteredClientRepositoryCustom.findById(id))
                .map(OAuth2ClientDTO::of).orElse(null);
    }

    @Override
    public OAuth2ClientDTO findByClientId(String clientId)
    {
        return Optional
                .ofNullable(jdbcRegisteredClientRepositoryCustom
                        .findByClientId(clientId))
                .map(OAuth2ClientDTO::of).orElse(null);
    }

    @Override
    public OAuth2ClientDTO findByClientSecret(String clientSecret)
    {
        return Optional
                .ofNullable(jdbcRegisteredClientRepositoryCustom
                        .findByClientSecret("{noop}" + clientSecret))
                .map(OAuth2ClientDTO::of).orElse(null);
    }
}
