package com.orbis.configuration.security.core.oidc.validation.secret;

import com.orbis.configuration.security.core.oidc.OAuth2ClientDTO;
import com.orbis.configuration.security.core.oidc.OAuth2ClientService;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class UniqueClientSecretValidation
        implements ConstraintValidator<UniqueClientSecret, OAuth2ClientDTO>
{

    private final OAuth2ClientService oAuth2ClientService;

    @Override
    public void initialize(UniqueClientSecret OAuth2Client)
    {
    }

    @Override
    public boolean isValid(OAuth2ClientDTO oAuth2Client,
            ConstraintValidatorContext cxt)
    {
        OAuth2ClientDTO client = oAuth2ClientService
                .findByClientSecret(oAuth2Client.getClientSecret());
        return client == null || client.getId().equals(oAuth2Client.getId());
    }
}
