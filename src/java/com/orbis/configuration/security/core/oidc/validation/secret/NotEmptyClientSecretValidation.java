package com.orbis.configuration.security.core.oidc.validation.secret;

import com.orbis.configuration.security.core.oidc.OAuth2ClientDTO;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.StringUtils;

public class NotEmptyClientSecretValidation
        implements ConstraintValidator<NotEmptyClientSecret, OAuth2ClientDTO>
{
    @Override
    public void initialize(NotEmptyClientSecret constraintAnnotation)
    {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(OAuth2ClientDTO oAuth2ClientDTO,
            ConstraintValidatorContext constraintValidatorContext)
    {
        return StringUtils.hasLength(oAuth2ClientDTO.getClientSecret())
                || StringUtils.hasLength(oAuth2ClientDTO.getId());
    }
}
