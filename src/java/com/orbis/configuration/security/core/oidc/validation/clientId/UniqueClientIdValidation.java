package com.orbis.configuration.security.core.oidc.validation.clientId;

import com.orbis.configuration.security.core.oidc.OAuth2ClientDTO;
import com.orbis.configuration.security.core.oidc.OAuth2ClientService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class UniqueClientIdValidation
        implements ConstraintValidator<UniqueClientId, OAuth2ClientDTO>
{

    private final OAuth2ClientService oAuth2ClientService;

    public UniqueClientIdValidation(OAuth2ClientService oAuth2ClientService)
    {
        this.oAuth2ClientService = oAuth2ClientService;
    }

    @Override
    public void initialize(UniqueClientId OAuth2Client)
    {
    }

    @Override
    public boolean isValid(OAuth2ClientDTO oAuth2Client,
            ConstraintValidatorContext cxt)
    {
        OAuth2ClientDTO client = oAuth2ClientService
                .findByClientId(oAuth2Client.getClientId());
        return client == null || client.getId().equals(oAuth2Client.getId());
    }
}
