package com.orbis.configuration.security.core.oidc;

import com.orbis.configuration.common.ValidationException;

import java.util.List;

public interface OAuth2ClientService
{

    OAuth2ClientDTO save(OAuth2ClientDTO auth2ClientDTO) throws ValidationException;

    List<OAuth2ClientDTO> findAll();

    void delete(String id);

    OAuth2ClientDTO findById(String id);
    OAuth2ClientDTO findByClientId(String clientId);

    OAuth2ClientDTO findByClientSecret(String clientSecret);

}
