package com.orbis.configuration.security.core.oidc.validation.uri;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class UrisValidation implements ConstraintValidator<ValidUris, List<String>>
{

    @Override
    public void initialize(ValidUris uris)
    {
    }

    @Override
    public boolean isValid(List<String> uris, ConstraintValidatorContext cxt)
    {
        return !(uris.stream().noneMatch(u -> u.matches("^http[s]?://.*")) || uris
                .stream().map(UrisValidation::validateRedirectUri).anyMatch(s -> !s)
                || uris.isEmpty());
    }

    private static boolean validateRedirectUri(String redirectUri)
    {
        try
        {
            URI validRedirectUri = new URI(redirectUri);
            return validRedirectUri.getFragment() == null;
        }
        catch (URISyntaxException ex)
        {
            return false;
        }
    }

}
