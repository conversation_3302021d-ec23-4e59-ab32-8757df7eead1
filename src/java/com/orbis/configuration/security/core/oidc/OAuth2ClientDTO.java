package com.orbis.configuration.security.core.oidc;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.orbis.configuration.security.core.oidc.validation.secret.NotEmptyClientSecret;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.ServletRequestUtils;

import com.orbis.configuration.security.core.oidc.validation.ValidAuthorizationGrantTypes;
import com.orbis.configuration.security.core.oidc.validation.clientId.UniqueClientId;
import com.orbis.configuration.security.core.oidc.validation.secret.UniqueClientSecret;
import com.orbis.configuration.security.core.oidc.validation.scopes.ValidScopes;
import com.orbis.configuration.security.core.oidc.validation.uri.ValidUris;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ValidAuthorizationGrantTypes
@UniqueClientId
@NotEmptyClientSecret
@UniqueClientSecret
public class OAuth2ClientDTO implements Serializable
{

    private String id;

    @NotBlank(message = "i18n.OAuth2ClientDTO.ClientIdca5976756957778743")
    private String clientId;

    @NotBlank(message = "i18n.OAuth2ClientDTO.ClientName6666604587509859")
    private String clientName;

    private Date clientIdIssuedAt;

    private Date clientSecretExpiresAt;

    private String clientSecret;

    @ValidScopes
    private List<String> scopes;

    @NotEmpty(message = "i18n.OAuth2ClientDTO.Authentica3844377587691182")
    private List<String> clientAuthenticationMethods;

    @NotNull
    @NotEmpty(message = "i18n.OAuth2ClientDTO.Authorizat7226297999040079")
    private List<String> authorizationGrantTypes;

    @NotEmpty(message = "i18n.OAuth2ClientDTO.RedirectUR4655945832187126")
    @ValidUris
    private List<String> redirectUris;

    private List<String> postLogoutRedirectUris;

    public static OAuth2ClientDTO of(RegisteredClient registeredClient)
    {
        OAuth2ClientDTO view = new OAuth2ClientDTO();
        view.setId(registeredClient.getId());
        view.setClientId(registeredClient.getClientId());
        view.setClientName(registeredClient.getClientName());
        view.setClientIdIssuedAt(registeredClient.getClientIdIssuedAt() != null
                ? Date.from(registeredClient.getClientIdIssuedAt())
                : null);
        view.setClientSecretExpiresAt(
                registeredClient.getClientSecretExpiresAt() != null
                        ? Date.from(registeredClient.getClientSecretExpiresAt())
                        : null);
        view.setClientSecret(registeredClient.getClientSecret());
        view.setScopes(registeredClient.getScopes().stream().toList());
        view.setClientAuthenticationMethods(
                registeredClient.getClientAuthenticationMethods().stream()
                        .map(ClientAuthenticationMethod::getValue).toList());
        view.setAuthorizationGrantTypes(
                registeredClient.getAuthorizationGrantTypes().stream()
                        .map(AuthorizationGrantType::getValue).toList());
        view.setRedirectUris(registeredClient.getRedirectUris().stream().toList());
        view.setPostLogoutRedirectUris(
                registeredClient.getPostLogoutRedirectUris().stream().toList());
        return view;
    }

    public static OAuth2ClientDTO of(HttpServletRequest request)
    {

        String id = Optional.ofNullable(request.getParameter("id"))
                .filter(StringUtils::hasLength).orElse(null);
        OAuth2ClientDTO oAuth2ClientView = new OAuth2ClientDTO();
        oAuth2ClientView.setId(id);
        oAuth2ClientView.setClientId(request.getParameter("clientId"));
        oAuth2ClientView.setClientName(request.getParameter("clientName"));
        oAuth2ClientView.setClientSecret(request.getParameter("clientSecret"));

        Optional<String[]> uris = Optional
                .ofNullable(request.getParameterMap().get("redirectUris"));
        oAuth2ClientView.setRedirectUris(
                uris.map(Arrays::asList).orElse(Collections.emptyList()));
        Optional<String[]> methods = Optional.ofNullable(
                request.getParameterMap().get("clientAuthenticationMethods"));
        oAuth2ClientView.setClientAuthenticationMethods(
                methods.map(Arrays::asList).orElse(Collections.emptyList()));
        Optional<String[]> types = Optional.ofNullable(
                request.getParameterMap().get("authorizationGrantTypes"));

        oAuth2ClientView.setAuthorizationGrantTypes(
                types.map(Arrays::asList).orElse(Collections.emptyList()));

        String scope = ServletRequestUtils.getStringParameter(request, "scopes",
                "");
        oAuth2ClientView.setScopes(Arrays.stream(scope.split(","))
                .filter(org.springframework.util.StringUtils::hasLength).toList());

        return oAuth2ClientView;
    }

}
