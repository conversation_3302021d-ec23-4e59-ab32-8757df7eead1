package com.orbis.configuration.security.core.oidc;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.List;
import java.util.UUID;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.jackson2.SecurityJackson2Modules;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.client.JdbcRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.jackson2.OAuth2AuthorizationServerJackson2Module;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import com.orbis.configuration.common.ValidationUtils;

import lombok.RequiredArgsConstructor;
import lombok.val;

@Configuration
@RequiredArgsConstructor
public class AuthorizationServerConfiguration
{

    private final AuthenticationEntryPoint orbisAuthenticationProcessingFilterEntryPoint;

    @Bean
    @Order(1)
    public SecurityFilterChain authorizationServerSecurityFilterChain(
            HttpSecurity http) throws Exception
    {
        OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
        http.getConfigurer(OAuth2AuthorizationServerConfigurer.class)
                .oidc(customizer -> customizer.userInfoEndpoint(endpoint -> endpoint
                        .userInfoMapper(userInfoResponseCustomizer(null))));
        http.anonymous(AbstractHttpConfigurer::disable)
                .exceptionHandling(
                        exceptions -> exceptions.defaultAuthenticationEntryPointFor(
                                orbisAuthenticationProcessingFilterEntryPoint,
                                new MediaTypeRequestMatcher(MediaType.TEXT_HTML)))
                // Accept access tokens for User Info and/or Client Registration
                .oauth2ResourceServer(resourceServer -> resourceServer
                        .jwt(Customizer.withDefaults()));
        return http.build();
    }

    @Bean
    public JdbcRegisteredClientRepositoryCustom registeredClientRepository(
            JdbcOperations jdbcOperations)
    {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(
                new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
        ClassLoader classLoader = JdbcRegisteredClientRepository.class
                .getClassLoader();
        List<Module> securityModules = SecurityJackson2Modules
                .getModules(classLoader);
        mapper.registerModules(securityModules);
        mapper.registerModule(new OAuth2AuthorizationServerJackson2Module());
        val jdbcRegisteredClientRepositoryCustom = new JdbcRegisteredClientRepositoryCustom(
                jdbcOperations);
        val registeredClientRowMapper = new JdbcRegisteredClientRepository.RegisteredClientRowMapper();
        registeredClientRowMapper.setObjectMapper(mapper);
        jdbcRegisteredClientRepositoryCustom
                .setRegisteredClientRowMapper(registeredClientRowMapper);
        return jdbcRegisteredClientRepositoryCustom;
    }

    @Bean
    public JWKSource<SecurityContext> jwkSource()
    {
        KeyPair keyPair = generateRsaKey();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        RSAKey rsaKey = new RSAKey.Builder(publicKey).privateKey(privateKey)
                .keyID(UUID.randomUUID().toString()).build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }

    private static KeyPair generateRsaKey()
    {
        KeyPair keyPair;
        try
        {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            keyPair = keyPairGenerator.generateKeyPair();
        }
        catch (Exception ex)
        {
            throw new IllegalStateException(ex);
        }
        return keyPair;
    }

    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource)
    {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    @Bean
    public AuthorizationServerSettings authorizationServerSettings()
    {
        return AuthorizationServerSettings.builder().build();
    }

    @Bean
    public UserInfoResponseCustomizer userInfoResponseCustomizer(
            UserDetailsService userDetailsService)
    {
        return new UserInfoResponseCustomizer(userDetailsService);
    }

    @Bean
    public OAuth2ClientService oAuth2ClientService(
            JdbcRegisteredClientRepositoryCustom jdbcRegisteredClientRepositoryCustom,
            ValidationUtils validationUtils)
    {
        return new OAuth2ClientServiceImpl(jdbcRegisteredClientRepositoryCustom,
                validationUtils);
    }

}
