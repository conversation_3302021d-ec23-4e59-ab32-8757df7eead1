package com.orbis.configuration.security.core.oidc.validation.secret;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Constraint(validatedBy = UniqueClientSecretValidation.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueClientSecret {

    String message() default "i18n.UniqueClientSecret.Notuniquec6633410377334083";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
