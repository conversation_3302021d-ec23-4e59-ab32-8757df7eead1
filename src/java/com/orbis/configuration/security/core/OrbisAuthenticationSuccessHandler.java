package com.orbis.configuration.security.core;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.acegi.providers.dao.hibernate.UserLoginAttemptsService;
import com.orbis.security.AuthenticationUtils;
import com.orbis.utils.LocaleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Class that handles the authentication success event. Sets the session timeout
 * and locale, then redirects to the requested page(See
 * {@link SavedRequestAwareAuthenticationSuccessHandler}).
 */
public class OrbisAuthenticationSuccessHand<PERSON>
        extends SavedRequestAwareAuthenticationSuccessHandler
{

    @Autowired
    private UserLoginAttemptsService loginAttemptsService;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request,
            HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException
    {

        super.onAuthenticationSuccess(request, response, authentication);
        UserDetailsImpl user = ((OrbisUserDetails) authentication.getPrincipal())
                .getOriginalUser();
        AuthenticationUtils.initializeSessionTimeout(request, user);
        LocaleUtils.changeSiteLocale(request, response, request.getServletContext(),
                user.getUserLocale());
        loginAttemptsService.resetLoginAttempts(user.getUsername());
    }
}
