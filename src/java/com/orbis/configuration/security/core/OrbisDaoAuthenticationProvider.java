package com.orbis.configuration.security.core;

import com.orbis.acegi.providers.dao.hibernate.UserLoginAttemptsService;
import com.orbis.configuration.security.core.exception.CaptchaException;
import com.orbis.security.AuthenticationDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * This class is used to load the security user details from the database.
 * See parent class {@link DaoAuthenticationProvider} for more information.
 */
public class OrbisDaoAuthenticationProvider extends DaoAuthenticationProvider {

    @Autowired
    private UserLoginAttemptsService loginAttemptsService;

    /**
     * This method provides additional Authentication checks.
     * Verifies if password is correct. See {@link DaoAuthenticationProvider}.
     * If the account is locked and captcha is not solved throws a {@link LockedException}.
     * @param userDetails the user details
     * @param authentication the authentication
     * @throws AuthenticationException if the account is locked and the captcha is not solved
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
            UsernamePasswordAuthenticationToken authentication)
            throws AuthenticationException
    {
        super.additionalAuthenticationChecks(userDetails, authentication);

        if (loginAttemptsService.hasFailedLoginAttempts(
                userDetails.getUsername()) && !isCaptchaSolved(authentication))
        {
            throw new CaptchaException("account is locked");
        }
    }

    protected boolean isCaptchaSolved(Authentication auth)
    {
        return auth
                .getDetails() instanceof AuthenticationDetails authenticationdetails
                && authenticationdetails.isCaptchaSolved();
    }
}
