package com.orbis.configuration.security.core.repository;

import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.stereotype.Repository;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.TypedQuery;

@Repository
public class UserRepositoryImpl implements UserRepository
{
    private final EntityManager entityManager;

    public UserRepositoryImpl(EntityManagerFactory entityManagerFactory)
    {
        this.entityManager = SharedEntityManagerCreator
                .createSharedEntityManager(entityManagerFactory);
    }

    @Override
    public UserDetailsImpl findByUsername(String username)
    {
        TypedQuery<UserDetailsImpl> query = entityManager.createQuery(
                "from UserDetailsImpl u where u.username=:username",
                UserDetailsImpl.class);
        query.setParameter("username", username);
        return query.getSingleResult();
    }
}
