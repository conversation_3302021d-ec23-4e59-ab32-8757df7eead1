package com.orbis.configuration.security.core;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.web.bind.ServletRequestUtils;

import com.orbis.acegi.providers.dao.hibernate.NotApprovedException;
import com.orbis.acegi.providers.dao.hibernate.UserLoginAttemptsService;
import com.orbis.acegi.providers.dao.hibernate.UserLoginFailed;
import com.orbis.configuration.security.core.exception.CaptchaException;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.site.SiteManager;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;

/**
 * This class is used to handle the authentication failure, adds messages to be
 * displayed to the user and make a decision where to redirect failed
 * authentication.
 */
@CommonsLog
@RequiredArgsConstructor
public class OrbisAuthenticationFailureHandler
        implements AuthenticationFailureHandler
{

    private static final String ACCOUNT_LOCKED_MESSAGE = "i18n.OrbisAuthenticationFailureHandler.Accounthas7068200651568116";

    @Autowired
    private UserLoginAttemptsService loginAttemptsService;

    private final SiteManager siteManager;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException failed)
            throws IOException
    {
        if (log.isDebugEnabled())
        {
            log.debug("Authentication request failed: " + failed.toString());
        }

        AuthenticationException exception = failed;

        String userName = request.getParameter("j_username");
        String browserSignature = request.getHeader("user-agent");

        UserLoginFailed loginFailed = new UserLoginFailed();
        loginFailed.setUserName(userName);
        loginFailed.setRemoteAddress(request.getRemoteAddr());
        loginFailed.setBrowserSignature(browserSignature);
        loginFailed.setPortalUser(UserDetailsHelper.getUserByUsername(userName));
        if (failed instanceof UsernameNotFoundException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Usernameno1863097455748982",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Usernameno1863097455748982",
                    LocaleUtils.getSecondaryLocale()));

        }
        else if (failed instanceof BadCredentialsException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.IncorrectP6626144918829673",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.IncorrectP6626144918829673",
                    LocaleUtils.getSecondaryLocale()));
            loginAttemptsService.createUpdateUserPasswordLoginAttempts(userName);
            if (loginAttemptsService.ifLoginAttemptsLimitReached(userName))
            {
                loginAttemptsService.lockUserAndResetAttempts(userName);
                exception = new LockedException(ACCOUNT_LOCKED_MESSAGE);
            }
        }
        else if (failed instanceof CaptchaException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Recaptchan7128079725442347",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Recaptchan7128079725442347",
                    LocaleUtils.getSecondaryLocale()));
            loginAttemptsService.createUpdateUserPasswordLoginAttempts(userName);
            if (loginAttemptsService.ifLoginAttemptsLimitReached(userName))
            {
                loginAttemptsService.lockUserAndResetAttempts(userName);
                exception = new LockedException(ACCOUNT_LOCKED_MESSAGE);
            }
        }
        else if (failed instanceof LockedException)
        {

            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    ACCOUNT_LOCKED_MESSAGE, LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    ACCOUNT_LOCKED_MESSAGE, LocaleUtils.getSecondaryLocale()));

        }
        else if (failed instanceof DisabledException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun9732231576429054",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun9732231576429054",
                    LocaleUtils.getSecondaryLocale()));
        }
        else if (failed instanceof NotApprovedException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun7472924800749692",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun7472924800749692",
                    LocaleUtils.getSecondaryLocale()));
        }
        else if (failed instanceof AccountExpiredException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun2848296146299172",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Useraccoun2848296146299172",
                    LocaleUtils.getSecondaryLocale()));
        }
        else if (failed instanceof CredentialsExpiredException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Usercreden9169242484019373",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Usercreden9169242484019373",
                    LocaleUtils.getSecondaryLocale()));
        }
        else if (failed instanceof AuthenticationServiceException)
        {
            loginFailed.setReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Authentica8425893616845406",
                    LocaleUtils.getDefaultLocale()));
            loginFailed.setL2ReasonOfFailure(PortalUtils.getI18nMessage(
                    "i18n.OrbisAuthenticationProcessingFilter.Authentica8425893616845406",
                    LocaleUtils.getSecondaryLocale()));
        }
        PortalUtils.getHt().save(loginFailed);

        request.getSession().setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION,
                exception);

        String redirectURL;

        if (failed instanceof DisabledException)
        {
            redirectURL = PortalUtils.PORTAL_SUSPENDED_PATH;
        }
        else if (failed instanceof NotApprovedException)
        {
            redirectURL = PortalUtils.PORTAL_NOTAPPROVED_PATH;
        }
        else
        {
            redirectURL = ServletRequestUtils.getStringParameter(request,
                    "loginPageUrl",
                    PortalUtils.getLoginControllerElement(this.siteManager)
                            .getFullPathWithFQDN());
        }

        response.sendRedirect(response.encodeRedirectURL(redirectURL));
    }
}
