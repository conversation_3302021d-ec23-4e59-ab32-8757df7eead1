package com.orbis.configuration.security.core;

import com.orbis.portal.PortalUtils;
import com.orbis.web.site.SiteElement;
import org.springframework.security.web.util.matcher.RequestMatcher;

import jakarta.servlet.http.HttpServletRequest;

/**
 * This Request Matcher is used to determine if not logged user can access a site element.
 */
public class SiteElementRestrictedRequestMatcher implements RequestMatcher
{
    @Override
    public boolean matches(HttpServletRequest request)
    {
        return request.getParameter("edit") != null
                || request.getParameter("mode") != null
                || PortalUtils.getUriElements(request).stream()
                        .anyMatch(SiteElement::isLoginRequired);
    }
}
