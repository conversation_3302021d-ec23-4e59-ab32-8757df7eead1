package com.orbis.configuration;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.sql.DataSource;

import org.hibernate.cfg.AvailableSettings;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.context.support.ServletContextResource;

import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.HibernateLegacyQueryTransformer;
import com.orbis.portal.OrbisJPATemplate;
import com.orbis.portal.OrbisJdbcTemplate;
import com.orbis.portal.PortalUtils2;
import com.orbis.spring.orm.hibernate.OrbisDataSource;
import com.orbis.utils.StringUtils;

import jakarta.persistence.EntityManagerFactory;
import jakarta.servlet.ServletContext;
import lombok.val;

@Configuration
public class HibernateConfiguration
{

    private final List<String> mappingResources;

    public HibernateConfiguration(ResourceLoader resourceLoader) throws IOException
    {
        Resource[] resources = ResourcePatternUtils
                .getResourcePatternResolver(resourceLoader)
                .getResources("WEB-INF/**/*.orm.xml");
        mappingResources = new ArrayList<>();
        for (Resource resource : resources)
        {
            ServletContextResource servletContextResource = (ServletContextResource) resource;
            mappingResources.add(servletContextResource.getPath().substring(17));
        }
    }

    @Bean
    public HibernateLegacyQueryTransformer hibernateLegacyQueryTransformer()
    {
        return new HibernateLegacyQueryTransformer();
    }

    @Bean
    @Primary
    public CommandQueryTemplate hibernateTemplate(
            EntityManagerFactory writeEntityManagerFactory) throws Exception
    {
        return new OrbisJPATemplate(writeEntityManagerFactory,
                hibernateLegacyQueryTransformer());
    }

    @Bean
    @Primary
    @DependsOn("portalUtils2")
    @Order(10)
    public LocalContainerEntityManagerFactoryBean writeEntityManagerFactory(
            DataSource dataSource, ServletContext servletContext) throws IOException
    {
        val properties = new Properties();

        properties.put(AvailableSettings.USE_SECOND_LEVEL_CACHE, Boolean.TRUE);
        properties.put(AvailableSettings.DIALECT,
                "org.hibernate.OrbisClassicSQLServerDialect");
        properties.put(AvailableSettings.CURRENT_SESSION_CONTEXT_CLASS, "thread");
        properties.put(AvailableSettings.FORMAT_SQL, Boolean.TRUE);
        properties.put(AvailableSettings.HBM2DDL_AUTO,
                PortalUtils2.hibernateDDLAuto());
        properties.put(AvailableSettings.MAX_FETCH_DEPTH, "0");
        properties.put(AvailableSettings.ISOLATION, "1");
        properties.put(AvailableSettings.SHOW_SQL, "false");
        properties.put(AvailableSettings.IMPLICIT_NAMING_STRATEGY, "legacy-hbm");
        properties.put(AvailableSettings.ENABLE_LAZY_LOAD_NO_TRANS, "true");

        properties.put("hibernate.javax.cache.missing_cache_strategy", "create");
        properties.put("hibernate.javax.cache.provider",
                "org.ehcache.jsr107.EhcacheCachingProvider");
        properties.put("hibernate.javax.cache.uri", "file:"
                + servletContext.getResource("WEB-INF/conf/ehcache.xml").getPath());

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setPackagesToScan("com.orbis");

        factory.setMappingResources(mappingResources.toArray(new String[0]));
        factory.setDataSource(dataSource);
        factory.setJpaProperties(properties);
        // factory.afterPropertiesSet();
        return factory;
    }

    @Bean
    @Primary
    public PlatformTransactionManager jpaTransactionManager(
            EntityManagerFactory entityManagerFactory)
    {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        return transactionManager;
    }

    @Bean
    @Conditional(AzureConditional.class)
    public DataSource readOnlyDataSource(OrbisDataSource dataSource)
    {
        OrbisDataSource readOnlyDataSource = new OrbisDataSource();
        readOnlyDataSource.setJdbcUrl(
                dataSource.getJdbcUrl() + ";ApplicationIntent=ReadOnly");
        readOnlyDataSource.setUsername(dataSource.getUsername());
        readOnlyDataSource
                .setPassword(StringUtils.DES_encrypt(dataSource.getPassword()));
        readOnlyDataSource
                .setTransactionIsolation(dataSource.getTransactionIsolation());
        readOnlyDataSource.setMinimumIdle(dataSource.getMinimumIdle());
        readOnlyDataSource.setMaximumPoolSize(dataSource.getMaximumPoolSize());
        readOnlyDataSource.setIdleTimeout(dataSource.getIdleTimeout());
        readOnlyDataSource.setReadOnly(true);
        readOnlyDataSource.setMaxLifetime(dataSource.getMaxLifetime());
        return readOnlyDataSource;
    }

    @Bean
    @Conditional(AzureConditional.class)
    public OrbisJdbcTemplate readOnlyJdbcTemplate()
    {
        return new OrbisJdbcTemplate(readOnlyDataSource(null));
    }

    @Bean
    @Conditional(AzureConditional.class)
    @Order(20)
    public LocalContainerEntityManagerFactoryBean readOnlyEntityManagerFactory(
            ServletContext servletContext, @Qualifier("readOnlyDataSource")
            DataSource readOnlyDataSource) throws MalformedURLException
    {
        val properties = new Properties();

        properties.put(AvailableSettings.USE_SECOND_LEVEL_CACHE, Boolean.TRUE);
        properties.put(AvailableSettings.DIALECT,
                "org.hibernate.OrbisClassicSQLServerDialect");
        properties.put(AvailableSettings.CURRENT_SESSION_CONTEXT_CLASS, "thread");
        properties.put(AvailableSettings.FORMAT_SQL, Boolean.TRUE);
        properties.put(AvailableSettings.HBM2DDL_AUTO, "none");
        properties.put(AvailableSettings.MAX_FETCH_DEPTH, "0");
        properties.put(AvailableSettings.ISOLATION, "1");
        properties.put(AvailableSettings.SHOW_SQL, "false");
        properties.put(AvailableSettings.IMPLICIT_NAMING_STRATEGY, "legacy-hbm");
        properties.put(AvailableSettings.ENABLE_LAZY_LOAD_NO_TRANS, "true");

        properties.put("hibernate.javax.cache.missing_cache_strategy", "create");
        properties.put("hibernate.javax.cache.provider",
                "org.ehcache.jsr107.EhcacheCachingProvider");
        properties.put("hibernate.javax.cache.uri", "file:"
                + servletContext.getResource("WEB-INF/conf/ehcache.xml").getPath());

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setPackagesToScan("com.orbis");
        factory.setDataSource(readOnlyDataSource);
        factory.setMappingResources(mappingResources.toArray(new String[0]));

        factory.setJpaProperties(properties);
        return factory;
    }

    @Bean
    @Conditional(AzureConditional.class)
    public CommandQueryTemplate readOnlyHibernateTemplate() throws Exception
    {
        return new OrbisJPATemplate(
                readOnlyEntityManagerFactory(null, readOnlyDataSource(null))
                        .getObject(),
                hibernateLegacyQueryTransformer());
    }

    @Bean
    @Conditional(AzureConditional.class)
    public PlatformTransactionManager readOnlyJpaTransactionManager()
            throws Exception
    {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(
                readOnlyEntityManagerFactory(null, readOnlyDataSource(null))
                        .getObject());
        return transactionManager;
    }

}
