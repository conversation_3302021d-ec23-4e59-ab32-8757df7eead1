package com.orbis.configuration.ckfinder;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import com.cksource.ckfinder.config.Config;
import com.cksource.ckfinder.filesystem.Backend;
import com.cksource.ckfinder.filesystem.BackendFactory;

@Component
@Primary
@Scope(value = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
public class CustomBackendFactory extends BackendFactory
{
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Backend getBackend(String backendName)
    {
        return instances.computeIfAbsent(backendName, name -> {
            Config.Backend config = Optional
                    .ofNullable(this.config.getBackendConfig(name)).orElseThrow(
                            () -> new IllegalArgumentException("Backend with name "
                                    + name + " not found in configuration."));

            return new CKFinderOrbisBackend(config,
                    this.fileSystemFactory.getFileSystem(config.getAdapter(),
                            config.getFileSystemOptions()),
                    this.acl,
                    applicationContext.getBean(FileAttributeService.class));
        });
    }
}
