package com.orbis.configuration.ckfinder;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.WebApplicationInitializer;

import com.cksource.ckfinder.servlet.CKFinderServlet;

import jakarta.servlet.FilterRegistration;
import jakarta.servlet.MultipartConfigElement;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletRegistration;

@Configuration
@Order(1)
public class CKFinderWebConfiguration implements WebApplicationInitializer
{

    @Override
    public void onStartup(ServletContext servletContext)
    {
        ServletRegistration.Dynamic dispatcher = servletContext
                .addServlet("CKFinderServlet", new CKFinderServlet());
        dispatcher.setLoadOnStartup(1);
        dispatcher.addMapping("/core/ckfinder/core/connector/java/connector.java",
                "/ckfinder/connector");
        dispatcher.setInitParameter("scan-path",
                "com.orbis.configuration.ckfinder");
        FilterRegistration.Dynamic filter = servletContext
                .addFilter("x-content-options", new CKFinderFilter());
        filter.addMappingForUrlPatterns(null, false, "/content/documents/**");
        MultipartConfigElement multipartConfigElement = new MultipartConfigElement(
                "", 5242880, 20971520, 0);
        dispatcher.setMultipartConfig(multipartConfigElement);
    }
}
