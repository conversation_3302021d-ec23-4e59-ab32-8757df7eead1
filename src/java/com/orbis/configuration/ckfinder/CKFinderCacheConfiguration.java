package com.orbis.configuration.ckfinder;

import java.io.IOException;

import javax.cache.CacheManager;
import javax.cache.Caching;
import javax.cache.spi.CachingProvider;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableCaching
@EnableScheduling
public class CKFinderCacheConfiguration
{

    @Bean(name = "ckFinderCacheManager")
    public org.springframework.cache.CacheManager ckFinderCacheManager()
            throws IOException
    {
        CachingProvider cachingProvider = Caching
                .getCachingProvider("org.ehcache.jsr107.EhcacheCachingProvider");

        CacheManager jsr107CacheManager = cachingProvider.getCacheManager(
                new ClassPathResource("spring-ehcache.xml",
                        this.getClass().getClassLoader()).getURI(),
                getClass().getClassLoader());

        return new JCacheCacheManager(jsr107CacheManager);
    }
}
