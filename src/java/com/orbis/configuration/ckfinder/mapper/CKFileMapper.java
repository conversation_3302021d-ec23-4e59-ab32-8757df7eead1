package com.orbis.configuration.ckfinder.mapper;

import java.nio.file.Path;
import java.nio.file.attribute.FileTime;

import com.cksource.ckfinder.filesystem.node.File;
import com.orbis.configuration.ckfinder.dto.FileAttributesDto;

import lombok.experimental.UtilityClass;

@UtilityClass
public class CKFileMapper
{

    public static File createFileFromPath(Path path,
            FileAttributesDto fileAttributes)
    {
        return new File().setName(path.getFileName().toString())
                .setSize(fileAttributes.size()).setLastModifiedTime(
                        FileTime.fromMillis(fileAttributes.lastModifiedTime()));
    }
}
