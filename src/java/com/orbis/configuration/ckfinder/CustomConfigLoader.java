package com.orbis.configuration.ckfinder;

import java.io.InputStream;
import java.nio.file.Files;

import com.orbis.portal.CkLicenseManager;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import com.cksource.ckfinder.config.Config;
import com.cksource.ckfinder.config.loader.ConfigLoader;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CustomConfigLoader implements ConfigLoader
{

    private final ResourceLoader resourceLoader;

    @Override
    public Config loadConfig() throws Exception
    {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        Resource resource = resourceLoader
                .getResource("/site/private/conf/ckfinder.yml");
        try (InputStream inputStream = Files
                .newInputStream(resource.getFile().toPath()))
        {
            String rootPath = resourceLoader.getResource("/").getFile()
                    .getAbsolutePath();
            Config config = mapper.readValue(inputStream, Config.class);
            config.getBackends().forEach((key, value) -> value
                    .setRoot(rootPath + "/content/documents/"));
            config.setLicenseKey(CkLicenseManager.getCkFinderLicense());
            return config;
        }
    }
}
