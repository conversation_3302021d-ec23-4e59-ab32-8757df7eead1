package com.orbis.configuration.ckfinder;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.cksource.ckfinder.acl.Permission;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.orbis.portal.PortalUtils;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.cksource.ckfinder.filesystem.node.Folder;
import com.orbis.configuration.ckfinder.dto.FileAttributesDto;

import lombok.extern.apachecommons.CommonsLog;

@Service
@CommonsLog
public class FileAttributeService
{

    protected static final char[] ESCAPABLE_CHARS = "^$[]{}<>+-=()!|".toCharArray();

    protected static final String[] DEFAULT_HIDE_FOLDERS = new String[] { ".svn",
            "CVS", "_thumbs" };

    protected static final String CKFINDER_YAML_PATH = "/site/private/conf/ckfinder.yml";

    protected static final int ALL_PERMISSIONS = Permission.FOLDER_VIEW.getValue() | Permission.FOLDER_CREATE.getValue()
            | Permission.FOLDER_RENAME.getValue() | Permission.FOLDER_DELETE.getValue()
            | Permission.FILE_VIEW.getValue() | Permission.FILE_CREATE.getValue()
            | Permission.FILE_RENAME.getValue() | Permission.FILE_DELETE.getValue()
            | Permission.IMAGE_RESIZE.getValue() | Permission.IMAGE_RESIZE_CUSTOM.getValue();

    @Autowired
    private ResourceLoader resourceLoader;

    private Pattern hideFoldersPattern;

    private final Map<String, String> resourcePaths = new HashMap<>();

    @Getter
    private int defaultConfigPermission = ALL_PERMISSIONS;

    @PostConstruct
    public void init()
    {
        try
        {
            // Load configuration during initialization
            log.info("Loading CKFinder configuration from YAML...");
            loadConfigFromYaml();
            log.info("CKFinder configuration loaded successfully");
        }
        catch (Exception e)
        {
            log.error("Failed to load CKFinder configuration", e);
            // Set up defaults to ensure the application can still function
            setupDefaultConfiguration();
        }
    }

    /**
     * Sets up default configuration if loading from YAML fails
     */
    private void setupDefaultConfiguration()
    {
        // Default hideFolders
        List<String> defaultHideFolders = Arrays.asList(DEFAULT_HIDE_FOLDERS);
        this.hideFoldersPattern = Pattern.compile(
                buildHiddenNodePattern(defaultHideFolders));
        log.info("Using default hideFolders configuration: " + defaultHideFolders);

        // Default resource paths
        resourcePaths.clear();
        setupDefaultResourcePaths();

        // Default access control - allow everything for all users
        // Set default permissions to ALL_PERMISSIONS
        defaultConfigPermission = ALL_PERMISSIONS;
        log.info(
                "Using default access control configuration: " + defaultConfigPermission);
    }

    /**
     * Loads configuration from ckfinder.yml file
     */
    private void loadConfigFromYaml() throws IOException
    {
        Resource resource = resourceLoader.getResource(CKFINDER_YAML_PATH);
        try (InputStream inputStream = resource.getInputStream())
        {
            ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
            JsonNode rootNode = mapper.readTree(inputStream);

            // Load hideFolders configuration
            loadHideFoldersConfig(rootNode);

            // Load resource types configuration
            loadResourceTypesConfig(rootNode);

            // Load access control configuration
            loadAccessControlConfig(rootNode);
        }
        catch (IOException e)
        {
            log.error("Error reading CKFinder configuration file", e);
            throw e;
        }
    }

    /**
     * Loads hideFolders configuration from the parsed YAML
     */
    private void loadHideFoldersConfig(JsonNode rootNode)
    {
        JsonNode backendsNode = rootNode.path("backends");

        // Find the 'orbis' backend
        for (JsonNode backendNode : backendsNode)
        {
            if (backendNode.path("name").asText().equals("orbis"))
            {
                JsonNode hideFoldersNode = backendNode.path("hideFolders");
                List<String> hideFolders = new ArrayList<>();
                if (hideFoldersNode.isArray())
                {
                    for (JsonNode folderNode : hideFoldersNode)
                    {
                        hideFolders.add(folderNode.asText());
                    }
                }

                if (!hideFolders.isEmpty())
                {
                    this.hideFoldersPattern = Pattern.compile(
                            buildHiddenNodePattern(hideFolders));
                    log.info("Loaded hideFolders configuration: " + hideFolders);
                }
                break;
            }
        }

        // If no configuration was found, use defaults
        if (this.hideFoldersPattern == null)
        {
            List<String> defaultHideFolders = Arrays.asList(DEFAULT_HIDE_FOLDERS);
            this.hideFoldersPattern = Pattern.compile(
                    buildHiddenNodePattern(defaultHideFolders));
            log.debug(
                    "Using default hideFolders configuration: " + defaultHideFolders);
        }
    }

    /**
     * Loads resource types configuration from the parsed YAML
     */
    private void loadResourceTypesConfig(JsonNode rootNode)
    {

        String basePath = PortalUtils.getRealPath("content/documents");

        JsonNode resourceTypesNode = rootNode.path("resourceTypes");
        if (resourceTypesNode.isArray())
        {
            for (JsonNode resourceType : resourceTypesNode)
            {
                String name = resourceType.path("name").asText();
                String directory = resourceType.path("directory").asText();

                // Remove leading slash if present
                if (directory.startsWith("/"))
                {
                    directory = directory.substring(1);
                }

                String fullPath = Paths.get(basePath, directory).toString();
                resourcePaths.put(name, fullPath);
            }
            log.info("Loaded resource types: " + resourcePaths.keySet());
        }

        // If no resource types were found, use defaults
        if (resourcePaths.isEmpty())
        {
            setupDefaultResourcePaths();
            log.warn("Using default resource types");
        }
    }

    /**
     * Sets up default resource paths
     */
    private void setupDefaultResourcePaths()
    {
        try
        {
            resourcePaths.put("Flash",
                    PortalUtils.getRealPath("content\\documents\\flash"));
            resourcePaths.put("Files",
                    PortalUtils.getRealPath("content\\documents\\Link"));
            resourcePaths.put("Images",
                    PortalUtils.getRealPath("content\\documents\\Image"));
            log.info("Using default resource types: " + resourcePaths.keySet());
        }
        catch (Exception e)
        {
            log.error("Failed to set up default resource paths", e);
            // Even if this fails, the map will be empty but not null
        }
    }

    /**
     * Loads access control configuration from the parsed YAML
     */
    private void loadAccessControlConfig(JsonNode rootNode)
    {
        // Reset permissions
        defaultConfigPermission = 0;

        // We load the default permissions from the YAML file based on current yml file format and config,
        // if yml file format changes, this will need to be updated
        JsonNode accessControlNode = rootNode.path("accessControl");
        if (accessControlNode.isArray())
        {
            for (JsonNode ruleNode : accessControlNode)
            {
                String role = ruleNode.path("role").asText("*");
                String resourceType = ruleNode.path("resourceType").asText("*");
                String folder = ruleNode.path("folder").asText("/");

                // We only care about wildcard rules
                if ("*".equals(role) && "*".equals(resourceType) && "/".equals(
                        folder))
                {
                    // Process all permission fields
                    Iterator<Map.Entry<String, JsonNode>> fields = ruleNode.fields();
                    while (fields.hasNext())
                    {
                        Map.Entry<String, JsonNode> field = fields.next();
                        String key = field.getKey();

                        // Skip non-permission fields
                        if (key.equals("role") || key.equals(
                                "resourceType") || key.equals("folder"))
                        {
                            continue;
                        }

                        // If permission is granted, add it to the mask
                        if (field.getValue().asBoolean(false))
                        {
                            try
                            {
                                Permission permission = Permission.valueOf(key);
                                defaultConfigPermission |= permission.getValue();
                            }
                            catch (IllegalArgumentException e)
                            {
                                log.warn("Unknown permission in config: " + key);
                            }
                        }
                    }
                }
            }
            log.info(
                    "Loaded permissions configuration: " + defaultConfigPermission);
        }
        else
        {
            log.warn(
                    "No access control rules found in configuration, using defaults");
            defaultConfigPermission = ALL_PERMISSIONS;
        }
    }

    /**
     * Gets the resource paths loaded from configuration
     */
    public Map<String, String> getResourcePaths()
    {
        return Collections.unmodifiableMap(resourcePaths);
    }

    @Cacheable(value = "fileAttributes")
    public FileAttributesDto getFileAttributes(Path path) throws IOException
    {
        log.debug("Reading attributes for " + path.toAbsolutePath());
        var attrs = Files.readAttributes(path, BasicFileAttributes.class,
                LinkOption.NOFOLLOW_LINKS);
        boolean hasChildren = false;
        if (attrs.isDirectory())
        {
            //check if the folder has children
            hasChildren = folderContainsSubfolders(path);
        }
        return new FileAttributesDto(attrs.size(),
                attrs.lastModifiedTime().toMillis(), attrs.isRegularFile(),
                attrs.isDirectory(), hasChildren);
    }

    @CachePut(value = "folderList", key = "#root.args[0]")
    public List<Folder> getCachedFolderList(Path folderPath, List<Folder> folders)
    {
        log.debug("Caching folder list for " + folderPath);
        return folders;
    }

    @Cacheable(value = "folderList")
    public List<Folder> getFolderList(Path folderPath)
    {
        log.debug(
                "We do not cache folders separately - folder was cached as part of file attributes. If we want cache folders separately, would need actual folder listing logic here" + folderPath);
        return null;
    }

    @CacheEvict(value = "fileAttributes")
    public void deleteFile(Path path)
    {
        log.debug("Evicting cache for deleted file: " + path);
    }

    @CacheEvict(value = { "folderAttributes", "folderList" })
    public void deleteFolder(Path folderPath)
    {
        log.debug("Evicting cache for folder: " + folderPath);
    }

    @CacheEvict(value = { "folderList" }, key = "#root.args[0]")
    public void evictParentFolderCache(Path folderPath)
    {
        log.debug("Evicting parent folder cache: " + folderPath);
    }

    private boolean folderContainsSubfolders(Path fsPath) throws IOException
    {

        try
        {
            boolean hasSubfolders = false;
            try (DirectoryStream<Path> directoryStream = Files.newDirectoryStream(
                    fsPath))
            {
                for (Path path : directoryStream)
                {
                    if (!this.isHiddenPath(path.getFileName().toString()))
                    {
                        if (Files.isDirectory(path))
                        {
                            hasSubfolders = true;
                            break;
                        }
                    }
                }
            }
            return hasSubfolders;
        }
        catch (NoSuchFileException var12)
        {
            return false;
        }

    }

    private Pattern getHideFoldersPattern()
    {
        return this.hideFoldersPattern;
    }

    private static String buildHiddenNodePattern(List<String> hiddenList)
    {
        StringBuilder sb = new StringBuilder("^(");

        for (String item : hiddenList)
        {
            if (sb.length() > 3)
            {
                sb.append("|");
            }

            item = item.replaceAll("\\.", "\\\\.");
            item = item.replaceAll("\\*", ".*");
            item = item.replaceAll("\\?", ".");

            for (char escapableCharacter : ESCAPABLE_CHARS)
            {
                String escaped = String.format("\\%s", escapableCharacter);
                item = item.replaceAll(escaped, Matcher.quoteReplacement(escaped));
            }

            sb.append("(");
            sb.append(item);
            sb.append(")");
        }

        sb.append(")$");
        return sb.toString();
    }

    private boolean isHiddenFolder(String folderName)
    {
        return getHideFoldersPattern() != null && getHideFoldersPattern().matcher(
                folderName).matches();
    }

    public boolean isHiddenPath(String folderPath)
    {
        if (getHideFoldersPattern() != null && folderPath != null)
        {
            for (String pathPart : folderPath.split("/"))
            {
                if (this.isHiddenFolder(pathPart))
                {
                    return true;
                }
            }

            return false;
        }
        else
        {
            return false;
        }
    }

}