package com.orbis.configuration.ckfinder;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.StreamSupport;

import com.cksource.ckfinder.acl.Acl;
import com.cksource.ckfinder.acl.AclResult;
import com.cksource.ckfinder.config.Config;
import com.cksource.ckfinder.exception.InvalidNameException;
import com.cksource.ckfinder.filesystem.Backend;
import com.cksource.ckfinder.filesystem.node.File;
import com.cksource.ckfinder.filesystem.node.Folder;
import com.cksource.ckfinder.resourcetype.ResourceType;
import com.cksource.ckfinder.utils.PathUtils;
import com.cksource.ckfinder.utils.StringUtils;
import com.orbis.configuration.ckfinder.dto.FileAttributesDto;
import com.orbis.configuration.ckfinder.mapper.CKFileMapper;

import lombok.val;

public class CKFinderOrbisBackend extends Backend
{
    private final FileAttributeService attributeService;

    public CKFinderOrbisBackend(Config.Backend config, FileSystem fileSystem,
            Acl acl, FileAttributeService attributeService)
    {
        super(config, fileSystem, acl);
        this.attributeService = attributeService;
    }

    @Override
    public List<File> listFiles(ResourceType resourceType, String folderPath,
            boolean allowHidden) throws IOException
    {
        if (!allowHidden && this.isHiddenPath(folderPath))
        {
            throw new InvalidNameException(
                    "Folder name is configured as hidden: " + folderPath);
        }

        val fsPath = this.toFileSystemPath(
                PathUtils.combinePaths(resourceType.getDirectory(), folderPath));

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(fsPath))
        {
            List<Path> sortedPaths = StreamSupport
                    .stream(stream.spliterator(), false)
                    .sorted(Comparator.comparing(
                            p -> p.getFileName().toString().toLowerCase()))
                    .toList();

            return buildFileList(sortedPaths, resourceType, allowHidden);
        }
    }

    @Override
    public List<Folder> listFolders(ResourceType resourceType, String folderPath)
            throws IOException
    {
        if (this.isHiddenPath(folderPath))
        {
            throw new InvalidNameException(
                    "Folder name is configured as hidden: " + folderPath);
        }

        val cacheKey = Path.of(resourceType.getName(), folderPath);
        List<Folder> cachedFolders = attributeService.getFolderList(cacheKey);

        if (cachedFolders != null)
        {
            return new ArrayList<>(cachedFolders);
        }

        List<Folder> folders = listFoldersUncached(resourceType, folderPath);

        return attributeService.getCachedFolderList(cacheKey, folders);
    }

    private List<Folder> listFoldersUncached(ResourceType resourceType,
            String folderPath) throws IOException
    {
        ArrayList<Folder> folders = new ArrayList<>();
        Path fsPath = this.toFileSystemPath(
                PathUtils.combinePaths(resourceType.getDirectory(), folderPath));

        try (DirectoryStream<Path> directoryStream = Files
                .newDirectoryStream(fsPath))
        {
            for (Path p : directoryStream)
            {
                FileAttributesDto attributes = attributeService
                        .getFileAttributes(p);

                if (attributes.isDirectory())
                {
                    String folderName = p.getFileName().toString();
                    if (!this.isHiddenFolder(folderName))
                    {
                        Folder folder = new Folder().setName(folderName);
                        folder.setAclResult(new AclResult(
                                attributeService.getDefaultConfigPermission()));
                        folder.setHasChildren(attributes.hasChildren());
                        folders.add(folder);
                    }
                }
            }
        }

        return folders;
    }

    @Override
    public String createFolder(ResourceType resourceType, String folderPath,
            String folderName)
    {
        String result = super.createFolder(resourceType, folderPath, folderName);
        attributeService.evictParentFolderCache(
                Path.of(resourceType.getName(), folderPath));
        return result;
    }

    @Override
    public void deleteFolder(ResourceType resourceType, String folderPath,
            boolean allowHidden)
    {
        super.deleteFolder(resourceType, folderPath, allowHidden);
        attributeService.evictParentFolderCache(
                Path.of(resourceType.getName() + folderPath));
    }

    @Override
    public String renameFolder(ResourceType resourceType, String folderPath,
            String oldFolderName, String newFolderName, boolean allowHidden,
            boolean validateNames)
    {
        String result = super.renameFolder(resourceType, folderPath, oldFolderName,
                newFolderName, allowHidden, validateNames);
        attributeService.evictParentFolderCache(
                Path.of(resourceType.getName() + folderPath));
        return result;
    }

    private List<File> buildFileList(List<Path> sortedPaths,
            ResourceType resourceType, boolean allowHidden) throws IOException
    {
        List<File> files = new ArrayList<>();
        for (Path path : sortedPaths)
        {
            val file = includeFile(path, allowHidden, resourceType);
            if (file != null)
                files.add(file);
        }
        return files;
    }

    public File includeFile(Path path, boolean allowHidden,
            ResourceType resourceType) throws IOException
    {
        val fileAttributes = this.attributeService.getFileAttributes(path);
        if ((allowHidden || !isHiddenFile(path.getFileName().toString()))
                && shouldIncludeFile(path, resourceType, fileAttributes))
        {
            return CKFileMapper.createFileFromPath(path, fileAttributes);
        }
        return null;
    }

    private boolean shouldIncludeFile(Path path, ResourceType resourceType,
            FileAttributesDto fileAttributes)
    {
        return fileAttributes.isRegularFile() && resourceType.isAllowedExtension(
                StringUtils.getFilenameExtension(path.getFileName().toString()));
    }
}