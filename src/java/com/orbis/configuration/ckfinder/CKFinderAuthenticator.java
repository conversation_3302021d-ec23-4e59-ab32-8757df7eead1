package com.orbis.configuration.ckfinder;

import com.cksource.ckfinder.authentication.Authenticator;
import jakarta.inject.Named;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;


@Named
public class CKFinderAuthenticator implements Authenticator {
    @Override
    public boolean authenticate()
    {
        Authentication authentication = SecurityContextHolder.getContext()
                .getAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }
}