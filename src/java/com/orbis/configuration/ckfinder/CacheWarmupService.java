package com.orbis.configuration.ckfinder;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import com.cksource.ckfinder.acl.AclResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


import com.cksource.ckfinder.filesystem.node.Folder;
import com.orbis.configuration.ckfinder.dto.FileAttributesDto;

import lombok.extern.apachecommons.CommonsLog;

@Component
@CommonsLog
public class CacheWarmupService
{

    private final FileAttributeService fileAttributeService;

    private volatile boolean isRootInstance = false;

    @Autowired
    public CacheWarmupService(FileAttributeService fileAttributeService)
    {
        this.fileAttributeService = fileAttributeService;
    }

    @EventListener(ContextRefreshedEvent.class)
    public void warmupOnStartup(ContextRefreshedEvent event)
    {
        log.debug("Cache warmup on startup...");
        if (event.getApplicationContext().getParent() == null)
        {
            log.info("Cache warmup on startup - starting asynchronously...");
            this.isRootInstance = true;
            CompletableFuture.runAsync(this::performWarmup)
                    .exceptionally(throwable -> {
                        log.error("Async warmup failed", throwable);
                        return null;
                    });
        }
        else
        {
            this.isRootInstance = false;
            log.debug("Warmup already executed, skipping...");
        }
    }

    @Scheduled(cron = "0 0 */2 * * *")
    public void warmupOnSchedule()
    {
        // Only run scheduled warmup on the root instance
        if (!isRootInstance)
        {
            log.info("Scheduled warmup skipped - not the root instance");
            return;
        }

        log.info("Scheduled cache warmup...");
        CompletableFuture.runAsync(this::performWarmup)
                .exceptionally(throwable -> {
                    log.error("Async warmup failed", throwable);
                    return null;
                });
    }

    public void performWarmup()
    {
        log.info("Starting cache warmup...");

        try
        {
            // Get resource paths from FileAttributeService
            Map<String, String> resourcePaths = fileAttributeService.getResourcePaths();

            for (Map.Entry<String, String> entry : resourcePaths.entrySet())
            {
                String resourceType = entry.getKey();
                String path = entry.getValue();
                log.info(
                        "Warming up resource type: " + resourceType + " at path: " + path);
                warmupDirectory(resourceType, path);
            }

        }
        catch (Exception e)
        {
            log.error("Error during cache warmup", e);
        }

        log.info("Cache warmup finished.");
    }

    private void warmupDirectory(String resourceType, String directoryPath)
            throws IOException
    {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path))
        {
            log.warn("Directory does not exist: " + directoryPath);
            return;
        }

        processDirectory(resourceType, "", path);
    }

    private void processDirectory(String resourceType, String relativePath,
            Path directory) throws IOException
    {
        List<Folder> folders = new ArrayList<>();

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory))
        {
            for (Path entry : stream)
            {
                try
                {
                    FileAttributesDto attrs = fileAttributeService
                            .getFileAttributes(entry);

                    if (attrs.isDirectory())
                    {
                        String folderName = entry.getFileName().toString();
                        Folder folder = new Folder().setName(folderName);
                        folder.setAclResult(new AclResult(
                                fileAttributeService.getDefaultConfigPermission()));
                        folder.setHasChildren(attrs.hasChildren());
                        folders.add(folder);

                        String newRelativePath = relativePath.isEmpty() ? folderName
                                : relativePath + "/" + folderName;
                        processDirectory(resourceType, newRelativePath, entry);
                    }
                }
                catch (IOException e)
                {
                    log.warn("Failed to read attributes for: " + entry, e);
                }
            }
        }

        if (!folders.isEmpty())
        {
            Path cacheKey = relativePath.isEmpty() ? Path.of(resourceType)
                    : Path.of(resourceType, relativePath);
            fileAttributeService.getCachedFolderList(cacheKey, folders);
            log.debug("Cached folder list for: " + cacheKey + " with "
                    + folders.size() + " folders");
        }
    }

}
