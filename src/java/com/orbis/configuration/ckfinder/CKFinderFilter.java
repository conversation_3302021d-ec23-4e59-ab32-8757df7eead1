package com.orbis.configuration.ckfinder;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;

public class CKFinderFilter implements Filter
{
    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
            FilterChain chain) throws IOException, ServletException
    {
        ((HttpServletResponse) response).setHeader("X-Content-Type-Options",
                "nosniff");
        chain.doFilter(request, response);
    }
}
