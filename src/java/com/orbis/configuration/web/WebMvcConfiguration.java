package com.orbis.configuration.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfiguration extends WebMvcConfigurationSupport
{

    private final ResourceLoader resourceLoader;

    protected RequestMappingHandlerAdapter createRequestMappingHandlerAdapter()
    {
        return new OrbisRequestMappingHandlerAdapter();
    }

    protected RequestMappingHandlerMapping createRequestMappingHandlerMapping()
    {
        return new OrbisRequestMappingHandlerMapping();
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry)
    {
        registry.addViewController("/").setViewName("index");
    }

    @Override
    protected void addResourceHandlers(ResourceHandlerRegistry registry)
    {
        super.addResourceHandlers(registry);
        Resource resource = resourceLoader.getResource("/content/documents/");
        registry.addResourceHandler("/content/documents/**")
                .addResourceLocations(resource);
    }
}
