package com.orbis.configuration.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

public class OrbisRequestMappingHandlerAdapter extends RequestMappingHandlerAdapter
{

    @Override
    public ModelAndView handleInternal(HttpServletRequest request,
            HttpServletResponse response, HandlerMethod handlerMethod)
            throws Exception
    {

        return super.handleInternal(request, response, handlerMethod);
    }
}
