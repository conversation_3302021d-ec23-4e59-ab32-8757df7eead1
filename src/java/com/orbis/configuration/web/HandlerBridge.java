package com.orbis.configuration.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import lombok.extern.apachecommons.CommonsLog;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import lombok.Setter;
import org.springframework.web.util.ServletRequestPathUtils;
import org.springframework.web.util.UrlPathHelper;
import org.springframework.web.util.WebUtils;

@CommonsLog
public class HandlerBridge
{

    @Setter
    private static OrbisRequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @Setter
    private static OrbisRequestMappingHandlerMapping requestMappingHandlerMapping;

    private static final HandlerInterceptor interceptor = new OrbisGlobalInterceptor();

    public static ModelAndView handle(HttpServletRequest request,
            HttpServletResponse response, HandlerMethod handlerMethod)
            throws Exception
    {
        interceptor.preHandle(request, response, handlerMethod);
        ModelAndView modelAndView = requestMappingHandlerAdapter.handleInternal(
                request, response, handlerMethod.createWithResolvedBean());
        interceptor.postHandle(request, response, handlerMethod, modelAndView);
        return modelAndView;
    }

    public static HandlerMethod lookupHandler(String uri,
            HttpServletRequest request) throws Exception
    {
        request.setAttribute(WebUtils.INCLUDE_SERVLET_PATH_ATTRIBUTE, "/");
        request.setAttribute(UrlPathHelper.PATH_ATTRIBUTE, uri);
        request.setAttribute(WebUtils.INCLUDE_REQUEST_URI_ATTRIBUTE, uri);
        request.setAttribute(ServletRequestPathUtils.PATH_ATTRIBUTE,
                ServletRequestPathUtils.parseAndCache(request));
        HandlerMethod handlerMethod = requestMappingHandlerMapping
                .lookupHandlerMethod(uri, request);
        if (log.isTraceEnabled())
        {
            log.trace(request.getMethod() + ": " + uri
                    + "; Resolved Handler Method: "
                    + handlerMethod);
        }
        return handlerMethod;
    }

}
