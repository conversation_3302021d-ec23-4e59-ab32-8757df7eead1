package com.orbis.configuration.web;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.SessionCookieConfig;
import org.springframework.web.context.ContextLoaderListener;

public class CustomContextListener extends ContextLoaderListener
{

    @Override
    public void contextInitialized(ServletContextEvent event)
    {
        super.contextInitialized(event);

        SessionCookieConfig cookieConfig = event.getServletContext()
                .getSessionCookieConfig();
        cookieConfig.setHttpOnly(true);
        cookieConfig.setSecure(true);
        cookieConfig.setAttribute("SameSite", "Lax");
    }
}
