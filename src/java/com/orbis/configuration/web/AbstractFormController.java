package com.orbis.configuration.web;

import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;
import org.springframework.AnnotatedActionController;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Getter
@Setter
public abstract class AbstractFormController<T> extends AnnotatedActionController
{

    private boolean sessionForm;

    private String formView;

    private String successView;

    private Validator validator;

    protected abstract ModelAndView showForm(T cmd, BindingResult bindingResult,
                                             HttpServletRequest request, HttpServletResponse response);

    protected ModelAndView showNewForm(T command, BindingResult bindingResult,
            HttpServletRequest request, HttpServletResponse response) {
        return showForm(command, bindingResult, request, response);
    }

}
