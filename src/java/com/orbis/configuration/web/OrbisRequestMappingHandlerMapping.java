package com.orbis.configuration.web;

import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.apachecommons.CommonsLog;

@CommonsLog
public class OrbisRequestMappingHandlerMapping extends RequestMappingHandlerMapping
{

    @Override
    protected boolean isHandler(Class<?> beanType)
    {
        return super.isHandler(beanType) || AnnotatedElementUtils
                .hasAnnotation(beanType, RequestMapping.class);
    }

    @Override
    protected HandlerMethod getHandlerInternal(HttpServletRequest request)
            throws Exception
    {
        return super.getHandlerInternal(request);
    }

    @Override
    public HandlerMethod lookupHandlerMethod(String lookupPath,
            HttpServletRequest request) throws Exception
    {
        return super.lookupHandlerMethod(lookupPath, request);
    }

}
