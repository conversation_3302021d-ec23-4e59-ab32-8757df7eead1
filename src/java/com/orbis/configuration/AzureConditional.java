package com.orbis.configuration;

import java.util.Map;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import com.orbis.utils.StringUtils;

public class AzureConditional implements Condition
{
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata)
    {
        boolean ret = false;
        if (context.getBeanFactory() != null)
        {
            ret = !StringUtils.isEmpty((String) context.getBeanFactory()
                    .getBean("siteControllerSettings", Map.class).get("azure"));
        }
        return ret;
    }
}
