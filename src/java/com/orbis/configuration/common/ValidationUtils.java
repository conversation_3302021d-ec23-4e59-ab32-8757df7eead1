package com.orbis.configuration.common;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ValidationUtils
{

    private final Validator validator;

    public void validate(Object object) throws ValidationException
    {
        Set<ConstraintViolation<Object>> validate = validator.validate(object);
        if (!validate.isEmpty())
        {
            Map<String, String> errors = new HashMap<>();
            validate.forEach(it -> errors.put(it.getPropertyPath().toString(),
                    it.getMessage()));
            throw new ValidationException(errors);
        }
    }
}