package com.orbis.configuration;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.orbis.domain.DomainEventsPublisherStaticProvider;
import com.orbis.domain.api.DomainEventsPublisher;
import com.orbis.domain.core.JustForwardDomainEventPublisher;

import lombok.val;

@Configuration
public class DomainEventsConfiguration
{
    @Bean
    public DomainEventsPublisher justForwardDomainEventPublisher(
            ApplicationEventPublisher applicationEventPublisher)
    {
        val justForwardDomainEventPublisher = new JustForwardDomainEventPublisher(
                applicationEventPublisher);
        DomainEventsPublisherStaticProvider
                .setDomainEventsPublisher(justForwardDomainEventPublisher);
        return justForwardDomainEventPublisher;
    }

}
