package com.orbis.docTemp;

import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

import com.aspose.words.IMailMergeDataSource;

public class MailMergeMapDataSource implements IMailMergeDataSource
{
    private Map map;

    private String key;

    private Locale locale;

    boolean ranOnce = false;

    public MailMergeMapDataSource(Map map, String key, Locale locale)
    {
        this.map = map;
        this.key = key;
        this.locale = locale;
    }

    /**
     * The name of the data source. Used by Aspose.Words only when executing
     * mail merge with repeatable regions.
     */
    public String getTableName() throws Exception
    {
        return this.key;
    }

    public boolean getValue(String fieldName, Object[] fieldValue) throws Exception
    {
        boolean replaceable = fieldName.startsWith(key);
        if (replaceable)
        {
            Pattern pattern = Pattern.compile("(.*)\\[\"(.*)\"\\](.*)");
            Matcher match = pattern.matcher(fieldName);

            if (match.find())
            {
                String key = match.group(2);
                String remainingPath = match.group(3);
                Object mapVal = map.get(key);
                if (!StringUtils.isEmpty(remainingPath))
                {
                    DocTemplateHelper.handleFieldMerge(mapVal,
                            remainingPath.substring(1), fieldValue, locale);
                }
                else if (mapVal != null)
                {
                    fieldValue[0] = mapVal.toString();
                }
                else
                {

                }
            }
            else
            {

            }
        }
        return replaceable;
    }

    public boolean moveNext() throws Exception
    {
        boolean runNow = !ranOnce;
        ranOnce = true;
        return runNow;
    }

    @Override
    public IMailMergeDataSource getChildDataSource(String arg0) throws Exception
    {
        return null;
    }
}
