package com.orbis.docTemp;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.util.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.aspose.words.BreakType;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.Font;
import com.aspose.words.License;
import com.aspose.words.LoadOptions;
import com.aspose.words.RelativeHorizontalPosition;
import com.aspose.words.RelativeVerticalPosition;
import com.aspose.words.SaveFormat;
import com.aspose.words.WrapType;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAbstractModelEntity;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFCategory;
import com.orbis.df.DFHelper;
import com.orbis.df.DFModelEntity;
import com.orbis.df.DFQuestion;
import com.orbis.email.EmailModel;
import com.orbis.email.EmailRecipient;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchMassAssignButton;
import com.orbis.search.SearchModel;
import com.orbis.spring.servlet.ByteArrayDownloadView;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailAttachment;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.OrbisThread;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisModule;
import com.orbis.web.content.aspose.AsposeBundle;
import com.orbis.web.content.coop.CoopHelper;
import com.orbis.web.content.coop.CoopLetterTemplate;
import com.orbis.web.content.doc.DocHelper;
import com.orbis.web.content.exp.EXPModule;
import com.orbis.web.content.exp.EXPPostingTemplate;
import com.orbis.web.content.exp.EXPRecord;
import com.orbis.web.content.exp.EXPRecordAbstract;
import com.orbis.web.content.exp.EXPTermCourseTypeStep;
import com.orbis.web.content.exp.EXPTypeWorkflowExtraForm;
import com.orbis.web.content.exp.EXPTypeWorkflowExtraFormHelper;

import jakarta.servlet.http.HttpServletRequest;

public final class DocTemplateHelper
{
    public static final String TEMPLATE_STORAGE = "/content/documents/templates/";

    public static final String NOT_DATASOURCE = "ASPOSE-NOT-DATASOURCE";

    public static final String DOC_TYPE_PDF = "pdf";

    private static boolean licenseApplied = false;

    private DocTemplateHelper()
    {
    }

    public static Document loadDocument(String uuid)
    {
        Document doc = null;
        String fileName = PortalUtils.getRealPath("");
        if (uuid.startsWith("/"))
        {
            fileName += uuid;
        }
        else
        {
            fileName += FilePathUtils.getFilePathUrlForUUID(uuid);
        }
        try
        {
            final LoadOptions lo = new LoadOptions();
            lo.setWebRequestTimeout(100);
            doc = new Document(fileName, lo);
            doc.updateFields();
        }
        catch (Exception e)
        {

        }

        return doc;
    }

    public static String handleFieldMergeValue(Object value, String[] args,
            Locale locale)
    {
        String ret = "";
        if (value != null)
        {
            if (value instanceof Date)
            {
                String dateFormat = DateUtils.DF_MEDIUM_DATE_2;

                if (args.length > 0 && !StringUtils.isEmpty(args[0]))
                {
                    if ("time".equals(args[0]))
                    {
                        dateFormat = DateUtils.DF_SHORT_TIME;
                    }
                    else if ("date".equals(args[0]))
                    {
                        dateFormat = DateUtils.DF_SHORT_DATE;
                    }
                    else if ("dateAndTime".equals(args[0]))
                    {
                        dateFormat = DateUtils.DF_SHORT_DATE_TIME;
                    }
                    else
                    {
                        dateFormat = args[0];
                    }
                }

                ret = DateUtils.formatDate((Date) value, dateFormat, locale);
            }
            else
            {
                ret = value.toString();
            }
        }

        return ret;
    }

    public static void markSampleWithCustomName(JSONObject sample) throws Exception
    {
        if (sample.has("sampleData"))
        {
            sample.getJSONObject("sampleData").put("customName", true);
        }
        else
        {
            sample.put("sampleData", new JSONObject("{customName : true}"));
        }
    }

    public static JSONObject loadDocTemplateSample(final String sampleId)
            throws Exception
    {
        String dir = "/WEB-INF/conf/docTemplate_samples/";
        String filePath = dir.concat(sampleId).concat(".json");
        return new JSONObject(PortalUtils.getResourceAsString(filePath, false));
    }

    public static Document loadDocument(HttpServletRequest request,
            String paramName)
    {
        Document doc = null;
        MultipartFile mFile = FileUtils.getUploadedFile(request, "wordTemplate");

        try
        {
            doc = new Document(mFile.getInputStream());
            doc.updateFields();
        }
        catch (Exception e)
        {

        }

        return doc;
    }

    public static void addBean(Document doc, DocumentModelItem item, Locale locale)
            throws Exception
    {
        if (item.getValue() != null && !StringUtils.isEmpty(item.getKey()))
        {
            doc.getMailMerge().execute(new MailMergeBeanDataSource(item.getValue(),
                    item.getKey(), locale));
        }
    }

    public static void addMap(Document doc, DocumentModelItem item, Locale locale)
            throws Exception
    {
        if (item.getValue() != null && !StringUtils.isEmpty(item.getKey()))
        {
            doc.getMailMerge().execute(new MailMergeMapDataSource(
                    (Map) item.getValue(), item.getKey(), locale));
        }
    }

    public static void addBeanList(Document doc, DocumentModelItem item,
            Locale locale) throws Exception
    {
        if (item.getValue() != null && !StringUtils.isEmpty(item.getKey()))
        {
            MailMergeGroupListInstructionDataSource groupInstruction = new MailMergeGroupListInstructionDataSource(
                    (List) item.getValue(), "groupBy", locale);
            doc.getMailMerge().execute(groupInstruction);
            doc.getMailMerge().executeWithRegions(new MailMergeGroupListDataSource(
                    item.getKey() + "Grouped", locale, groupInstruction));
            MailMergeBeanListDataSource mailMergeBeanListDataSource = new MailMergeBeanListDataSource(
                    (List) item.getValue(), item.getKey(), locale);
            doc.getMailMerge().executeWithRegions(mailMergeBeanListDataSource);
        }
    }

    public static void addField(Document doc, DocumentModelItem item, Locale locale)
            throws Exception
    {
        if (!StringUtils.isEmpty(item.getKey()))
        {
            doc.getMailMerge().execute(new String[] { item.getKey() },
                    new Object[] {
                            handleFieldMergeValue(item.getValue(), null, locale) });
        }
    }

    public static JSONArray getSampleTree(Class<? extends DocTemplate> templateType,
            int type, JSONObject additionalParams) throws Exception
    {
        if (additionalParams == null)
        {
            additionalParams = new JSONObject();
        }
        DocTemplate template = templateType.newInstance();
        template.setType(type);
        JSONObject sample = template.getSample(additionalParams);
        buildSample(sample);

        return generateSampleTree(sample, "");
    }

    private static void buildSample(JSONObject sample) throws Exception
    {
        for (Iterator i = sample.keys(); i.hasNext();)
        {
            String key = (String) i.next();

            if ("sampleData".equals(key))
            {
                continue;
            }

            if (sample.get(key) != JSONObject.NULL)
            {
                buildSample(sample.getJSONObject(key));
            }
        }

        if (sample.has("sampleData"))
        {
            JSONObject sampleData = sample.getJSONObject("sampleData");
            if (sampleData.has("appendSample"))
            {
                JSONObject appendSample = new JSONObject();
                if (sampleData.get("appendSample") instanceof JSONArray)
                {
                    JSONArray samplesToAppend = sampleData
                            .getJSONArray("appendSample");
                    for (int i = 0; i < samplesToAppend.length(); i++)
                    {
                        JSONObject newSample = DocTemplateHelper
                                .loadDocTemplateSample(
                                        samplesToAppend.getString(i));
                        buildSample(newSample);
                        JSONUtils.mergeObjects(appendSample, newSample);
                    }
                }
                else
                {
                    appendSample = DocTemplateHelper.loadDocTemplateSample(
                            sampleData.getString("appendSample"));
                    buildSample(appendSample);
                }

                for (Iterator i = appendSample.keys(); i.hasNext();)
                {
                    String key = (String) i.next();
                    sample.put(key, appendSample.get(key));
                }
            }
        }
    }

    private static JSONArray generateSampleTree(JSONObject sample, String path)
            throws Exception
    {
        JSONArray ret = new JSONArray();
        for (Iterator i = sample.keys(); i.hasNext();)
        {
            if (!StringUtils.isEmpty(path) && !path.endsWith("."))
            {
                path += ".";
            }

            String key = (String) i.next();
            JSONObject node = null;
            if (sample.get(key) == JSONObject.NULL)
            {
                ret.put(newSampleNode(path + key, key));
            }
            else
            {
                JSONObject sampleFields = sample.getJSONObject(key);

                JSONObject sampleData = new JSONObject();
                if (sampleFields.has("sampleData"))
                {
                    sampleData = sampleFields.getJSONObject("sampleData");
                    sampleFields.remove("sampleData");
                }

                String currentPath = path + key;
                String name = key;

                Object[] refinedData = handleSampleData(currentPath, name,
                        sampleData, path, key);
                currentPath = (String) refinedData[0];
                name = (String) refinedData[1];
                path = (String) refinedData[2];
                key = (String) refinedData[3];

                node = newSampleNode(currentPath, name);
                if (sampleFields.length() > 0)
                {
                    JSONArray children = generateSampleTree(sampleFields,
                            path + key + ".");
                    addSampleChildren(node, children);
                }
            }

            if (node != null)
            {
                ret.put(node);
            }
        }
        return ret;
    }

    private static Object[] handleSampleData(String currentPath, String name,
            JSONObject sampleData, String path, String key) throws Exception
    {

        if (sampleData.has("description")
                && !StringUtils.isEmpty(sampleData.getString("description")))
        {
            name = sampleData.getString("description");
        }

        if (sampleData.has("isCollection") && sampleData.getBoolean("isCollection"))
        {
            name += " (Collection)";
            currentPath += " -C";
        }

        if (sampleData.has("customName") && sampleData.getBoolean("customName"))
        {
            name += " (Custom Name)";
            currentPath = currentPath.replace("." + key, "[\\\"" + key + "\\\"]");
            if (path.endsWith("."))
            {
                path = path.substring(0, path.length() - 1);
            }
            key = "[\\\"" + key + "\\\"]";
        }

        return new Object[] { currentPath, name, path, key };
    }

    private static void addSampleChildren(JSONObject target, JSONArray childNodes)
            throws Exception
    {
        target.put("isexpand", true);
        if (!target.getString("value").contains("-"))
        {
            target.put("id", "");
            target.put("value", "");
        }
        target.put("hasChildren", true);
        target.put("ChildNodes", childNodes);

    }

    private static JSONObject newSampleNode(String key, String label)
            throws Exception
    {
        JSONObject node = new JSONObject();

        node.put("id", key);
        node.put("value", key);
        node.put("text", label);
        node.put("showcheck", true);
        node.put("isexpand", false);
        node.put("checkstate", 1);
        node.put("hasChildren", false);
        node.put("complete", true);

        return node;
    }

    public static void prepTemplatesConfigPage(ModelAndView mv,
            Class<? extends DocTemplate> templateType, String templateConfigAction,
            JSONObject additionalParams, HttpServletRequest request)
            throws Exception
    {
        if (additionalParams == null)
        {
            additionalParams = new JSONObject();
        }
        DocTemplate template = templateType.newInstance();
        if (!StringUtils.isEmpty(request.getParameter("saveTemplate")))
        {
            saveTemplate(mv, templateType, additionalParams, request);
        }
        else if (!StringUtils.isEmpty(request.getParameter("deleteTemplate")))
        {
            deleteDocTemplate(request);
        }

        DocTemplate instance = templateType.newInstance();

        String additionalHql = instance.getAdditionalHql(additionalParams);
        List<DocTemplate> templates = PortalUtils.getHt()
                .find("from " + templateType.getName() + " t "
                        + (!StringUtils.isEmpty(additionalHql)
                        ? "where " + additionalHql
                        : ""));
        Locale locale = PortalUtils.getLocale(request);
        Map<Integer, String> docTypes = template.generateTypeMap(locale);

        // cannot be created several templates with doc types marked as singleton
        Set<Integer> availableDocTypes = new HashSet<>(docTypes.keySet());
        List<Integer> singletonTypes = template.getSingletonTypes();
        for (DocTemplate t : templates)
        {
            t.setUsable(DocTemplateHelper
                    .doesTemplateHaveAValidFile(PortalUtils.getLocale(request), t));
            if (singletonTypes.contains(t.getType()))
            {
                availableDocTypes.remove(t.getType());
            }
        }

        instance.setAdditionalData(additionalParams);
        template.setAdditionalData(additionalParams);

        Set<Integer> docTypesWithSamples = new HashSet<>();
        for (Integer type : docTypes.keySet())
        {
            template.setType(type);
            if (template.getSample(additionalParams) != null)
            {
                docTypesWithSamples.add(type);
            }
        }

        mv.addObject("availableDocTypes", availableDocTypes);
        mv.addObject("docTypes",
                template.getTypeMap(PortalUtils.getLocale(request)));
        mv.addObject("docTypesWithSamples", docTypesWithSamples);
        mv.addObject("templateInstance", instance);
        mv.addObject("templateType", templateType.getName());
        mv.addObject("templateConfigAction", templateConfigAction);
        mv.addObject("templates", templates);
        mv.addObject("additionalParams", additionalParams.toString());
    }

    public static void addSearchEmailButton(SearchModel searchModel,
            Class<? extends DocTemplate> templateType, JSONObject additionalParams,
            int type, Locale locale, String action) throws Exception
    {
        addSearchEmailButton(searchModel, templateType, additionalParams, type,
                locale, action, null);
    }

    public static void addSearchEmailButton(SearchModel searchModel,
            Class<? extends DocTemplate> templateType, JSONObject additionalParams,
            int type, Locale locale, String action, String emailLabel)
            throws Exception
    {
        boolean isUsable = DocTemplateHelper.isTemplateValid(templateType, type,
                additionalParams, locale);
        JSONObject additionalParamsClone = JSONUtils.cloneJson(additionalParams);

        if (isUsable)
        {
            if (!StringUtils.isEmpty(emailLabel))
                additionalParamsClone.put("emailLabel", emailLabel);

            DocTemplate template = templateType.newInstance();
            template.setType(type);
            searchModel.setCanMassUpdate(true);
            SearchMassAssignButton bundleBtn = new SearchMassAssignButton();
            String label = !StringUtils.isEmpty(emailLabel)
                    ? "i18n.DocTemplateHelper.Email0Docu7763330697703668"
                    : "i18n.DocTemplateHelper.Email0Docu3953262639798392";
            MessageSource messageSource = PortalUtils.getMessageSource();
            bundleBtn.setLabel(messageSource.getMessage(label,
                    new Object[] { template.getTypeLabel(locale), emailLabel },
                    locale));
            bundleBtn.setAction(action);
            bundleBtn.addAdditionalParam("docType", "pdf");
            bundleBtn.addAdditionalParam("templateType", templateType.getName());
            bundleBtn.addAdditionalParam("type", String.valueOf(type));
            bundleBtn.addAdditionalParam("fromGrid", "true");
            bundleBtn.addAdditionalParam("additionalParams",
                    additionalParamsClone.toString().replace("\"", "&quot;"));

            searchModel.addMassAssignButton(
                    "emailFilledTemplate" + templateType.getSimpleName() + type
                            + (!StringUtils.isEmpty(emailLabel) ? emailLabel : ""),
                    bundleBtn);
        }
    }

    public static boolean addSearchButton(SearchModel searchModel,
            Class<? extends DocTemplate> templateType, JSONObject additionalParams,
            int type, String fileType, String idKey, Locale locale) throws Exception
    {
        boolean isUsable = false;
        DocTemplate templateInstance = templateType.newInstance();

        if (ArrayUtils.contains(templateInstance.getTypesWithoutDefault(), type))
        {
            List<DocTemplate> templates = DocTemplateHelper.getTemplates(type,
                    additionalParams, templateType);
            for (DocTemplate template : templates)
            {
                isUsable = doesTemplateHaveAValidFile(locale, template);

                if (isUsable)
                {
                    SearchMassAssignButton bundleBtn = getSearchMassAssignButton(
                            template.getName(), locale, fileType,
                            templateType.getName(), type, additionalParams, idKey);
                    bundleBtn.addAdditionalParam("templateId",
                            String.valueOf(template.getId()));

                    searchModel.addMassAssignButton(
                            "downloadFilledTemplate" + templateType.getSimpleName()
                                    + type + template.getName().replaceAll(" ", ""),
                            bundleBtn);
                }
            }
        }
        else
        {
            isUsable = DocTemplateHelper.isTemplateValid(templateType, type,
                    additionalParams, locale);

            if (isUsable)
            {
                templateInstance.setType(type);
                SearchMassAssignButton bundleBtn = getSearchMassAssignButton(
                        templateInstance.getTypeLabel(locale), locale, fileType,
                        templateType.getName(), type, additionalParams, idKey);

                searchModel.addMassAssignButton("downloadFilledTemplate"
                        + templateType.getSimpleName() + type, bundleBtn);
            }
        }
        if (isUsable)
        {
            searchModel.setCanMassUpdate(true);
        }

        return isUsable;
    }

    private static SearchMassAssignButton getSearchMassAssignButton(
            String templateName, Locale locale, String fileType,
            String templateTypeName, int type, JSONObject additionalParams,
            String idKey)
    {
        SearchMassAssignButton bundleBtn = new SearchMassAssignButton();
        MessageSource ms = PortalUtils.getMessageSource();
        bundleBtn.setLabel(
                ms.getMessage("i18n.DocTemplateHelper.Downloadte1006583390329626",
                        new Object[] { templateName }, locale));
        bundleBtn.setAction("downloadSearchFilledTemplate");

        bundleBtn.addAdditionalParam("docType", fileType);
        bundleBtn.addAdditionalParam("templateType", templateTypeName);
        bundleBtn.addAdditionalParam("type", String.valueOf(type));
        bundleBtn.addAdditionalParam("additionalParams",
                additionalParams.toString().replace("\"", "'"));
        bundleBtn.addAdditionalParam("idKey", idKey);
        bundleBtn.setAjaxAction(true);
        return bundleBtn;
    }

    public static boolean isTemplateValid(Class<? extends DocTemplate> templateType,
            Integer type, JSONObject additionalParams, Locale locale)
            throws Exception
    {
        DocTemplate templateInstance = templateType.newInstance();
        templateInstance.setType(type);

        DocTemplate template = DocTemplateHelper.getTemplate(type, additionalParams,
                templateType);

        return doesTemplateHaveAValidFile(locale, template);
    }

    public static boolean doesTemplateHaveAValidFile(Locale locale,
            DocTemplate template)
    {
        boolean ret = false;
        if (template != null)
        {
            String uuid = LocaleUtils.isL1(locale) ? template.getDocument()
                    : template.getL2Document();

            if (StringUtils.isEmpty(uuid))
            {
                uuid = template.getDocument();
            }

            if (!StringUtils.isEmpty(uuid))
            {
                ret = FilePathUtils.getFilePathUrlForUUID(uuid, true) != null;
            }
        }
        return ret;
    }

    private static void saveTemplate(ModelAndView mv,
            Class<? extends DocTemplate> templateType, JSONObject additionalParams,
            HttpServletRequest request) throws Exception
    {
        boolean canSave = true;
        DocTemplate template;
        if (!StringUtils.isEmpty(request.getParameter("templateId")))
        {
            template = PortalUtils.getHt().l(templateType,
                    RequestUtils.getInteger(request, "templateId"));
        }
        else
        {
            Integer type = RequestUtils.getInteger(request, "type");
            template = templateType.newInstance();
            String additionalHql = template.getAdditionalHql(additionalParams);
            if (template.getSingletonTypes().contains(type)
                    && existsTemplate(templateType, type, additionalHql))
            {
                canSave = false;
                FlashMessageUtils.error(request,
                        "i18n.DocTemplateHelper.Onlyonetam1084646562058116");
            }
            else
            {
                template.setType(type);
                boolean hasDefault = (Integer) PortalUtils
                        .getHt().find(
                                "select count(t.id) from " + templateType.getName()
                                        + " t where "
                                        + (!StringUtils.isEmpty(additionalHql)
                                        ? additionalHql + " and "
                                        : "")
                                        + " t.type = ? and t.defaultForType = true",
                                type)
                        .get(0) > 0;
                template.setDefaultForType(!hasDefault);
            }
        }
        if (canSave)
        {
            if (request.getParameter("docTemplate") != null)
            {
                template.setDocument(request.getParameter("docTemplate"));
            }

            if (request.getParameter("l2DocTemplate") != null)
            {
                template.setL2Document(request.getParameter("l2DocTemplate"));
            }

            template.setName(request.getParameter("name"));
            template.setL2Name(StringUtils.getValueForL2Fields(
                    request.getParameter("l2Name"), request.getParameter("name")));
            template.setAdditionalData(additionalParams);
            PortalUtils.getHt().saveOrUpdate(template);

            saveSherbrookeSSNFields(request);

            mv.addObject("templateSaved", Boolean.TRUE);
        }
    }

    private static void saveSherbrookeSSNFields(HttpServletRequest request)
    {
        if (PortalUtils.isSiteCode(PortalUtils.SHERBROOKE_SITE_CODE))
        {
            boolean reloadPortalConfigMap = false;

            if (!StringUtils.isEmpty(request.getParameter("ssnurl")))
            {
                PortalConfig pc = PortalConfigHelper
                        .getPortalConfig("SSN_URL_FRAGMENT");
                if (pc == null)
                {
                    pc = new PortalConfig();
                    reloadPortalConfigMap = true;
                }
                pc.setOrbisKey("SSN_URL_FRAGMENT");
                pc.setOrbisValue(request.getParameter("ssnurl"));
                PortalUtils.getHt().saveOrUpdate(pc);
            }
            if (!StringUtils.isEmpty(request.getParameter("ssnCert")))
            {
                PortalConfig pc = PortalConfigHelper
                        .getPortalConfig("SSN_CERTIFICATE_PATH");
                if (pc == null)
                {
                    pc = new PortalConfig();
                    reloadPortalConfigMap = true;
                }
                pc.setOrbisKey("SSN_CERTIFICATE_PATH");
                pc.setOrbisValue(request.getParameter("ssnCert"));
                PortalUtils.getHt().saveOrUpdate(pc);
            }
            if (!StringUtils.isEmpty(request.getParameter("ssnKeystore")))
            {
                PortalConfig pc = PortalConfigHelper
                        .getPortalConfig("SSN_KEYSTORE_PATH");
                if (pc == null)
                {
                    pc = new PortalConfig();
                    reloadPortalConfigMap = true;
                }
                pc.setOrbisKey("SSN_KEYSTORE_PATH");
                pc.setOrbisValue(request.getParameter("ssnKeystore"));
                PortalUtils.getHt().saveOrUpdate(pc);
            }

            if (reloadPortalConfigMap)
            {
                PortalConfigHelper.reloadPortalConfigMap();
            }
        }
    }

    public static ByteArrayOutputStream getTemplateOutputStream(
            HttpServletRequest request, DocTemplate template,
            JSONObject additionalParams, String docType) throws Exception
    {
        return getTemplateOutputStream(template, additionalParams,
                PortalUtils.getLocale(request), docType, request);
    }

    public static ByteArrayOutputStream getTemplateOutputStream(
            DocTemplate template, JSONObject additionalParams, Locale locale,
            String docType, HttpServletRequest request) throws Exception
    {
        template.setCurrentLanguage(locale.getLanguage());

        String documentUUID = template.getDefaultDocumentUUID(locale,
                additionalParams);

        Document doc = DocTemplateHelper.loadDocument(documentUUID);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        File file = FilePathUtils.getFileForUUID(documentUUID);

        if (file.getName().endsWith(".pdf"))
        {
            if (docType.equals("docx"))
            {
                try
                {
                    return getDocxByteArrayOutputStream(file);
                }
                catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
            else
            {
                return FileUtils.readFile(file);
            }
        }
        else
        {
            if (doc != null && doc.getMailMerge() != null)
            {
                List<DocumentModel> docModels = template.populateDocumentData(
                        additionalParams, locale, null,
                        doc.getMailMerge().getFieldNames(),
                        PortalUtils.getUserLoggedIn(request));

                if (!(docModels == null || docModels.isEmpty()))
                {
                    DocumentModel docModel = docModels.get(0);
                    doc = handlePotentialDocOverride(doc, docModel);

                    if (docModel != null)
                    {
                        applyDocModelToDoc(doc, docModel, locale);
                        if (docModel.getDocCallback() != null)
                        {
                            docModel.getDocCallback().onDocumentPopulated(doc);
                        }
                    }
                }

                // OUTCOME-3779 Commenting this out for now because it is removing
                // carriage returns and breaking doc templates
                // doc.accept(new RemoveEmptyRegions(true));
                doc.getMailMerge().deleteFields();

                Object[] saveFormatData = getTemplateSaveFormat(docType);
                doc.save(baos, (Integer) saveFormatData[1]);
            }
        }
        return baos;
    }

    private static ByteArrayOutputStream getDocxByteArrayOutputStream(File pdfFile)
            throws IOException
    {
        PDDocument pdfDocument = PDDocument.load(pdfFile);
        PDFTextStripper stripper = new PDFTextStripper();
        String pdfText = stripper.getText(pdfDocument);
        pdfDocument.close();

        XWPFDocument docxDocument = new XWPFDocument();
        docxDocument.createParagraph().createRun().setText(pdfText);

        ByteArrayOutputStream docxBaos = new ByteArrayOutputStream();
        docxDocument.write(docxBaos);
        docxBaos.close();

        return docxBaos;
    }

    private static Document handlePotentialDocOverride(Document defaultDoc,
            DocumentModel docModel)
    {
        if (docModel.getOverrideDocumentUUID() != null)
        {
            Document overrideDoc = DocTemplateHelper
                    .loadDocument(docModel.getOverrideDocumentUUID());
            if (overrideDoc != null && overrideDoc.getMailMerge() != null)
                defaultDoc = overrideDoc;
        }
        return defaultDoc;
    }

    private static void applyDocModelToDoc(Document doc, DocumentModel docModel,
            Locale locale) throws Exception
    {
        for (DocumentModelItem item : docModel.getItems())
        {
            if (item.getType() == DocumentModelItem.TYPE.BEAN)
            {
                if (item.getKey().equals("institutionLogo"))
                {
                    // to handle InfoSession Template institution logo image
                    DocumentBuilder builder = new DocumentBuilder(doc);
                    File file1 = FilePathUtils
                            .getFileForUUID(item.getValue().toString());
                    ByteArrayOutputStream x = FileUtils.readFile(file1);
                    builder.insertImage(x.toByteArray(),
                            RelativeHorizontalPosition.MARGIN, 0,
                            RelativeVerticalPosition.MARGIN, 0, 75, 75,
                            WrapType.SQUARE);
                }
                else
                {
                    addBean(doc, item, locale);
                }
            }
            else if (item.getType() == DocumentModelItem.TYPE.BEAN_LIST)
            {
                addBeanList(doc, item, locale);
            }
            else if (item.getType() == DocumentModelItem.TYPE.MAP)
            {
                addMap(doc, item, locale);
            }
            else if (item.getType() == DocumentModelItem.TYPE.FIELD)
            {

                addField(doc, item, locale);
            }
        }
    }

    public static ByteArrayOutputStream getMergedTemplateOutputStream(Locale locale,
            JSONObject additionalParams, List<Integer> itemIds,
            UserDetailsImpl userLoggedIn, String docType,
            List<DocTemplate> templates, boolean concatPdfs) throws Exception
    {
        List<InputStream> pdfs = new ArrayList<>();

        ByteArrayOutputStream finalBaos = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(finalBaos);
        for (DocTemplate template : templates)
        {
            getMergedTemplatesForBaos(pdfs, zip, template, additionalParams,
                    itemIds, userLoggedIn, locale, docType);

        }
        if (concatPdfs && pdfs.size() > 0)
        {
            finalBaos = DocHelper.concatPDFs(pdfs, false);
        }
        zip.close();
        return finalBaos;
    }

    public static ByteArrayOutputStream getMergedTemplateOutputStream(Locale locale,
            JSONObject additionalParams, List<Integer> itemIds,
            UserDetailsImpl userLoggedIn, String docType, DocTemplate template,
            boolean concatPdfs) throws Exception
    {

        List<InputStream> docs = new ArrayList<>();

        ByteArrayOutputStream finalBaos = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(finalBaos);

        getMergedTemplatesForBaos(docs, zip, template, additionalParams, itemIds,
                userLoggedIn, locale, docType);
        zip.close();

        if (concatPdfs && docs.size() > 0)
        {
            finalBaos = DocHelper.concatPDFs(docs, false);
        }

        return finalBaos;
    }

    public static void getMergedTemplatesForBaos(List<InputStream> docs,
            ZipOutputStream zip, DocTemplate template, JSONObject additionalParams,
            List<Integer> itemIds, UserDetailsImpl userLoggedIn, Locale locale,
            String docType) throws Exception
    {
        template.setCurrentLanguage(locale.getLanguage());
        String docUUID = "";
        if (LocaleUtils.isL1(locale))
        {
            docUUID = template.getDocument();
        }
        else
        {
            docUUID = !StringUtils.isEmpty(template.getL2Document())
                    ? template.getL2Document()
                    : template.getDocument();
        }

        Document originalDoc = DocTemplateHelper.loadDocument(docUUID);
        List<DocumentModel> docModels = new ArrayList<>();
        if (originalDoc != null)
        {
            docModels = template.populateDocumentData(additionalParams, locale,
                    itemIds, originalDoc.getMailMerge().getFieldNames(),
                    userLoggedIn);
        }

        OrbisThread documentThread = null;
        if (additionalParams.has("thread"))
        {
            documentThread = PortalUtils
                    .getOrbisThread(additionalParams.getString("thread"));
        }

        List<String> taxCreditIneligibleStudents = new ArrayList<>();

        for (DocumentModel docModel : docModels)
        {
            try
            {
                if (docModel != null && originalDoc != null)
                {
                    Document doc = handlePotentialDocOverride(originalDoc.deepClone(),
                            docModel);
                    applyDocModelToDoc(doc, docModel, locale);

                    if (docModel.getDocCallback() != null)
                    {
                        docModel.getDocCallback().onDocumentPopulated(doc);
                    }
                    String fileName = StringUtils.getRandomString();
                    String fileNameFromDocModel = (String) getDataWithinDocModel(
                            docModel, "fileName");
                    if (!StringUtils.isEmpty(fileNameFromDocModel))
                    {
                        fileName = fileNameFromDocModel;
                    }

                    // We need to look at removing some of this template
                    // specific code. The DocTemplateHelper was meant to
                    // refer
                    // to templates generically. This code would need to be
                    // moved to a controller or the specified template.

                    if (template instanceof CoopLetterTemplate
                            && CoopHelper.isTemplateTypeTaxLetter(template.getType()))
                    {
                        boolean taxCreditEligible = (boolean) Optional.ofNullable(
                                        getDataWithinDocModel(docModel, "taxCreditEligible"))
                                .orElse(true);

                        if (!taxCreditEligible)
                        {
                            String ineligibleStudentDetail = (String) getDataWithinDocModel(
                                    docModel, "ineligibityDetails");
                            taxCreditIneligibleStudents.add(ineligibleStudentDetail);
                            continue;
                        }
                    }

                    Object[] saveFormatData = getTemplateSaveFormat(docType);

                    String docExtension = SaveFormat
                            .getName((int) saveFormatData[1]) != null
                            ? SaveFormat.getName((int) saveFormatData[1])
                            : docType;
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ZipEntry zipEntry = new ZipEntry(fileName + "." + docExtension);

                    // doc.accept(new RemoveEmptyRegions(true));
                    doc.getMailMerge().deleteFields();

                    doc.save(baos, (Integer) saveFormatData[1]);
                    baos.close();
                    byte[] bytes = baos.toByteArray();
                    docs.add(new BufferedInputStream(new ByteArrayInputStream(bytes)));
                    zipEntry.setSize(bytes.length);
                    zip.putNextEntry(zipEntry);
                    zip.write(bytes);
                    zip.closeEntry();
                }

                if (documentThread != null)
                {
                    documentThread.setProgress(documentThread.getProgress() + 1);
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();

            }
        }

        if (!taxCreditIneligibleStudents.isEmpty())
        {
            ByteArrayOutputStream baos = getTaxCreditIneligibleStudentsSheet(
                    taxCreditIneligibleStudents, locale);
            if (baos != null)
            {
                baos.close();
                byte[] bytes = baos.toByteArray();
                docs.add(0,
                        new BufferedInputStream(new ByteArrayInputStream(bytes)));
                ZipEntry zipEntry = new ZipEntry("_IneligiblePlacements.pdf");
                zipEntry.setSize(bytes.length);
                zip.putNextEntry(zipEntry);
                zip.write(bytes);
                zip.closeEntry();
            }
        }
    }

    public static DocTemplate getTemplate(HttpServletRequest request)
            throws Exception
    {
        String className = request.getAttribute("templateType") != null
                ? (String) request.getAttribute("templateType")
                : request.getParameter("templateType");

        Class<? extends DocTemplate> templateType = ClassUtils.loadClass(className);

        DocTemplate template = null;

        if (!StringUtils.isEmpty(request.getParameter("templateId")))
        {
            template = (DocTemplate) PortalUtils.getHt().load(templateType,
                    Integer.valueOf(request.getParameter("templateId")));
        }
        else if (request.getAttribute("type") != null)
        {
            Integer type = (Integer) request.getAttribute("type");
            JSONObject additionalParams = DocTemplateHelper
                    .getAdditionalParams(request);
            template = getTemplate(type, additionalParams, templateType);
        }
        else
        {
            Integer type = Integer.valueOf(request.getParameter("type"));
            JSONObject additionalParams = DocTemplateHelper
                    .getAdditionalParams(request);
            template = getTemplate(type, additionalParams, templateType);

        }
        return template;
    }

    public static List<DocTemplate> getTemplates(HttpServletRequest request,
            OrbisModule module) throws Exception
    {
        String className = request.getAttribute("templateType") != null
                ? (String) request.getAttribute("templateType")
                : request.getParameter("templateType");
        Class<? extends DocTemplate> templateType = ClassUtils.loadClass(className);
        List<DocTemplate> templates = new LinkedList<>();
        if (module instanceof EXPModule)
        {
            Integer expTypeId = RequestUtils.getInteger(request, "expTypeId");
            if (expTypeId != null)
            {
                templates = PortalUtils.getHt().find(
                        "from " + templateType.getSimpleName()
                                + " e where e.experienceType.id=? and e.id in "
                                + DBUtils.buildInClause(
                                request.getParameterValues("templateIds")),
                        expTypeId);
            }
        }
        return templates;
    }

    public static DocTemplate getTemplate(Integer type, JSONObject additionalParams,
            Class<? extends DocTemplate> templateType) throws Exception
    {
        DocTemplate template = null;
        DocTemplate templateInstance = templateType.newInstance();

        templateInstance.setType(type);

        if (!(templateInstance instanceof EXPPostingTemplate))
        {
            String additionalHql = templateInstance
                    .getAdditionalHql(additionalParams);
            List<DocTemplate> templates = PortalUtils.getHt().find("from "
                    + templateType.getName() + " t where "
                    + (!StringUtils.isEmpty(additionalHql) ? additionalHql + " and "
                            : "")
                    + " t.type = ? and t.defaultForType = true", type);
            if (!templates.isEmpty())
            {
                template = templates.get(0);
            }
        }

        return template;
    }

    public static List<DocTemplate> getTemplates(Integer type,
            JSONObject additionalParams, Class<? extends DocTemplate> templateType)
            throws Exception
    {
        List<DocTemplate> templates = new ArrayList<DocTemplate>();
        DocTemplate templateInstance = templateType.newInstance();

        String additionalHql = templateInstance.getAdditionalHql(additionalParams);
        templates = PortalUtils.getHt()
                .find("from " + templateType.getName() + " t where "
                        + (!StringUtils.isEmpty(additionalHql)
                        ? additionalHql + " and "
                        : "")
                        + " t.type = ? ", type);

        return templates;
    }

    public static Object[] getTemplateSaveFormat(String docType)
    {
        int saveFormatInt = -1;
        if ("docx".equals(docType))
        {
            saveFormatInt = SaveFormat.DOCX;
        }
        else if ("pdf".equals(docType) || "zip".equals(docType))
        {
            saveFormatInt = SaveFormat.PDF;
        }
        return new Object[] { docType, saveFormatInt };
    }

    public static Object[] getTemplateSaveFormat(HttpServletRequest request)
    {
        return getTemplateSaveFormat(request.getParameter("docType"));
    }

    public static ModelAndView getMergedTemplate(HttpServletRequest request,
            JSONObject additionalParams, List<Integer> itemIds) throws Exception
    {
        Locale locale = PortalUtils.getLocale(request);
        DocTemplate template = DocTemplateHelper.getTemplate(request);
        Object[] saveFormatData = DocTemplateHelper.getTemplateSaveFormat(request);
        String docType = request.getParameter("docType");
        ByteArrayOutputStream baos = DocTemplateHelper
                .getMergedTemplateOutputStream(locale, additionalParams, itemIds,
                        PortalUtils.getUserLoggedIn(request), docType, template,
                        !"zip".equals(saveFormatData[0]));
        String docName = !StringUtils.isEmpty(request.getParameter("documentName"))
                ? request.getParameter("documentName")
                : (LocaleUtils.isL1(locale) ? template.getName()
                : template.getL2Name());
        return new ModelAndView(
                new ByteArrayDownloadView(docName + "." + saveFormatData[0], baos));
    }

    public static JSONObject getAdditionalParams(HttpServletRequest request)
            throws Exception
    {
        JSONObject additionalParams = new JSONObject();
        if (!StringUtils.isEmpty(request.getParameter("additionalParams")))
        {
            additionalParams = new JSONObject(request
                    .getParameter("additionalParams").replace("&quot;", "\""));
        }
        else if (request.getAttribute("additionalParams") != null)
        {
            additionalParams = (JSONObject) request
                    .getAttribute("additionalParams");
        }
        return additionalParams;
    }

    public static void applyLicense()
    {
        try
        {
            if (!licenseApplied)
            {
                License license = new License();
                license.setLicense(License.class.getClassLoader()
                        .getResourceAsStream("Aspose.Words.lic"));
                licenseApplied = true;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void addFormattedDfModel(DocumentModel docModel,
            DFAnswerEntity answers, DFAbstractModelEntity dfModelEntity, String key,
            UserDetailsImpl userLoggedIn, Locale locale) throws Exception
    {
        Map<String, Map<String, String>> qaMap = getMappedDFQuestionFormattedAnswers(
                answers, dfModelEntity, userLoggedIn, locale);
        docModel.addItem(
                new DocumentModelItem(key, new LinkedList(qaMap.entrySet())));
    }

    public static Map<String, Map<String, String>> getMappedDFQuestionFormattedAnswers(
            DFAnswerEntity answers, DFAbstractModelEntity dfModelEntity,
            UserDetailsImpl userLoggedIn, Locale locale)
            throws IllegalAccessException, InvocationTargetException,
            NoSuchMethodException
    {
        DFHelper.populateModel(dfModelEntity.getDFModel(), false, answers,
                userLoggedIn);
        return getMappedDFQuestionFormattedAnswers(answers, dfModelEntity, locale);
    }

    public static Map<String, Map<String, String>> getMappedDFQuestionFormattedAnswers(
            DFAnswerEntity answers, DFAbstractModelEntity dfModelEntity,
            Locale locale) throws IllegalAccessException, InvocationTargetException,
            NoSuchMethodException
    {
        Map<String, Map<String, String>> qaMap = new LinkedHashMap<>();
        for (DFCategory category : dfModelEntity.getDFModel().getCategories())
        {
            Map<String, String> questions = qaMap.getOrDefault(category.getName(),
                    new LinkedHashMap<>());
            for (DFQuestion question : category.getQuestions())
            {
                if (question.isCanRead() || question.isCanWrite())
                {
                    questions.put(question.getQuestionText(), DFHelper
                            .getFormattedDFAnswer(question, answers, locale));
                }
            }
            if (!questions.isEmpty())
            {
                qaMap.put(category.getName(), questions);
            }
        }
        return qaMap;
    }

    public static Map<String, Object> getMappedDFFormattedFields(
            DFAnswerEntity answers, DFAbstractModelEntity dfModelEntity,
            UserDetailsImpl userLoggedIn, Locale locale)
            throws IllegalAccessException, InvocationTargetException,
            NoSuchMethodException
    {
        DFHelper.populateModel(dfModelEntity.getDFModel(), false, answers,
                userLoggedIn);
        return getMappedDFFormattedFields(answers, dfModelEntity, locale);
    }

    public static Map<String, Object> getMappedDFFormattedFields(
            DFAnswerEntity answers, DFModelEntity dfModelEntity, Locale locale)
            throws IllegalAccessException, InvocationTargetException,
            NoSuchMethodException
    {

        Map<String, Object> templateDTO = new HashMap<>();
        templateDTO.put(DocTemplateHelper.NOT_DATASOURCE, true);
        templateDTO.put("entity", answers);
        for (DFCategory category : dfModelEntity.getDFModel().getCategories())
        {
            for (DFQuestion question : category.getQuestions())
            {
                if (question.isCanRead() || question.isCanWrite())
                {
                    String answer = DFHelper.getFormattedDFAnswer(question, answers,
                            locale);
                    Map<String, Object> df = new HashMap<>();
                    df.put(DocTemplateHelper.NOT_DATASOURCE, true);
                    df.put("category", category.getName());
                    df.put("question", question.getQuestionText());
                    df.put("answer", answer);
                    templateDTO.put(question.getAnswerField1(), df);
                }
            }
        }
        return templateDTO;
    }

    public static Map<String, Object> getAdditionalWorkflowDocumentModelFromRecord(
            UserDetailsImpl userLoggedIn, DFAnswerEntity answers, Locale locale)
            throws Exception
    {
        Map<String, Object> rec = new HashMap<>();

        if ((answers instanceof EXPRecordAbstract subRecord
                && subRecord.getStudentStep() != null) ||
                (answers instanceof EXPRecord record
                        && record.getStudentStep() != null))
        {

            EXPTermCourseTypeStep step = (answers instanceof EXPRecordAbstract)
                    ? ((EXPRecordAbstract) answers).getStudentStep().getStep()
                    : ((EXPRecord) answers).getStudentStep().getStep();

            EXPTypeWorkflowExtraForm workflowForm = EXPTypeWorkflowExtraFormHelper
                    .getWorkflowExtraFormForTCTStep(step);

            if (workflowForm != null)
            {
                int recordId = answers.getId();

                DFAnswerEntity workflowStepSubRecord = PortalUtils.getHt().getFirst(
                        "from " + workflowForm.getAnswerEntityClass()
                                .getSimpleName()
                                + " rwa where rwa."
                                + workflowForm.getParentRecordFieldName()
                                + ".id=? and rwa.dfModel=?",
                        new Object[] { recordId, workflowForm.getDFModel() });

                DFHelper.populateModel(workflowForm.getDFModel(), false,
                        workflowStepSubRecord, userLoggedIn);

                Map<String, Map<String, String>> qaMap = DocTemplateHelper
                        .getMappedDFQuestionFormattedAnswers(workflowStepSubRecord,
                                (DFAbstractModelEntity) workflowForm, userLoggedIn,
                                locale);

                rec.put(DocTemplateHelper.NOT_DATASOURCE, true);
                rec.put("fields", new LinkedList<>(qaMap.entrySet()));
            }
        }
        return rec;
    }

    public static void populateAndSendEmail(Integer type,
            Class<? extends DocTemplate> templateType, JSONObject additionalParams,
            Locale locale, String docName, HttpServletRequest request)
            throws Exception
    {
        DocTemplate template = getTemplate(type, additionalParams, templateType);

        if (StringUtils.isEmpty(docName))
        {
            docName = LocaleUtils.isL1(locale) ? template.getName()
                    : template.getL2Name();
        }

        EmailModel emailModel = new EmailModel();

        template.setEmailModelDefaults(additionalParams, emailModel);

        EmailAttachment attachment = new EmailAttachment();
        Object[] saveFormatData = DocTemplateHelper.getTemplateSaveFormat("pdf");
        String fileName = docName + "." + saveFormatData[0];
        attachment.setName(fileName);
        attachment.setMimeType(FileUtils.getMimeType(fileName));

        ByteArrayOutputStream baos = getTemplateOutputStream(template,
                additionalParams, locale, "pdf", request);
        attachment.setBytes(baos.toByteArray());

        List<EmailRecipient> recipients = template
                .getEmailRecipients(additionalParams, request);

        emailModel.setEmailRecipients(recipients);

        Map<String, List<String>> emails = EmailUtils
                .getEmailModelEmailsMap(emailModel);
        EmailUtils.sendAndLogEmails(emailModel.getSubject(), emailModel.getBody(),
                emailModel.getBody(), emailModel.getFromAddress(), emails,
                new EmailAttachment[] { attachment }, PortalUtils.getSiteBaseUrl(),
                request);
    }

    public static Map<Integer, Object> prepRawValuesArray(Object[] valArray)
    {
        Map<Integer, Object> ret = new LinkedHashMap<Integer, Object>();
        for (int i = 0; i < valArray.length; i++)
        {
            ret.put(i, valArray[i]);
        }
        return ret;
    }

    public static void handleFieldMerge(Object bean, String property,
            Object[] fieldValue, Locale locale)
    {
        try
        {
            Pattern pattern = Pattern.compile("([A-Za-z0-9.]+)(\\((.*)\\))?");
            Matcher matcher = pattern.matcher(property);
            String[] args = new String[] {};
            if (matcher.find())
            {
                if (matcher.group(1) != null)
                {
                    property = matcher.group(1);
                }
                if (matcher.group(3) != null)
                {
                    args = matcher.group(3).split(",");
                }
            }

            if (property.contains("."))
            {
                // this will make sure we have the object that we are actually
                // pulling the value from so that we can figure out exactly
                // which object type we are dealing with
                bean = PropertyUtils.getProperty(bean,
                        property.substring(0, property.lastIndexOf(".")));
                property = property.substring(property.lastIndexOf(".") + 1);
            }

            Object foundVal = PropertyUtils.getProperty(bean, property);

            if (foundVal != null && bean instanceof DocTemplateObject
                    && ((DocTemplateObject) bean).getFieldStrings(locale) != null
                    && ((DocTemplateObject) bean).getFieldStrings(locale)
                    .containsKey(property)
                    && ((DocTemplateObject) bean).getFieldStrings(locale)
                    .get(property).containsKey(foundVal.toString()))
            {
                foundVal = ((DocTemplateObject) bean).getFieldStrings(locale)
                        .get(property).get(foundVal.toString());

                if (foundVal != null && foundVal instanceof String
                        && ((String) foundVal).startsWith("i18n"))
                {
                    foundVal = PortalUtils.getMessageSource()
                            .getMessage((String) foundVal, null, locale);
                }
            }

            if (foundVal != null)
            {
                fieldValue[0] = HtmlUtils.stripHtml(DocTemplateHelper
                        .handleFieldMergeValue(foundVal, args, locale));
            }
        }
        catch (Exception e)
        {

        }
    }

    public static ByteArrayOutputStream getTaxCreditCoverSheet(
            LinkedList<Object[]> taxCreditEligibleWtrInfo,
            LinkedList<Object[]> taxCreditIneligibleWtrInfo, Locale locale)
            throws Exception
    {
        MessageSource ms = PortalUtils.getMessageSource();
        Document doc = new Document();
        DocumentBuilder builder = new DocumentBuilder(doc);
        Font font = builder.getFont();

        font.setName("Arial");

        builder.getCurrentParagraph().getParagraphFormat().getShading()
                .setBackgroundPatternColor(java.awt.SystemColor.LIGHT_GRAY);
        font.setSize(24);
        font.setColor(java.awt.SystemColor.WHITE);
        builder.write(ms.getMessage("i18n.DocTemplateHelper.TaxCreditLetters", null,
                "", locale));

        builder.insertParagraph();
        builder.getCurrentParagraph().getParagraphFormat().clearFormatting();
        builder.insertBreak(BreakType.PARAGRAPH_BREAK);
        font.setSize(12);
        font.setColor(java.awt.SystemColor.BLACK);
        String notes = ms.getMessage(
                "i18n.DocTemplateHelper.TaxCreditAdditionalNotes", null, "",
                locale);
        if (!StringUtils.isEmpty(notes))
        {
            builder.writeln(notes);
            builder.insertBreak(BreakType.LINE_BREAK);
            builder.insertBreak(BreakType.LINE_BREAK);
        }
        font.setBold(true);
        builder.writeln(ms.getMessage(
                "i18n.DocTemplateHelper.EligibleForTaxCr378384", null, "", locale));
        builder.insertBreak(BreakType.LINE_BREAK);
        font.setSize(10);
        font.setBold(false);
        for (Object[] wtrInfo : taxCreditEligibleWtrInfo)
        {
            builder.writeln(wtrInfo[2] + " - " + wtrInfo[0] + " " + wtrInfo[1]);
        }
        builder.insertBreak(BreakType.LINE_BREAK);
        builder.insertBreak(BreakType.LINE_BREAK);
        font.setSize(12);
        font.setBold(true);
        builder.writeln(ms.getMessage(
                "i18n.DocTemplateHelper.NotEligibleForTa467894", null, "", locale));
        builder.insertBreak(BreakType.LINE_BREAK);
        font.setSize(10);
        font.setBold(false);
        for (Object[] wtrInfo : taxCreditIneligibleWtrInfo)
        {
            builder.writeln(wtrInfo[2] + " - " + wtrInfo[0] + " " + wtrInfo[1]);
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        doc.save(baos, SaveFormat.PDF);
        return baos;
    }

    public static ByteArrayOutputStream getTaxCreditIneligibleStudentsSheet(
            List<String> ineligibleStudents, Locale locale)
    {
        ByteArrayOutputStream baos = null;

        try
        {
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);
            Font font = builder.getFont();
            font.setName("Arial");
            font.setBold(true);
            builder.writeln(PortalUtils.getI18nMessage(
                    "i18n.DocTemplateHelper.NotEligibleForTa467894", locale));
            font.setBold(false);
            builder.insertBreak(BreakType.LINE_BREAK);
            for (String detail : ineligibleStudents)
            {
                builder.writeln(detail);
            }
            baos = new ByteArrayOutputStream();
            doc.save(baos, SaveFormat.PDF);
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
        }

        return baos;
    }

    public static AsposeBundle saveAsposeBundle(String[] paths,
            UserDetailsImpl userLoggedIn)
    {
        AsposeBundle bundle = new AsposeBundle();
        bundle.setCreatedOn(new Date());
        bundle.setCreatedBy(userLoggedIn);
        bundle.setPdfUrl(paths[1]);
        Double d = Math.floor(Math.random() * 1000000);
        bundle.setConfirmationCode(String.valueOf(d.intValue()));
        PortalUtils.getHt().save(bundle);

        return bundle;
    }

    public static Object getDataWithinDocModel(DocumentModel docModel, String data)
    {
        Object ret = null;

        List<Object> dataFromDocModel = docModel.getItems().stream()
                .filter(d -> d.getKey().equals(data)).map(d -> d.getValue())
                .collect(Collectors.toList());

        if (!dataFromDocModel.isEmpty())
        {
            ret = dataFromDocModel.get(0);
        }

        return ret;
    }

    public static void initAspose()
    {
        Runnable initAspose = () -> {
            DocTemplateHelper.applyLicense();
            try
            {
                Document doc = DocTemplateHelper.loadDocument(
                        "/WEB-INF/conf/averyBadgeDocs/name_badges_averyId_5384.docx");
                doc.updateFields();
                doc.save(new ByteArrayOutputStream(), SaveFormat.PDF);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        };
        new Thread(initAspose).start();
    }

    public static Map<String, String> getEmailDocTemplateAdditionalParametersCommon(
            JSONObject additionalParamsJson, HttpServletRequest request)
            throws JSONException
    {
        Map<String, String> additionalParams = new HashMap<>();

        for (Iterator<Object> iterator = additionalParamsJson.keys(); iterator
                .hasNext();)
        {
            String key = (String) iterator.next();
            additionalParams.put(key, additionalParamsJson.get(key).toString());
        }

        additionalParams.put("additionalParams", additionalParamsJson.toString());
        additionalParams.put("type", request.getParameter("type"));
        additionalParams.put("templateId", request.getParameter("templateId"));
        additionalParams.put("templateType", request.getParameter("templateType"));
        additionalParams.put("docType", request.getParameter("docType"));
        return additionalParams;
    }

    private static void deleteDocTemplate(HttpServletRequest request)
            throws Exception
    {
        Class templateType = ClassUtils
                .loadClass(request.getParameter("templateType"));
        DocTemplate template = (DocTemplate) PortalUtils.getHt().load(templateType,
                Integer.valueOf(request.getParameter("templateId")));
        JSONObject additionalParams = DocTemplateHelper
                .getAdditionalParams(request);
        if (template.isDefaultForType())
        {
            String additionalHql = template.getAdditionalHql(additionalParams);
            PortalUtils.getJt().update("update t set defaultForType = 1 from "
                    + template.getTableName()
                    + " t where id in (select top 1 id from "
                    + template.getTableName() + " where "
                    + (!StringUtils.isEmpty(additionalHql) ? additionalHql + " and "
                    : "")
                    + " type = " + template.getType() + " and defaultForType = 0)");
        }
        PortalUtils.getHt().delete(template);
        FlashMessageUtils.success(request,
                "i18n.coop_coopLettersConfig.Lettertemp39924015825903847");
    }

    private static boolean existsTemplate(Class<? extends DocTemplate> clazz,
            Integer type, String additionalHql)
    {
        return PortalUtils.getHt()
                .findInt("SELECT COUNT(t.id) FROM " + clazz.getName()
                                + " t WHERE t.type = ? "
                                + (!StringUtils.isEmpty(additionalHql)
                                ? " AND " + additionalHql
                                : ""),
                        type) > 0;
    }
}