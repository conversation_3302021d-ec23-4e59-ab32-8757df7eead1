package com.orbis.docTemp;

import com.aspose.words.IMailMergeDataSource;
import org.apache.commons.beanutils.PropertyUtils;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class MailMergeGroupListDataSource implements IMailMergeDataSource
{

    private MailMergeGroupListInstructionDataSource dataSource;

    private Object currentRecord;

    private List items;

    private Iterator iterator;

    private String key;

    private Locale locale;

    private boolean ranOnce = false;

    public MailMergeGroupListDataSource(String key, Locale locale,
            MailMergeGroupListInstructionDataSource dataSource)
    {
        this.locale = locale;
        this.key = key;
        this.dataSource = dataSource;
    }

    @Override
    public String getTableName() throws Exception
    {
        return key;
    }

    @Override
    public boolean moveNext() throws Exception
    {
        boolean hasNext = false;
        if (items == null && dataSource.getGrouped() != null && iterator == null)
        {
            items = dataSource.getGrouped();
            iterator = dataSource.getGrouped().iterator();
        }
        if (iterator != null)
        {
            hasNext = iterator.hasNext();
            if (hasNext)
            {
                currentRecord = iterator.next();
            }
            else
            {
                iterator = items.iterator();
                currentRecord = null;
            }
        }
        return hasNext;
    }

    @Override
    public boolean getValue(String fieldName, Object[] fieldValue) throws Exception
    {

        if ((!ranOnce && currentRecord != null) || ranOnce)
        {
            if ("counter".equals(fieldName))
            {
                fieldValue[0] = items.indexOf(this.currentRecord) + 1;
            }
            else
            {
                DocTemplateHelper.handleFieldMerge(this.currentRecord, fieldName,
                        fieldValue, locale);
            }

            if (fieldValue[0] == null)
            {
                fieldValue[0] = "";
            }
        }

        ranOnce = true;

        return true;
    }

    @Override
    public IMailMergeDataSource getChildDataSource(String fieldName)
            throws Exception
    {
        IMailMergeDataSource source = null;
        if (this.currentRecord != null)
        {
            try
            {
                Object foundVal = PropertyUtils.getProperty(this.currentRecord,
                        fieldName);

                if (foundVal instanceof List)
                {
                    source = new MailMergeBeanListDataSource((List) foundVal,
                            fieldName, locale);
                }
                else if (foundVal instanceof Map)
                {
                    Map foundValMap = (Map) foundVal;

                    if (!(foundValMap.containsKey(DocTemplateHelper.NOT_DATASOURCE)
                            && (Boolean) foundValMap
                                    .get(DocTemplateHelper.NOT_DATASOURCE)))
                    {
                        source = new MailMergeBeanListDataSource(
                                new LinkedList(foundValMap.entrySet()), fieldName,
                                locale);
                    }

                }
            }
            catch (Exception e)
            {

            }
        }

        return source;
    }
}
