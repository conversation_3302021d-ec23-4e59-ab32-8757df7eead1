package com.orbis.docTemp;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.beanutils.PropertyUtils;

import com.aspose.words.IMailMergeDataSource;

public class MailMergeBeanListDataSource implements IMailMergeDataSource
{

    private Object currentRecord;

    private List items;

    private Iterator iterator;

    private String key;

    private Locale locale;

    private boolean ranOnce = false;

    public MailMergeBeanListDataSource(List items, String key, Locale locale)
    {
        this.iterator = items.iterator();
        this.key = key;
        this.items = items;
        this.locale = locale;
    }

    @Override
    public String getTableName() throws Exception
    {
        return key;
    }

    @Override
    public boolean getValue(String fieldName, Object[] fieldValue) throws Exception
    {

        if ((!ranOnce && currentRecord != null) || ranOnce)
        {
            if ("counter".equals(fieldName))
            {
                fieldValue[0] = items.indexOf(this.currentRecord) + 1;
            }
            else
            {
                DocTemplateHelper.handleFieldMerge(this.currentRecord, fieldName,
                        fieldValue, locale);
            }

            if (fieldValue[0] == null)
            {
                fieldValue[0] = "";
            }
        }

        ranOnce = true;

        return true;
    }

    @Override
    public boolean moveNext() throws Exception
    {
        boolean hasNext = iterator.hasNext();
        if (hasNext)
        {
            currentRecord = iterator.next();
        }
        else
        {
            iterator = items.iterator();
            currentRecord = null;
        }

        return hasNext;
    }

    @Override
    public IMailMergeDataSource getChildDataSource(String fieldName)
            throws Exception
    {
        IMailMergeDataSource source = null;

        if (this.currentRecord != null)
        {
            try
            {
                Object foundVal = PropertyUtils.getProperty(this.currentRecord,
                        fieldName);

                if (foundVal instanceof List)
                {
                    source = new MailMergeBeanListDataSource((List) foundVal,
                            fieldName, locale);
                }
                else if (foundVal instanceof Map)
                {
                    Map foundValMap = (Map) foundVal;

                    if (!(foundValMap.containsKey(DocTemplateHelper.NOT_DATASOURCE)
                            && (Boolean) foundValMap
                                    .get(DocTemplateHelper.NOT_DATASOURCE)))
                    {
                        source = new MailMergeBeanListDataSource(
                                new LinkedList(foundValMap.entrySet()), fieldName,
                                locale);
                    }

                }
            }
            catch (Exception e)
            {

            }
        }

        return source;
    }
}
