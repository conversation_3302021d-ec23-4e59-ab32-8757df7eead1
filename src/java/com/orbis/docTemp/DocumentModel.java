package com.orbis.docTemp;

import java.util.LinkedList;
import java.util.List;

public class DocumentModel
{
    private List<DocumentModelItem> items = new LinkedList<DocumentModelItem>();

    private DocumentCallback docCallback;

    private String overrideDocumentUUID;

    public void addItem(DocumentModelItem item)
    {
        items.add(item);
    }

    public List<DocumentModelItem> getItems()
    {
        return items;
    }

    public DocumentCallback getDocCallback()
    {
        return docCallback;
    }

    public void setDocCallback(DocumentCallback docCallback)
    {
        this.docCallback = docCallback;
    }

    public String getOverrideDocumentUUID()
    {
        return overrideDocumentUUID;
    }

    public void setOverrideDocumentUUID(String overrideDocumentUUID)
    {
        this.overrideDocumentUUID = overrideDocumentUUID;
    }
}
