package com.orbis.docTemp;

import java.util.Locale;

import com.aspose.words.IMailMergeDataSource;

public class MailMergeBeanDataSource implements IMailMergeDataSource
{
    private Object bean;

    private String key;

    private Locale locale;

    boolean ranOnce = false;

    public MailMergeBeanDataSource(Object bean, String key, Locale locale)
    {
        this.bean = bean;
        this.key = key;
        this.locale = locale;
    }

    /**
     * The name of the data source. Used by Aspose.Words only when executing
     * mail merge with repeatable regions.
     */
    public String getTableName() throws Exception
    {
        return this.key;
    }

    public boolean getValue(String fieldName, Object[] fieldValue) throws Exception
    {
        boolean replaceable = fieldName.startsWith(key);
        if (replaceable)
        {
            DocTemplateHelper.handleFieldMerge(bean,
                    fieldName.substring(fieldName.indexOf(".") + 1), fieldValue,
                    locale);
        }

        return replaceable;
    }

    public boolean moveNext() throws Exception
    {
        boolean runNow = !ranOnce;
        ranOnce = true;
        return runNow;
    }

    @Override
    public IMailMergeDataSource getChildDataSource(String arg0) throws Exception
    {
        return null;
    }
}
