package com.orbis.docTemp;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.orbis.utils.ClassUtils;

public class DocumentModelItem
{
    public static enum TYPE
    {
        BEAN,
        BEAN_LIST,
        MAP,
        FIELD
    }

    private String key;

    private Object value;

    public DocumentModelItem(String key, Object value)
    {
        this.key = key;
        this.value = value;
    }

    public String getKey()
    {
        return key;
    }

    public void setKey(String key)
    {
        this.key = key;
    }

    public Object getValue()
    {
        return value;
    }

    public void setValue(Object value)
    {
        this.value = value;
    }

    public TYPE getType()
    {
        TYPE ret = null;

        if (value != null)
        {
            if (value instanceof Map)
            {
                ret = TYPE.MAP;
            }
            else if (value instanceof List || value instanceof Set)
            {
                ret = TYPE.BEAN_LIST;
            }
            else if (ClassUtils.isWrapperOrString(value.getClass()))
            {
                ret = TYPE.FIELD;
            }
            else
            {
                ret = TYPE.BEAN;
            }
        }

        return ret;
    }
}
