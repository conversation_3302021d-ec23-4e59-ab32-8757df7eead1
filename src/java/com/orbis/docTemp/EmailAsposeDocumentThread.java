package com.orbis.docTemp;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.axis.utils.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.MessageSource;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.OrbisThread;
import com.orbis.web.content.aspose.AsposeBundle;

public class EmailAsposeDocumentThread extends OrbisThread
{
    private String threadName = "Email Aspose Documents Thread";

    private int[] recordIds;

    private int moduleId;

    private DocTemplate template;

    private String email;

    private HttpServletRequest request;

    private UserDetailsImpl user;

    public EmailAsposeDocumentThread(UserDetailsImpl user,
            HttpServletRequest request, int[] recordIds, int moduleId,
            DocTemplate template, String email)
    {
        super(user);
        this.user = user;
        this.recordIds = recordIds;
        this.moduleId = moduleId;
        this.template = template;
        this.email = email;
        this.request = request;
        threadName = threadName + " " + NumberUtils.getRandomInt(1000, 9999);
    }

    @Override
    public String getThreadName()
    {
        return threadName;
    }

    @Override
    public void doRun() throws Exception
    {
        setTotal(recordIds.length);

        Locale locale = new Locale(request.getParameter("locale"));
        JSONObject additionalParams = new JSONObject();
        additionalParams.put("moduleId", moduleId);
        additionalParams.put("recordIds", new JSONArray(recordIds));
        additionalParams.put("thread", threadName);
        ByteArrayOutputStream baos = DocTemplateHelper
                .getMergedTemplateOutputStream(locale, additionalParams, null, user,
                        "pdf", template, true);

        String documentName = LocaleUtils.isL1(locale) ? template.getName()
                : template.getL2Name();
        if (StringUtils.isEmpty(documentName))
        {
            documentName = "Documents";
        }

        /*
         * EmailAttachment[] attachments = new EmailAttachment[1];
         * EmailAttachment attachment = new EmailAttachment();
         * attachment.setMimeType("application/pdf");
         * attachment.setName(documentName + ".pdf");
         * attachment.setBytes(baos.toByteArray()); attachments[0] = attachment;
         */

        List<String> recipients = new ArrayList<String>();
        recipients.add(email);

        int defaultSenderId = 1;
        List<Integer> defaultSender = PortalUtils.getHt().find(
                "select immnru.user.id from InteractionModuleMessageNoRecipientUser immnru");
        if (!defaultSender.isEmpty())
        {
            defaultSenderId = defaultSender.get(0);
        }
        List<String> defaultSenderEmail = PortalUtils.getHt().find(
                "select u.emailAddress from UserDetailsImpl u where u.id=?",
                defaultSenderId);

        MessageSource ms = PortalUtils.getMessageSource();
        String emailMessage = ms.getMessage(
                "i18n.EmailAsposeDocumentsThread.Attached", null, "Attached:",
                locale) + " " + documentName;

        String strPath = PortalUtils.getRealPath("/");
        String storageFolderPath = FileUtils
                .fixFileName(strPath + "/content/private/asposedocs/");
        File storageFolder = new File(storageFolderPath);
        storageFolder.mkdirs();
        String rnd = ("" + Math.random()).substring(2)
                + ("" + Math.random()).substring(2);
        String relFolder = "/content/private/asposedocs/" + rnd;
        String folder = strPath + relFolder;
        folder = FileUtils.fixFileName(folder);
        FileUtils.createDir(folder);
        String fileName = FileUtils.cleanFileName(documentName) + ".pdf";
        String[] paths = new String[] { folder + "/" + fileName,
                relFolder + "/" + fileName };

        OutputStream bos = new BufferedOutputStream(
                new FileOutputStream(new File(paths[0])));
        bos.write(baos.toByteArray());
        bos.close();

        AsposeBundle bundle = DocTemplateHelper.saveAsposeBundle(paths, user);

        emailMessage += "<br><br>" + "<a href='" + bundle.getFileServiceUrl() + "'>"
                + ms.getMessage("i18n.EmailAsposeDocumentsThread.DownloadDocument",
                        null, "Download Document", locale)
                + "</a><br>"
                + ms.getMessage("i18n.EmailAsposeDocumentsThread.ConfirmationCode",
                        null, "Confirmation Code:", locale)
                + " " + bundle.getConfirmationCode();

        EmailUtils.sendAndLogEmails(documentName, HtmlUtils.stripHtml(emailMessage),
                emailMessage, defaultSenderEmail.get(0), recipients, null, user);
    }

}
