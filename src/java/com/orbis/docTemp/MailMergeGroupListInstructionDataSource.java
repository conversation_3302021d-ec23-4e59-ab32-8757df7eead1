package com.orbis.docTemp;

import com.aspose.words.IMailMergeDataSource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.orbis.utils.StringUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

public class MailMergeGroupListInstructionDataSource implements IMailMergeDataSource
{

    private List items;

    private String key;

    private Locale locale;

    private boolean moveNext = true;

    private List<Map.Entry> grouped;

    public MailMergeGroupListInstructionDataSource(List items, String key,
            Locale locale)
    {
        this.key = key;
        this.items = items;
        this.locale = locale;
    }

    @Override
    public String getTableName() throws Exception
    {
        return key;
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean getValue(String fieldName, Object[] fieldValue) throws Exception
    {
        if (StringUtils.isNotEmpty(fieldName)
                && fieldName.contains("groupBy.groupBy#") && grouped == null)
        {
            String[] split = fieldName.substring("groupBy.groupBy#".length())
                    .split("#");
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map.Entry> groupedList = new LinkedList<>(
                    ((Map<Object, Object>) items.stream()
                            .collect(Collectors.groupingBy(item -> {
                                Object groupField = item;
                                for (String field : split)
                                {
                                    if (groupField instanceof Map)
                                    {
                                        groupField = ((Map) groupField).get(field);
                                    }
                                    else if (groupField != null)
                                    {
                                        groupField = objectMapper
                                                .convertValue(groupField, Map.class)
                                                .get(field);
                                    }
                                }
                                if (groupField instanceof Map)
                                {
                                    ((Map) groupField).put(
                                            DocTemplateHelper.NOT_DATASOURCE, true);
                                }
                                return groupField != null ? groupField : "";
                            }))).entrySet());
            grouped = groupedList;
            items = groupedList;
        }
        return false;
    }

    @Override
    public boolean moveNext() throws Exception
    {
        boolean move = moveNext;
        moveNext = false;
        return move;
    }

    @Override
    public IMailMergeDataSource getChildDataSource(String fieldName)
            throws Exception
    {
        return null;
    }

    public List<Map.Entry> getGrouped()
    {
        return grouped;
    }
}
