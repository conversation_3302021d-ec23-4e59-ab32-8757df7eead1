package com.orbis.docTemp;

import java.util.Stack;

import com.aspose.words.Cell;
import com.aspose.words.ControlChar;
import com.aspose.words.DocumentVisitor;
import com.aspose.words.FieldEnd;
import com.aspose.words.FieldSeparator;
import com.aspose.words.FieldStart;
import com.aspose.words.FieldType;
import com.aspose.words.Node;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.aspose.words.Run;
import com.aspose.words.Table;
import com.aspose.words.VisitorAction;

class RemoveEmptyRegions extends DocumentVisitor
{
    private final String START_REGION_TEXT = "tablestart";

    private final String END_REGION_TEXT = "tableend";

    private Cell mStartCell;

    private boolean mRemoveParentContainer;

    private static int mRegionDepth = 0;

    private Stack mFieldStack = new Stack();

    public RemoveEmptyRegions()
    {

    }

    public RemoveEmptyRegions(boolean removeContainer)
    {
        mRemoveParentContainer = removeContainer;
    }

    public int visitFieldStart(FieldStart fieldStart) throws Exception
    {
        if (fieldStart.getFieldType() == FieldType.FIELD_MERGE_FIELD)
        {
            String fieldCode = getFieldCode(fieldStart);
            mFieldStack.add(fieldCode);

            if (fieldCode.toLowerCase().contains(START_REGION_TEXT))
            {
                if (isAtOuterRegion() && isInsideTable(fieldStart))
                {
                    mStartCell = (Cell) fieldStart.getAncestor(NodeType.CELL);
                }

                mRegionDepth++;
            }
        }

        if (isInsideRegion())
        {
            fieldStart.remove();
        }

        return VisitorAction.CONTINUE;

    }

    public int visitFieldEnd(FieldEnd fieldEnd)
    {
        if (fieldEnd.getFieldType() == FieldType.FIELD_MERGE_FIELD)
        {
            String fieldStartCode = (String) mFieldStack.pop();

            if (fieldStartCode.toLowerCase().contains(END_REGION_TEXT))
            {
                mRegionDepth--;

                if (isAtOuterRegion())
                {
                    if (isInsideTable(fieldEnd) && mRemoveParentContainer)
                    {
                        if (mStartCell.equals(fieldEnd.getAncestor(NodeType.CELL)))
                        {
                            mStartCell.getParentRow().remove();
                        }
                        else
                        {
                            mStartCell.getParentRow().getParentTable().remove();
                        }
                    }

                    fieldEnd.getParentParagraph().remove();
                }
            }

            if (isInsideRegion())
            {
                fieldEnd.remove();
            }
        }

        return VisitorAction.CONTINUE;
    }

    public int visitFieldSeparator(FieldSeparator fieldSeparator)
    {
        if (isInsideRegion())
        {
            fieldSeparator.remove();
        }

        return VisitorAction.CONTINUE;
    }

    public int visitParagraphEnd(Paragraph paragraph)
    {
        if (isInsideRegion())
        {
            paragraph.remove();
        }

        return VisitorAction.CONTINUE;
    }

    public int visitRun(Run run)
    {
        if (isInsideRegion())
        {
            run.remove();
        }
        else
        {
            if (run.getText().contains(ControlChar.LINE_BREAK))
            {
                try
                {
                    run.setText(run.getText().replace(ControlChar.LINE_BREAK_CHAR,
                            ' '));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        return VisitorAction.CONTINUE;
    }

    public int visitTableStart(Table table)
    {
        if (isInsideRegion())
        {
            table.remove();
        }

        return VisitorAction.CONTINUE;
    }

    private String getFieldCode(FieldStart fieldStart)
    {
        StringBuilder builder = new StringBuilder();

        for (Node node = fieldStart; node != null
                && node.getNodeType() != NodeType.FIELD_SEPARATOR
                && node.getNodeType() != NodeType.FIELD_END; node = node
                        .nextPreOrder(node.getDocument()))
        {
            if (node.getNodeType() == NodeType.RUN)
            {
                builder.append(node.getText());
            }
        }

        return builder.toString();
    }

    private boolean isInsideTable(Node node)
    {
        return (node.getAncestor(NodeType.TABLE) != null) ? true : false;
    }

    private boolean isInsideRegion()
    {
        return mRegionDepth > 0;
    }

    private boolean isAtOuterRegion()
    {
        return mRegionDepth == 0 ? true : false;
    }
}