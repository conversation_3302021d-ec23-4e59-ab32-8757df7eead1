package com.orbis.twitter;

import java.util.Date;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.web.content.ContentItem;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import twitter4j.auth.AccessToken;

@Access(value = AccessType.FIELD)
public class TwitterAccount extends ContentItem
{
    private long userId;

    private String name, token, tokenSecret, screenName, profileImage;

    private UserDetailsImpl createdBy;

    private Date dateCreated = new Date();

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public String getTokenSecret()
    {
        return tokenSecret;
    }

    public void setTokenSecret(String tokenSecret)
    {
        this.tokenSecret = tokenSecret;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public UserDetailsImpl getCreatedBy()
    {
        return createdBy;
    }

    public void setCreatedBy(UserDetailsImpl createdBy)
    {
        this.createdBy = createdBy;
    }

    public Date getDateCreated()
    {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated)
    {
        this.dateCreated = dateCreated;
    }

    public String getScreenName()
    {
        return screenName;
    }

    public void setScreenName(String screenName)
    {
        this.screenName = screenName;
    }

    public long getUserId()
    {
        return userId;
    }

    public void setUserId(long userId)
    {
        this.userId = userId;
    }

    public String getProfileImage()
    {
        return profileImage;
    }

    public void setProfileImage(String profileImage)
    {
        this.profileImage = profileImage;
    }

    public AccessToken getAccessToken()
    {
        return new AccessToken(token, tokenSecret);
    }
}
