package com.orbis.twitter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;

import twitter4j.Paging;
import twitter4j.Status;
import twitter4j.Twitter;
import twitter4j.TwitterFactory;
import twitter4j.conf.ConfigurationBuilder;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.StringUtils;

public class TwitterHelper
{
    private static final String CONSUMER_KEY = "*************************";

    private static final String CONSUMER_SECRET = "xAN1PHvJgnGM6xjLHdksCK0l6BpsgBZCqQwS7iisqXTkN7IyJb";

    public static ConfigurationBuilder getDefaultConfigurations()
    {
        ConfigurationBuilder cb = new ConfigurationBuilder();
        cb.setDebugEnabled(true).setOAuthConsumerKey(CONSUMER_KEY)
                .setOAuthConsumerSecret(CONSUMER_SECRET);
        return cb;
    }

    public static TwitterAccount getTwitterAccount(HttpServletRequest request)
    {
        TwitterAccount ta = null;

        try
        {
            String accountId = StringUtils
                    .isInteger(request.getParameter("accountId"))
                            ? request.getParameter("accountId")
                            : request.getAttribute("accountId").toString();
            ta = (TwitterAccount) PortalUtils.getHt().load(TwitterAccount.class,
                    Integer.valueOf(accountId));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ta;
    }

    public static Status updateStatus(TwitterAccount account, String status)
    {
        Status s = null;

        try
        {
            Twitter t = new TwitterFactory(getDefaultConfigurations().build())
                    .getInstance();
            t.setOAuthAccessToken(account.getAccessToken());
            s = t.updateStatus(status);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return s;
    }

    public static List<Status> getUserStatuses(TwitterAccount account)
    {
        List statuses = new ArrayList();

        try
        {
            Twitter t = new TwitterFactory(getDefaultConfigurations().build())
                    .getInstance();
            t.setOAuthAccessToken(account.getAccessToken());
            Paging p = new Paging();
            p.setCount(5);
            statuses = t.getUserTimeline(p);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return statuses;
    }

    public static List<Status> getCombinedUserStatuses(
            List<TwitterAccount> accounts)
    {
        List<Status> statuses = new ArrayList();
        for (TwitterAccount a : accounts)
        {
            statuses.addAll(TwitterHelper.getUserStatuses(a));
        }

        statuses = statuses.subList(0, Math.min(5, statuses.size()));
        Collections.sort(statuses);

        return statuses;
    }
}