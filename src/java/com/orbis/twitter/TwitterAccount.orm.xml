<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.twitter.TwitterAccount" name="TwitterAccount">
        <table name="twitter_account"/>
        <attributes>


            <many-to-one name="createdBy"/>

            <basic name="name"/>
            <basic name="token"/>
            <basic name="tokenSecret"/>
            <basic name="screenName"/>
            <basic name="userId">
                <column name="userId" column-definition="numeric(19)"/>
            </basic>
            <basic name="dateCreated"/>
            <basic name="profileImage">
                <column column-definition="TEXT"/>
            </basic>
        </attributes>
    </entity>
</entity-mappings>