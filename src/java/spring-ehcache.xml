<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.ehcache.org/v3"
        xmlns:jsr107="http://www.ehcache.org/v3/jsr107"
        xsi:schemaLocation="
            http://www.ehcache.org/v3 https://www.ehcache.org/schema/ehcache-core-3.0.xsd
            http://www.ehcache.org/v3/jsr107 https://www.ehcache.org/schema/ehcache-107-ext-3.0.xsd">

    <service>
        <jsr107:defaults enable-management="true" enable-statistics="false" default-template="simple"/>
    </service>

    <!--	<persistence directory="${java.io.tmpdir}"/>-->

    <cache-template name="simple">
        <expiry>
            <ttl>60</ttl>
        </expiry>
        <heap>1000</heap>
    </cache-template>


    <cache alias="acl-cache">
        <expiry>
            <ttl>300</ttl>
        </expiry>
        <heap>10000</heap>
    </cache>

    <cache alias="transformQueries">
        <expiry>
            <ttl>300</ttl>
        </expiry>
        <heap>1000</heap>
    </cache>

    <cache alias="static-template">
        <expiry>
            <ttl>300</ttl>
        </expiry>
        <heap>20</heap>
    </cache>

    <cache alias="fileAttributes">
        <expiry>
            <tti>9000</tti>
        </expiry>
        <heap>20000</heap>
    </cache>

    <cache alias="folderList">
        <expiry>
            <tti>9000</tti>
        </expiry>
        <heap>5000</heap>
    </cache>

</config>
