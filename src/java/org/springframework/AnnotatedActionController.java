package org.springframework;

import com.orbis.utils.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.exception.HandlerNotFoundException;
import org.springframework.web.AbstractActionController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.configuration.web.HandlerBridge;

public class AnnotatedActionController
        extends AbstractActionController<HandlerMethod>
{

    protected HandlerMethod getHandlerMethod(HttpServletRequest request)
            throws Exception
    {
        String action = getMethodNameResolver() != null
                ? getMethodNameResolver().getAction(request)
                : request.getParameter("action");
        return getHandlerMethod(action, request);
    }

    protected HandlerMethod getHandlerMethod(String methodName,
            HttpServletRequest request) throws Exception
    {
        RequestMapping annotation = this.getClass()
                .getAnnotation(RequestMapping.class);

        String baseUrl = annotation.value()[0];
        String url = StringUtils.isEmpty(methodName) ? "/" + baseUrl
                : "/" + baseUrl + "/" + methodName;
        return HandlerBridge.lookupHandler(url, request);
    }

    protected ModelAndView handleRequestInternal(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        HandlerMethod handlerMethod = getHandlerMethod(request);
        if (handlerMethod == null)
        {
            throw new HandlerNotFoundException(
                    "No handler method found for request");
        }
        return HandlerBridge.handle(request, response, handlerMethod);
    }

    protected ModelAndView invokeNamedMethod(String methodName,
            HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        HandlerMethod handlerMethod = getHandlerMethod(methodName, request);
        if (handlerMethod == null)
        {
            throw new HandlerNotFoundException(
                    "No handler method found for request");
        }
        return HandlerBridge.handle(request, response, handlerMethod);
    }

}
