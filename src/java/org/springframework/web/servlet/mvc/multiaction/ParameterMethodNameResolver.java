package org.springframework.web.servlet.mvc.multiaction;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ParameterMethodNameResolver implements ActionResolver {

    private String paramName;

    private String defaultMethodName;

    public String getAction(HttpServletRequest request) {
        String parameter = request.getParameter(paramName);
        return StringUtils.hasLength(parameter) ? parameter : defaultMethodName;
    }

}
