package org.springframework.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractController;
import org.springframework.web.servlet.mvc.multiaction.ActionResolver;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public abstract class AbstractActionController<T> extends AbstractController
{

    private ActionResolver methodNameResolver;

    protected abstract T getHandlerMethod(String methodName,
            HttpServletRequest request) throws Exception;

    protected abstract T getHandlerMethod(HttpServletRequest request)
            throws Exception;

    protected abstract ModelAndView invokeNamedMethod(String methodName,
            HttpServletRequest request, HttpServletResponse response)
            throws Exception;

}
