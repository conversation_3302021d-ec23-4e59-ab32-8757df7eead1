package org.springframework;

import java.lang.reflect.Method;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.AbstractActionController;
import org.springframework.web.servlet.ModelAndView;

public class ReflectionActionController extends AbstractActionController<Method>
{
    @Override
    protected Method getHandlerMethod(String methodName, HttpServletRequest request)
            throws Exception
    {

        return this.getClass().getMethod(methodName, HttpServletRequest.class,
                HttpServletResponse.class);
    }

    @Override
    protected Method getHandlerMethod(HttpServletRequest request) throws Exception
    {
        String action = getMethodNameResolver().getAction(request);
        return getHandlerMethod(action, request);
    }

    @Override
    protected ModelAndView invokeNamedMethod(String methodName,
            HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        return (ModelAndView) this.getClass().getMethod(methodName,
                HttpServletRequest.class, HttpServletResponse.class)
                .invoke(this, request, response);
    }

    @Override
    protected ModelAndView handleRequestInternal(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        Method handlerMethod = getHandlerMethod(request);
        return (ModelAndView) handlerMethod.invoke(this, request, response);
    }
}
