package org.hibernate;

import com.orbis.web.content.ContentItem;
import lombok.extern.apachecommons.CommonsLog;
import org.hibernate.type.descriptor.java.IntegerJavaType;
import org.hibernate.type.descriptor.java.JavaType;
import org.hibernate.type.descriptor.java.SerializableJavaType;

/**
 * This class is used as a fix for
 * <a href="https://hibernate.atlassian.net/browse/HHH-16605"><PERSON><PERSON><PERSON> vs Integer
 * comparison crash</a>
 *
 */
@CommonsLog
public class ClassicIntegerJavaType extends IntegerJavaType
{
    public static final ClassicIntegerJavaType INSTANCE = new ClassicIntegerJavaType();

    @Override
    public boolean isWider(JavaType<?> javaType) {
        if (super.isWider(javaType))
        {
            return true;
        }
        if (javaType.getJavaType() != this.getJavaType() && log.isWarnEnabled())
        {
            StackTraceElement[] stacktrace = Thread.currentThread().getStackTrace();
            StringBuilder simpleTrace = new StringBuilder();
            for (int i = 2, stacktraceLength = stacktrace.length; i < stacktraceLength; i++)
            {
                StackTraceElement element = stacktrace[i];
                if (element.getClassName().startsWith("com.orbis"))
                {
                    if (!"find".equals(element.getMethodName()))
                    {
                        simpleTrace.append("\n\tat ").append(element.getClassName())
                                .append(".").append(element.getMethodName())
                                .append("(")
                                .append(element.getFileName()).append(":")
                                .append(element.getLineNumber()).append(")");
                    }
                }
                else if (!simpleTrace.isEmpty() && stacktrace[i-1].getClassName()
                        .endsWith("Controller"))
                {
                    break;
                }
            }
            log.warn(String.format(
                    "Not compatible types are used in the query where statement: %s != %s %s",
                    javaType.getJavaType(), Integer.class, simpleTrace));
        }
        return isCompatibleType(javaType);
    }

    private boolean isCompatibleType(JavaType<?> javaType) {
        String typeName = javaType.getJavaType().getTypeName();
        return (typeName.equals("boolean") || typeName.equals("java.lang.Boolean") ||
                typeName.equals("java.lang.String") || javaType.getClass() == SerializableJavaType.class ||
                ContentItem.class.isAssignableFrom(javaType.getJavaTypeClass()));
    }
}
