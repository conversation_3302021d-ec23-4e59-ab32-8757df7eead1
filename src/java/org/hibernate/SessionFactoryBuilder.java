package org.hibernate;

import java.util.List;

import org.hibernate.boot.spi.MetadataImplementor;
import org.hibernate.boot.spi.SessionFactoryBuilderFactory;
import org.hibernate.boot.spi.SessionFactoryBuilderImplementor;
import org.hibernate.mapping.PersistentClass;
import org.hibernate.type.descriptor.jdbc.IntegerJdbcType;
import org.hibernate.type.descriptor.jdbc.VarcharJdbcType;
import org.hibernate.type.internal.BasicTypeImpl;

import com.orbis.utils.ClassCache;

import lombok.Getter;
import lombok.Setter;

public class SessionFactoryBuilder implements SessionFactoryBuilderFactory
{

    private static final MetadataHolder holder = new MetadataHolder();

    @Override
    public org.hibernate.boot.SessionFactoryBuilder getSessionFactoryBuilder(
            MetadataImplementor metadata,
            SessionFactoryBuilderImplementor defaultBuilder)
    {
        metadata.getTypeConfiguration().getBasicTypeRegistry()
                .register(
                        new BasicTypeImpl<>(ClassicIntegerJavaType.INSTANCE,
                                IntegerJdbcType.INSTANCE),
                        "java.lang.Integer", "integer");
        metadata.getTypeConfiguration().getBasicTypeRegistry()
                .register(
                        new BasicTypeImpl<>(ClassicStringJavaType.INSTANCE,
                                VarcharJdbcType.INSTANCE),
                        "java.lang.String", "string");

        if (holder.getMeta() == null)
        {
            holder.setMeta(metadata);
            List<? extends Class<?>> list = metadata.getEntityBindings().stream()
                    .map(PersistentClass::getMappedClass).toList();
            ClassCache.putAll(list);
        }

        return defaultBuilder;
    }

    public static MetadataImplementor getMeta()
    {
        return holder.getMeta();
    }

    @Getter
    @Setter
    private static class MetadataHolder
    {
        private MetadataImplementor meta;
    }
}
