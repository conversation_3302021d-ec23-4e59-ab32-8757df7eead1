package org.hibernate;

import java.sql.Types;

import org.hibernate.boot.model.FunctionContributions;
import org.hibernate.dialect.SQLServerDialect;
import org.hibernate.dialect.function.StandardSQLFunction;
import org.hibernate.type.StandardBasicTypes;

public class OrbisClassicSQLServerDialect extends SQLServerDialect
{

    protected String columnType(int sqlTypeCode)
    {
        if (sqlTypeCode == Types.BOOLEAN)
        {
            return "tinyint";
        }
        return super.columnType(sqlTypeCode);
    }

    @Override
    public void initializeFunctionRegistry(FunctionContributions queryEngine)
    {
        super.initializeFunctionRegistry(queryEngine);
        queryEngine.getFunctionRegistry().register("count",
                new StandardSQLFunction("count", StandardBasicTypes.INTEGER));
        queryEngine.getFunctionRegistry().register("avg",
                new StandardSQLFunction("avg", StandardBasicTypes.DOUBLE));
        queryEngine.getFunctionRegistry().register("sum",
                new StandardSQLFunction("sum", StandardBasicTypes.DOUBLE));
        queryEngine.getFunctionRegistry().register("dateadd",
                new StandardSQLFunction("dateadd", StandardBasicTypes.TIMESTAMP));
        queryEngine.getFunctionRegistry().registerPattern("date_convert",
                "convert(date,?1)", queryEngine.getTypeConfiguration()
                        .getBasicTypeRegistry().resolve(StandardBasicTypes.DATE));
        queryEngine.getFunctionRegistry().registerPattern("datetime_convert",
                "convert(datetime,?1)", queryEngine.getTypeConfiguration()
                        .getBasicTypeRegistry().resolve(StandardBasicTypes.DATE));
    }
}
