package org.hibernate;

import lombok.extern.apachecommons.CommonsLog;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.event.spi.PreInsertEvent;
import org.hibernate.event.spi.PreInsertEventListener;
import org.hibernate.event.spi.PreUpdateEvent;
import org.hibernate.event.spi.PreUpdateEventListener;
import org.hibernate.mapping.PersistentClass;
import org.hibernate.mapping.Property;
import org.hibernate.persister.entity.EntityPersister;
import org.hibernate.proxy.HibernateProxy;

import com.orbis.portal.HibernateUtils;
import com.orbis.web.content.ContentItem;

/**
 * Intercepts pre-insert and pre-update events to ensure that Hibernate proxy
 * classes are replaced with the original classes before being persisted (ex.
 * {@link com.orbis.web.content.ecommerce.EcommerceOrder#orderEntityClass}).
 * Also logs these attempts. This helps to avoid issues where entities
 * containing proxy classes cannot be loaded after the proxy class is erased.
 * 
 */
@CommonsLog
public class ProxyClassHandler
        implements PreInsertEventListener, PreUpdateEventListener
{
    private final Map<String, List<String>> entityClassFieldNames = new HashMap<>();

    public ProxyClassHandler(Collection<PersistentClass> entityBindings)
    {
        populateEntityClassFieldNames(entityBindings);
    }

    /**
     * Memorizes entity names containing class type fields and the fields names
     */
    private void populateEntityClassFieldNames(
            Collection<PersistentClass> entityBindings)
    {
        for (PersistentClass persistentClass : entityBindings)
        {
            List<String> classFieldNames = new ArrayList<>();
            for (Property property : persistentClass.getProperties())
            {
                if (Class.class.equals(property.getType().getReturnedClass()))
                {
                    classFieldNames.add(property.getName());
                }
            }
            if (!classFieldNames.isEmpty())
            {
                entityClassFieldNames.put(persistentClass.getEntityName(),
                        classFieldNames);
            }
        }
    }

    @Override
    public boolean onPreInsert(PreInsertEvent event)
    {
        return handleEvent(event.getEntity(), event.getState(),
                event.getPersister());
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event)
    {
        return handleEvent(event.getEntity(), event.getState(),
                event.getPersister());
    }

    private boolean handleEvent(Object entity, Object[] state,
            EntityPersister persister)
    {
        String entityName = persister.getEntityName();
        List<String> classFieldNames = entityClassFieldNames.get(entityName);
        if (classFieldNames != null)
        {
            String[] propertyNames = persister.getPropertyNames();
            Map<String, Integer> propertyNameToIndex = new HashMap<>();
            for (int i = 0; i < propertyNames.length; i++)
            {
                propertyNameToIndex.put(propertyNames[i], i);
            }
            for (String fieldName : classFieldNames)
            {
                Integer index = propertyNameToIndex.get(fieldName);
                if (index != null && state[index] instanceof Class<?> clazz
                        && HibernateProxy.class.isAssignableFrom(clazz))
                {
                    Class<?> originalClass = HibernateUtils.resolveRealClass(clazz);
                    state[index] = originalClass;
                    if (log.isWarnEnabled())
                    {
                        StackTraceElement[] stacktrace = Thread.currentThread()
                                .getStackTrace();
                        StringBuilder message = new StringBuilder(
                                "Detected an attempt to persist proxy class '")
                                .append(clazz.getName()).append("' in field '")
                                .append(fieldName).append("' of entity: '")
                                .append(entity.getClass().getName()).append("'");
                        if (entity instanceof ContentItem contentItem)
                        {
                            message.append(" with id ")
                                    .append(contentItem.getId() != null
                                            ? contentItem.getId()
                                            : "(new entity)");
                        }
                        message.append(System.lineSeparator())
                                .append("Replace with original class '")
                                .append(originalClass.getName()).append("'");
                        for (int i = 2,
                                stacktraceLength = stacktrace.length; i < stacktraceLength; i++)
                        {
                            StackTraceElement element = stacktrace[i];
                            if (element.getClassName().startsWith("com.orbis"))
                            {
                                message.append(System.lineSeparator()).append("at ")
                                        .append(element.getClassName()).append(".")
                                        .append(element.getMethodName()).append("(")
                                        .append(element.getFileName()).append(":")
                                        .append(element.getLineNumber())
                                        .append(")");
                            }
                            else if (!message.isEmpty() && stacktrace[i - 1]
                                    .getClassName().endsWith("Controller"))
                            {
                                break;
                            }
                        }
                        log.warn(message);
                    }
                }
            }
        }
        return false;
    }
}
