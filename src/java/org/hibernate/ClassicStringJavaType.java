package org.hibernate;

import lombok.extern.apachecommons.CommonsLog;
import org.hibernate.type.descriptor.java.JavaType;
import org.hibernate.type.descriptor.java.StringJavaType;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.Set;

/**
 * This class is used as a fix for
 * <a href="https://hibernate.atlassian.net/browse/HHH-16608">Date vs String
 * comparison</a>
 */
@CommonsLog
public class ClassicStringJavaType extends StringJavaType
{
    public static final ClassicStringJavaType INSTANCE = new ClassicStringJavaType();

    private static final Set<String> WIDER_TYPES = Set.of(
            "java.util.Date",
            "java.lang.Integer",
            "java.lang.Boolean"
    );

    @Override
    public boolean isWider(JavaType<?> javaType)
    {
        if (super.isWider(javaType))
        {
            return true;
        }
        if (javaType.getJavaType() != this.getJavaType() && log.isWarnEnabled())
        {
            StackTraceElement[] stacktrace = Thread.currentThread().getStackTrace();
            StringBuilder simpleTrace = new StringBuilder();
            for (int i = 2, stacktraceLength = stacktrace.length; i < stacktraceLength; i++)
            {
                StackTraceElement element = stacktrace[i];
                if (element.getClassName().startsWith("com.orbis"))
                {
                    if (!"find".equals(element.getMethodName()))
                    {
                        simpleTrace.append("\n\tat ").append(element.getClassName())
                                .append(".").append(element.getMethodName())
                                .append("(")
                                .append(element.getFileName()).append(":")
                                .append(element.getLineNumber()).append(")");
                    }
                }
                else if (!simpleTrace.isEmpty() && stacktrace[i-1].getClassName()
                        .endsWith("Controller"))
                {
                    break;
                }
            }
            log.warn(String.format(
                    "Not compatible types are used in the query where statement: %s != %s %s",
                    javaType.getJavaType(), String.class, simpleTrace));
        }
        return WIDER_TYPES.contains(javaType.getJavaType().getTypeName());
    }
}
