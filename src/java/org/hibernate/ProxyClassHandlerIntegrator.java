package org.hibernate;

import java.util.Collection;

import org.hibernate.boot.Metadata;
import org.hibernate.boot.spi.BootstrapContext;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.integrator.spi.Integrator;
import org.hibernate.mapping.PersistentClass;
import org.hibernate.service.spi.ServiceRegistryImplementor;
import org.hibernate.service.spi.SessionFactoryServiceRegistry;

/**
 * Registers {@link ProxyClassHandler} globally in Hibernate as a pre-insert and
 * pre-update listener via Java SPI
 * (META-INF/services/org.hibernate.integrator.spi.Integrator)
 */
public class ProxyClassHandlerIntegrator implements Integrator
{
    @Override
    public void integrate(Metadata metadata, BootstrapContext bootstrapContext,
            SessionFactoryImplementor sessionFactory)
    {
        ServiceRegistryImplementor serviceRegistry = sessionFactory
                .getServiceRegistry();
        EventListenerRegistry listenerRegistry = serviceRegistry
                .getService(EventListenerRegistry.class);
        Collection<PersistentClass> entityBindings = metadata.getEntityBindings();
        ProxyClassHandler listener = new ProxyClassHandler(entityBindings);
        listenerRegistry.appendListeners(EventType.PRE_INSERT, listener);
        listenerRegistry.appendListeners(EventType.PRE_UPDATE, listener);
    }

    @Override
    public void disintegrate(SessionFactoryImplementor sessionFactory,
            SessionFactoryServiceRegistry serviceRegistry)
    {
    }
}
