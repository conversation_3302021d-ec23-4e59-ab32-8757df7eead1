/**
 *    Copyright 2009-2015 the original author or authors.
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
package org.apache.ibatis.jdbc;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
public abstract class AbstractSQL<T>
{

    private static final String AND = "AND ";

    private static final String OR = "OR ";

    private static final String START_BLOCK = "(";

    private static final String END_BLOCK = ")";

    private SQLStatement sql = new SQLStatement();

    public abstract T getSelf();

    public T whereFragment()
    {
        sql().statementType = SQLStatement.StatementType.WHEREFRAGMENT;
        sql().lastList = sql().where;
        return getSelf();
    }

    public T update(String table)
    {
        sql().statementType = SQLStatement.StatementType.UPDATE;
        sql().mainTable.add(table);
        return getSelf();
    }

    public T set(final String sets, final Object... args)
    {
        final String s;
        if (args != null && args.length > 0)
        {
            s = String.format(sets, args);
        }
        else
        {
            s = sets;
        }
        sql().sets.add(s);
        return getSelf();
    }

    public T insertInto(String tableName)
    {
        sql().statementType = SQLStatement.StatementType.INSERT;
        sql().mainTable.add(tableName);
        return getSelf();
    }

    public T values(String columns, String values)
    {
        sql().columns.add(columns);
        sql().values.add(values);
        return getSelf();
    }

    public T select(String columns)
    {
        sql().statementType = SQLStatement.StatementType.SELECT;
        sql().select.add(columns);
        return getSelf();
    }

    public T selectDistinct(String columns)
    {
        sql().distinct = true;
        select(columns);
        return getSelf();
    }

    public T deleteFrom(String table)
    {
        sql().statementType = SQLStatement.StatementType.DELETE;
        sql().mainTable.add(table);
        return getSelf();
    }

    public T from(String table)
    {
        sql().fromTables.add(table);
        return getSelf();
    }

    public T join(String join)
    {
        sql().join.add(join);
        return getSelf();
    }

    public T innerJoin(String join)
    {
        sql().innerJoin.add(join);
        return getSelf();
    }

    public T leftOuterJoin(String join)
    {
        sql().leftOuterJoin.add(join);
        return getSelf();
    }

    public T rightOuterJoin(String join)
    {
        sql().rightOuterJoin.add(join);
        return getSelf();
    }

    public T outerJoin(String join)
    {
        sql().outerJoin.add(join);
        return getSelf();
    }

    public T where(final String conditions, final Object... args)
    {
        final String s;
        if (args != null && args.length > 0)
        {
            s = String.format(conditions, args);
        }
        else
        {
            s = conditions;
        }
        sql().where.add(s);
        sql().lastList = sql().where;
        return getSelf();
    }

    public T or()
    {
        sql().lastList.add(OR);
        return getSelf();
    }

    public T and()
    {
        sql().lastList.add(AND);
        return getSelf();
    }

    public T startBlock()
    {
        sql().lastList.add(START_BLOCK);
        return getSelf();
    }

    public T endBlock()
    {
        sql().lastList.add(END_BLOCK);
        return getSelf();
    }

    public T groupBy(String columns)
    {
        sql().groupBy.add(columns);
        return getSelf();
    }

    public T having(String conditions)
    {
        sql().having.add(conditions);
        sql().lastList = sql().having;
        return getSelf();
    }

    public T orderBy(String columns)
    {
        sql().orderBy.add(columns);
        return getSelf();
    }

    private SQLStatement sql()
    {
        return sql;
    }

    public <A extends Appendable> A usingAppender(A a)
    {
        sql().sql(a);
        return a;
    }

    @Override
    public String toString()
    {
        StringBuilder sb = new StringBuilder();
        sql().sql(sb);
        return sb.toString();
    }

    private static class SafeAppendable
    {
        private final Appendable a;

        private boolean empty = true;

        public SafeAppendable(Appendable a)
        {
            super();
            this.a = a;
        }

        public SafeAppendable append(CharSequence s)
        {
            try
            {
                if (empty && s.length() > 0)
                {
                    empty = false;
                }
                a.append(s);
            }
            catch (IOException e)
            {
                throw new RuntimeException(e);
            }
            return this;
        }

        public boolean isEmpty()
        {
            return empty;
        }

    }

    private static class SQLStatement
    {

        public enum StatementType
        {
            DELETE,
            INSERT,
            SELECT,
            UPDATE,
            WHEREFRAGMENT
        }

        StatementType statementType;

        List<String> sets = new ArrayList<String>();

        List<String> select = new ArrayList<String>();

        List<String> mainTable = new ArrayList<String>();

        List<String> fromTables = new ArrayList<String>();

        List<String> join = new ArrayList<String>();

        List<String> innerJoin = new ArrayList<String>();

        List<String> outerJoin = new ArrayList<String>();

        List<String> leftOuterJoin = new ArrayList<String>();

        List<String> rightOuterJoin = new ArrayList<String>();

        List<String> where = new ArrayList<String>();

        List<String> having = new ArrayList<String>();

        List<String> groupBy = new ArrayList<String>();

        List<String> orderBy = new ArrayList<String>();

        List<String> lastList = new ArrayList<String>();

        List<String> columns = new ArrayList<String>();

        List<String> values = new ArrayList<String>();

        boolean distinct;

        public SQLStatement()
        {
            // Prevent Synthetic Access
        }

        private void sqlClause(SafeAppendable builder, String keyword,
                List<String> parts, String open, String close, String conjunction)
        {
            if (!parts.isEmpty())
            {
                if (!builder.isEmpty())
                {
                    builder.append("\n");
                }
                builder.append(keyword);
                builder.append(" ");
                builder.append(open);
                String last = "________";
                for (int i = 0, n = parts.size(); i < n; i++)
                {
                    String part = parts.get(i);
                    if (i > 0 && !part.equals(AND) && !part.equals(OR)
                            && !last.equals(AND) && !last.equals(OR))
                    {
                        builder.append(conjunction);
                    }
                    builder.append(part);
                    last = part;
                }
                builder.append(close);
            }
        }

        private final List<String> noParensIfCurrentElements = Arrays.asList(AND,
                OR, START_BLOCK, END_BLOCK);

        private final List<String> noConjunctionIfCurrentElements = Arrays.asList(
                AND, OR, END_BLOCK);

        private final List<String> noConjunctionIfPrevElements = Arrays.asList(AND,
                OR, START_BLOCK);

        private final List<String> conjunctions = Arrays.asList(AND, OR);

        private void whereClause(SafeAppendable builder, String keyword,
                List<String> parts)
        {

            if (!parts.isEmpty())
            {
                if (!builder.isEmpty())
                {
                    builder.append("\n");
                }
                if (!keyword.isEmpty())
                {
                    builder.append(keyword);
                    builder.append(" ");
                }

                String prev = "________";
                for (int i = 0, n = parts.size(); i < n; i++)
                {
                    final boolean isLastPart = !(i + 1 < n);

                    final String part = parts.get(i);

                    final boolean nextIsBlockEnd;
                    if (!isLastPart)
                    {
                        final String nextPart = parts.get(i + 1);
                        nextIsBlockEnd = END_BLOCK.equals(nextPart);
                    }
                    else
                    {
                        nextIsBlockEnd = false;
                    }

                    final boolean partIsConjunction = conjunctions.contains(part);
                    final boolean prevIsConjunction = conjunctions.contains(prev);

                    if (partIsConjunction && isLastPart
                            || (prevIsConjunction && partIsConjunction)
                            || (partIsConjunction && nextIsBlockEnd))
                        continue;

                    final boolean noConjunctionIfCurrent = noConjunctionIfCurrentElements
                            .contains(part);
                    final boolean noConjunctionIfPrev = noConjunctionIfPrevElements
                            .contains(prev);
                    final boolean noParensIfCurrent = noParensIfCurrentElements
                            .contains(part);
                    if (i > 0 && !noConjunctionIfCurrent && !noConjunctionIfPrev)
                    {
                        builder.append("AND ");
                    }

                    if (!noParensIfCurrent)
                        builder.append("(");

                    builder.append(part);

                    if (!noParensIfCurrent)
                        builder.append(")");
                    prev = part;

                    if (OR.equals(prev) || AND.equals(prev) || isLastPart)
                    {
                    }
                    else
                    {
                        builder.append("\n");
                    }
                }
            }
        }

        private String selectSQL(SafeAppendable builder)
        {
            if (distinct)
            {
                sqlClause(builder, "SELECT DISTINCT", select, "", "", ", ");
            }
            else
            {
                sqlClause(builder, "SELECT", select, "", "", ", ");
            }

            sqlClause(builder, "FROM", fromTables, "", "", ", ");
            sqlClause(builder, "JOIN", join, "", "", "\nJOIN ");
            sqlClause(builder, "INNER JOIN", innerJoin, "", "", "\nINNER JOIN ");
            sqlClause(builder, "OUTER JOIN", outerJoin, "", "", "\nOUTER JOIN ");
            sqlClause(builder, "LEFT OUTER JOIN", leftOuterJoin, "", "",
                    "\nLEFT OUTER JOIN ");
            sqlClause(builder, "RIGHT OUTER JOIN", rightOuterJoin, "", "",
                    "\nRIGHT OUTER JOIN ");
            whereClause(builder, "WHERE", where);
            sqlClause(builder, "GROUP BY", groupBy, "", "", ", ");
            sqlClause(builder, "HAVING", having, "(", ")", " AND ");
            sqlClause(builder, "ORDER BY", orderBy, "", "", ", ");
            return builder.toString();
        }

        private String insertSQL(SafeAppendable builder)
        {
            sqlClause(builder, "INSERT INTO", mainTable, "", "", "");
            sqlClause(builder, "", columns, "(", ")", ", ");
            sqlClause(builder, "VALUES", values, "(", ")", ", ");
            return builder.toString();
        }

        private String deleteSQL(SafeAppendable builder)
        {
            sqlClause(builder, "DELETE FROM", mainTable, "", "", "");
            sqlClause(builder, "FROM", fromTables, "", "", ", ");
            sqlClause(builder, "JOIN", join, "", "", "\nJOIN ");
            sqlClause(builder, "INNER JOIN", innerJoin, "", "", "\nINNER JOIN ");
            sqlClause(builder, "OUTER JOIN", outerJoin, "", "", "\nOUTER JOIN ");
            sqlClause(builder, "LEFT OUTER JOIN", leftOuterJoin, "", "",
                    "\nLEFT OUTER JOIN ");
            sqlClause(builder, "RIGHT OUTER JOIN", rightOuterJoin, "", "",
                    "\nRIGHT OUTER JOIN ");
            whereClause(builder, "WHERE", where);
            return builder.toString();
        }

        private String updateSQL(SafeAppendable builder)
        {

            sqlClause(builder, "UPDATE", mainTable, "", "", "");
            sqlClause(builder, "SET", sets, "", "", ", ");
            sqlClause(builder, "FROM", fromTables, "", "", ", ");
            sqlClause(builder, "JOIN", join, "", "", "\nJOIN ");
            sqlClause(builder, "INNER JOIN", innerJoin, "", "", "\nINNER JOIN ");
            sqlClause(builder, "OUTER JOIN", outerJoin, "", "", "\nOUTER JOIN ");
            sqlClause(builder, "LEFT OUTER JOIN", leftOuterJoin, "", "",
                    "\nLEFT OUTER JOIN ");
            sqlClause(builder, "RIGHT OUTER JOIN", rightOuterJoin, "", "",
                    "\nRIGHT OUTER JOIN ");
            whereClause(builder, "WHERE", where);
            return builder.toString();
        }

        private String whereFragmentSQL(SafeAppendable builder)
        {
            if (where.isEmpty())
            {
                builder.append("1 = 1");
            }
            else
            {
                whereClause(builder, "", where);
                // sqlClause(builder, "", where, "", "", " AND ");
            }

            return " ( " + builder.toString() + " ) ";
        }

        public String sql(Appendable a)
        {
            SafeAppendable builder = new SafeAppendable(a);

            final String answer;

            if (StatementType.DELETE.equals(statementType))
            {
                answer = deleteSQL(builder);
            }
            else if (StatementType.INSERT.equals(statementType))
            {
                answer = insertSQL(builder);
            }
            else if (StatementType.UPDATE.equals(statementType))
            {
                answer = updateSQL(builder);
            }
            else if (StatementType.SELECT.equals(statementType))
            {
                answer = selectSQL(builder);
            }
            else if (StatementType.WHEREFRAGMENT.equals(statementType))
            {
                answer = whereFragmentSQL(builder);
            }
            else
            {
                answer = null;
            }

            return answer;
        }
    }

}