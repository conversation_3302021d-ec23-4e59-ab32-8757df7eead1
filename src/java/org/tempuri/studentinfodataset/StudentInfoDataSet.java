package org.tempuri.studentinfodataset;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElements;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "tblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus" })
@XmlRootElement(name = "StudentInfoDataSet")
public class StudentInfoDataSet
{

    @XmlElements({
            @XmlElement(name = "tblStudentAddress", type = StudentInfoDataSet.TblStudentAddress.class),
            @XmlElement(name = "tblStudentProgramStatus", type = StudentInfoDataSet.TblStudentProgramStatus.class),
            @XmlElement(name = "tblStudentInfo", type = StudentInfoDataSet.TblStudentInfo.class),
            @XmlElement(name = "tblStudentCourseStatus", type = StudentInfoDataSet.TblStudentCourseStatus.class) })
    protected List<Object> tblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus;

    public List<Object> getTblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus()
    {
        if (tblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus == null)
        {
            tblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus = new ArrayList<Object>();
        }
        return this.tblStudentInfoOrTblStudentProgramStatusOrTblStudentCourseStatus;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "stdNo", "homeLocal", "areaCode", "phoneNo",
            "noAndStreet", "city", "provStateCode", "postalZipCode", "country",
            "source", "cellPhoneNo", "commentary", "cellAreaCode" })
    public static class TblStudentAddress
    {

        @XmlElement(name = "std_no")
        protected Integer stdNo;

        @XmlElement(name = "home_local")
        protected String homeLocal;

        @XmlElement(name = "area_code")
        protected String areaCode;

        @XmlElement(name = "phone_no")
        protected String phoneNo;

        @XmlElement(name = "no_and_street")
        protected String noAndStreet;

        protected String city;

        @XmlElement(name = "prov_state_code")
        protected String provStateCode;

        @XmlElement(name = "postal_zip_code")
        protected String postalZipCode;

        protected String country;

        protected String source;

        @XmlElement(name = "cell_phone_no")
        protected String cellPhoneNo;

        protected String commentary;

        @XmlElement(name = "cell_area_code")
        protected String cellAreaCode;

        /**
         * Gets the value of the stdNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getStdNo()
        {
            return stdNo;
        }

        /**
         * Sets the value of the stdNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setStdNo(Integer value)
        {
            this.stdNo = value;
        }

        /**
         * Gets the value of the homeLocal property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getHomeLocal()
        {
            return homeLocal;
        }

        /**
         * Sets the value of the homeLocal property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setHomeLocal(String value)
        {
            this.homeLocal = value;
        }

        /**
         * Gets the value of the areaCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getAreaCode()
        {
            return areaCode;
        }

        /**
         * Sets the value of the areaCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setAreaCode(String value)
        {
            this.areaCode = value;
        }

        /**
         * Gets the value of the phoneNo property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPhoneNo()
        {
            return phoneNo;
        }

        /**
         * Sets the value of the phoneNo property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPhoneNo(String value)
        {
            this.phoneNo = value;
        }

        /**
         * Gets the value of the noAndStreet property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getNoAndStreet()
        {
            return noAndStreet;
        }

        /**
         * Sets the value of the noAndStreet property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setNoAndStreet(String value)
        {
            this.noAndStreet = value;
        }

        /**
         * Gets the value of the city property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCity()
        {
            return city;
        }

        /**
         * Sets the value of the city property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCity(String value)
        {
            this.city = value;
        }

        /**
         * Gets the value of the provStateCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getProvStateCode()
        {
            return provStateCode;
        }

        /**
         * Sets the value of the provStateCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setProvStateCode(String value)
        {
            this.provStateCode = value;
        }

        /**
         * Gets the value of the postalZipCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPostalZipCode()
        {
            return postalZipCode;
        }

        /**
         * Sets the value of the postalZipCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPostalZipCode(String value)
        {
            this.postalZipCode = value;
        }

        /**
         * Gets the value of the country property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCountry()
        {
            return country;
        }

        /**
         * Sets the value of the country property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCountry(String value)
        {
            this.country = value;
        }

        /**
         * Gets the value of the source property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getSource()
        {
            return source;
        }

        /**
         * Sets the value of the source property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setSource(String value)
        {
            this.source = value;
        }

        /**
         * Gets the value of the cellPhoneNo property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCellPhoneNo()
        {
            return cellPhoneNo;
        }

        /**
         * Sets the value of the cellPhoneNo property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCellPhoneNo(String value)
        {
            this.cellPhoneNo = value;
        }

        /**
         * Gets the value of the commentary property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCommentary()
        {
            return commentary;
        }

        /**
         * Sets the value of the commentary property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCommentary(String value)
        {
            this.commentary = value;
        }

        /**
         * Gets the value of the cellAreaCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCellAreaCode()
        {
            return cellAreaCode;
        }

        /**
         * Sets the value of the cellAreaCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCellAreaCode(String value)
        {
            this.cellAreaCode = value;
        }

    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "stdNo", "term", "status",
            "statusDescription", "regDateTime", "crsNumber", "sectionNumber" })
    public static class TblStudentCourseStatus
    {

        @XmlElement(name = "std_no")
        protected Integer stdNo;

        protected String term;

        protected Short status;

        @XmlElement(name = "status_description")
        protected String statusDescription;

        @XmlElement(name = "reg_date_time")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar regDateTime;

        @XmlElement(name = "crs_number")
        protected String crsNumber;

        @XmlElement(name = "section_number")
        protected Short sectionNumber;

        /**
         * Gets the value of the stdNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getStdNo()
        {
            return stdNo;
        }

        /**
         * Sets the value of the stdNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setStdNo(Integer value)
        {
            this.stdNo = value;
        }

        /**
         * Gets the value of the term property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getTerm()
        {
            return term;
        }

        /**
         * Sets the value of the term property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setTerm(String value)
        {
            this.term = value;
        }

        /**
         * Gets the value of the status property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getStatus()
        {
            return status;
        }

        /**
         * Sets the value of the status property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setStatus(Short value)
        {
            this.status = value;
        }

        /**
         * Gets the value of the statusDescription property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getStatusDescription()
        {
            return statusDescription;
        }

        /**
         * Sets the value of the statusDescription property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setStatusDescription(String value)
        {
            this.statusDescription = value;
        }

        /**
         * Gets the value of the regDateTime property.
         * 
         * @return possible object is {@link XMLGregorianCalendar }
         * 
         */
        public XMLGregorianCalendar getRegDateTime()
        {
            return regDateTime;
        }

        /**
         * Sets the value of the regDateTime property.
         * 
         * @param value
         *            allowed object is {@link XMLGregorianCalendar }
         * 
         */
        public void setRegDateTime(XMLGregorianCalendar value)
        {
            this.regDateTime = value;
        }

        /**
         * Gets the value of the crsNumber property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCrsNumber()
        {
            return crsNumber;
        }

        /**
         * Sets the value of the crsNumber property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCrsNumber(String value)
        {
            this.crsNumber = value;
        }

        /**
         * Gets the value of the sectionNumber property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getSectionNumber()
        {
            return sectionNumber;
        }

        /**
         * Sets the value of the sectionNumber property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setSectionNumber(Short value)
        {
            this.sectionNumber = value;
        }

    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "stdNo", "firstName", "secondName",
            "surname", "status", "term", "prgCode", "campusCode", "delMode",
            "alSeqNumber", "alInd", "alLevel", "ftPtInd", "schedContInd",
            "vsnStartDate", "gradInd", "prgTitle", "nonCollegeEmail", "areaCode",
            "phoneNo", "cellAreaCode", "cellPhoneNo", "noAndStreet", "city",
            "provStateCode", "postalZipCode", "country", "statusDesc",
            "collegeEmail", "dateOfBirth", "gender", "sin", "ontEducNo",
            "statInCanada", "lvlStartDate", "progCumGpa", "poNoOfCals" })
    public static class TblStudentInfo
    {

        @XmlElement(name = "std_no")
        protected Integer stdNo;

        @XmlElement(name = "first_name")
        protected String firstName;

        @XmlElement(name = "second_name")
        protected String secondName;

        protected String surname;

        protected Short status;

        protected String term;

        @XmlElement(name = "prg_code")
        protected String prgCode;

        @XmlElement(name = "campus_code")
        protected String campusCode;

        @XmlElement(name = "del_mode")
        protected Short delMode;

        @XmlElement(name = "al_seq_number")
        protected Short alSeqNumber;

        @XmlElement(name = "al_ind")
        protected String alInd;

        @XmlElement(name = "al_level")
        protected Short alLevel;

        @XmlElement(name = "ft_pt_ind")
        protected String ftPtInd;

        @XmlElement(name = "sched_cont_ind")
        protected String schedContInd;

        @XmlElement(name = "vsn_start_date")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar vsnStartDate;

        @XmlElement(name = "grad_ind")
        protected String gradInd;

        @XmlElement(name = "prg_title")
        protected String prgTitle;

        @XmlElement(name = "non_college_email")
        protected String nonCollegeEmail;

        @XmlElement(name = "area_code")
        protected Short areaCode;

        @XmlElement(name = "phone_no")
        protected Integer phoneNo;

        @XmlElement(name = "cell_area_code")
        protected Short cellAreaCode;

        @XmlElement(name = "cell_phone_no")
        protected Integer cellPhoneNo;

        @XmlElement(name = "no_and_street")
        protected String noAndStreet;

        protected String city;

        @XmlElement(name = "prov_state_code")
        protected String provStateCode;

        @XmlElement(name = "postal_zip_code")
        protected String postalZipCode;

        protected String country;

        @XmlElement(name = "status_desc")
        protected String statusDesc;

        @XmlElement(name = "college_email")
        protected String collegeEmail;

        @XmlElement(name = "date_of_birth")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar dateOfBirth;

        protected String gender;

        @XmlElement(name = "sin")
        protected Integer sin;

        @XmlElement(name = "ont_educ_no")
        protected Integer ontEducNo;

        @XmlElement(name = "stat_in_canada")
        protected String statInCanada;

        @XmlElement(name = "lvl_start_date")
        protected String lvlStartDate;

        @XmlElement(name = "prog_cum_gpa")
        protected String progCumGpa;

        @XmlElement(name = "po_no_of_cals")
        protected String poNoOfCals;

        /**
         * Gets the value of the stdNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getStdNo()
        {
            return stdNo;
        }

        /**
         * Sets the value of the stdNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setStdNo(Integer value)
        {
            this.stdNo = value;
        }

        /**
         * Gets the value of the firstName property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getFirstName()
        {
            return firstName;
        }

        /**
         * Sets the value of the firstName property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setFirstName(String value)
        {
            this.firstName = value;
        }

        /**
         * Gets the value of the secondName property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getSecondName()
        {
            return secondName;
        }

        /**
         * Sets the value of the secondName property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setSecondName(String value)
        {
            this.secondName = value;
        }

        /**
         * Gets the value of the surname property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getSurname()
        {
            return surname;
        }

        /**
         * Sets the value of the surname property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setSurname(String value)
        {
            this.surname = value;
        }

        /**
         * Gets the value of the status property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getStatus()
        {
            return status;
        }

        /**
         * Sets the value of the status property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setStatus(Short value)
        {
            this.status = value;
        }

        /**
         * Gets the value of the term property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getTerm()
        {
            return term;
        }

        /**
         * Sets the value of the term property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setTerm(String value)
        {
            this.term = value;
        }

        /**
         * Gets the value of the prgCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPrgCode()
        {
            return prgCode;
        }

        /**
         * Sets the value of the prgCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPrgCode(String value)
        {
            this.prgCode = value;
        }

        /**
         * Gets the value of the campusCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCampusCode()
        {
            return campusCode;
        }

        /**
         * Sets the value of the campusCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCampusCode(String value)
        {
            this.campusCode = value;
        }

        /**
         * Gets the value of the delMode property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getDelMode()
        {
            return delMode;
        }

        /**
         * Sets the value of the delMode property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setDelMode(Short value)
        {
            this.delMode = value;
        }

        /**
         * Gets the value of the alInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getAlInd()
        {
            return alInd;
        }

        /**
         * Sets the value of the alInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setAlInd(String value)
        {
            this.alInd = value;
        }

        /**
         * Gets the value of the alLevel property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getAlLevel()
        {
            return alLevel;
        }

        /**
         * Sets the value of the alLevel property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setAlLevel(Short value)
        {
            this.alLevel = value;
        }

        /**
         * Gets the value of the ftPtInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getFtPtInd()
        {
            return ftPtInd;
        }

        /**
         * Sets the value of the ftPtInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setFtPtInd(String value)
        {
            this.ftPtInd = value;
        }

        /**
         * Gets the value of the schedContInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getSchedContInd()
        {
            return schedContInd;
        }

        /**
         * Sets the value of the schedContInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setSchedContInd(String value)
        {
            this.schedContInd = value;
        }

        /**
         * Gets the value of the vsnStartDate property.
         * 
         * @return possible object is {@link XMLGregorianCalendar }
         * 
         */
        public XMLGregorianCalendar getVsnStartDate()
        {
            return vsnStartDate;
        }

        /**
         * Sets the value of the vsnStartDate property.
         * 
         * @param value
         *            allowed object is {@link XMLGregorianCalendar }
         * 
         */
        public void setVsnStartDate(XMLGregorianCalendar value)
        {
            this.vsnStartDate = value;
        }

        /**
         * Gets the value of the gradInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getGradInd()
        {
            return gradInd;
        }

        /**
         * Sets the value of the gradInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setGradInd(String value)
        {
            this.gradInd = value;
        }

        /**
         * Gets the value of the prgTitle property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPrgTitle()
        {
            return prgTitle;
        }

        /**
         * Sets the value of the prgTitle property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPrgTitle(String value)
        {
            this.prgTitle = value;
        }

        /**
         * Gets the value of the nonCollegeEmail property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getNonCollegeEmail()
        {
            return nonCollegeEmail;
        }

        /**
         * Sets the value of the nonCollegeEmail property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setNonCollegeEmail(String value)
        {
            this.nonCollegeEmail = value;
        }

        /**
         * Gets the value of the areaCode property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getAreaCode()
        {
            return areaCode;
        }

        /**
         * Sets the value of the areaCode property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setAreaCode(Short value)
        {
            this.areaCode = value;
        }

        /**
         * Gets the value of the phoneNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getPhoneNo()
        {
            return phoneNo;
        }

        /**
         * Sets the value of the phoneNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setPhoneNo(Integer value)
        {
            this.phoneNo = value;
        }

        /**
         * Gets the value of the cellAreaCode property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getCellAreaCode()
        {
            return cellAreaCode;
        }

        /**
         * Sets the value of the cellAreaCode property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setCellAreaCode(Short value)
        {
            this.cellAreaCode = value;
        }

        /**
         * Gets the value of the cellPhoneNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getCellPhoneNo()
        {
            return cellPhoneNo;
        }

        /**
         * Sets the value of the cellPhoneNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setCellPhoneNo(Integer value)
        {
            this.cellPhoneNo = value;
        }

        /**
         * Gets the value of the noAndStreet property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getNoAndStreet()
        {
            return noAndStreet;
        }

        /**
         * Sets the value of the noAndStreet property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setNoAndStreet(String value)
        {
            this.noAndStreet = value;
        }

        /**
         * Gets the value of the city property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCity()
        {
            return city;
        }

        /**
         * Sets the value of the city property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCity(String value)
        {
            this.city = value;
        }

        /**
         * Gets the value of the provStateCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getProvStateCode()
        {
            return provStateCode;
        }

        /**
         * Sets the value of the provStateCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setProvStateCode(String value)
        {
            this.provStateCode = value;
        }

        /**
         * Gets the value of the postalZipCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPostalZipCode()
        {
            return postalZipCode;
        }

        /**
         * Sets the value of the postalZipCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPostalZipCode(String value)
        {
            this.postalZipCode = value;
        }

        /**
         * Gets the value of the country property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCountry()
        {
            return country;
        }

        /**
         * Sets the value of the country property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCountry(String value)
        {
            this.country = value;
        }

        public Short getAlSeqNumber()
        {
            return alSeqNumber;
        }

        public void setAlSeqNumber(Short alSeqNumber)
        {
            this.alSeqNumber = alSeqNumber;
        }

        public Integer getSin()
        {
            return sin;
        }

        public void setSin(Integer sin)
        {
            this.sin = sin;
        }

        public Integer getOntEducNo()
        {
            return ontEducNo;
        }

        public void setOntEducNo(Integer ontEducNo)
        {
            this.ontEducNo = ontEducNo;
        }

        public String getStatInCanada()
        {
            return statInCanada;
        }

        public void setStatInCanada(String statInCanada)
        {
            this.statInCanada = statInCanada;
        }

        public String getLvlStartDate()
        {
            return lvlStartDate;
        }

        public void setLvlStartDate(String lvlStartDate)
        {
            this.lvlStartDate = lvlStartDate;
        }

        public String getProgCumGpa()
        {
            return progCumGpa;
        }

        public void setProgCumGpa(String progCumGpa)
        {
            this.progCumGpa = progCumGpa;
        }

        public String getPoNoOfCals()
        {
            return poNoOfCals;
        }

        public void setPoNoOfCals(String poNoOfCals)
        {
            this.poNoOfCals = poNoOfCals;
        }

        /**
         * Gets the value of the statusDesc property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getStatusDesc()
        {
            return statusDesc;
        }

        /**
         * Sets the value of the statusDesc property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setStatusDesc(String value)
        {
            this.statusDesc = value;
        }

        /**
         * Gets the value of the collegeEmail property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCollegeEmail()
        {
            return collegeEmail;
        }

        /**
         * Sets the value of the collegeEmail property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCollegeEmail(String value)
        {
            this.collegeEmail = value;
        }

        /**
         * Gets the value of the dateOfBirth property.
         * 
         * @return possible object is {@link XMLGregorianCalendar }
         * 
         */
        public XMLGregorianCalendar getDateOfBirth()
        {
            return dateOfBirth;
        }

        /**
         * Sets the value of the dateOfBirth property.
         * 
         * @param value
         *            allowed object is {@link XMLGregorianCalendar }
         * 
         */
        public void setDateOfBirth(XMLGregorianCalendar value)
        {
            this.dateOfBirth = value;
        }

        /**
         * Gets the value of the gender property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getGender()
        {
            return gender;
        }

        /**
         * Sets the value of the gender property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setGender(String value)
        {
            this.gender = value;
        }

    }

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "stdNo", "term", "prgCode", "alLevel",
            "status", "campusCode", "delMode", "alInd", "ftPtInd", "vsnStartDate",
            "statusDescription", "prgTitle", "statusChgDate" })
    public static class TblStudentProgramStatus
    {

        @XmlElement(name = "std_no")
        protected Integer stdNo;

        protected String term;

        @XmlElement(name = "prg_code")
        protected String prgCode;

        @XmlElement(name = "al_level")
        protected Short alLevel;

        protected Short status;

        @XmlElement(name = "campus_code")
        protected String campusCode;

        @XmlElement(name = "del_mode")
        protected Short delMode;

        @XmlElement(name = "al_ind")
        protected String alInd;

        @XmlElement(name = "ft_pt_ind")
        protected String ftPtInd;

        @XmlElement(name = "vsn_start_date")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar vsnStartDate;

        @XmlElement(name = "status_description")
        protected String statusDescription;

        @XmlElement(name = "prg_title")
        protected String prgTitle;

        @XmlElement(name = "status_chg_date")
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar statusChgDate;

        /**
         * Gets the value of the stdNo property.
         * 
         * @return possible object is {@link Integer }
         * 
         */
        public Integer getStdNo()
        {
            return stdNo;
        }

        /**
         * Sets the value of the stdNo property.
         * 
         * @param value
         *            allowed object is {@link Integer }
         * 
         */
        public void setStdNo(Integer value)
        {
            this.stdNo = value;
        }

        /**
         * Gets the value of the term property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getTerm()
        {
            return term;
        }

        /**
         * Sets the value of the term property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setTerm(String value)
        {
            this.term = value;
        }

        /**
         * Gets the value of the prgCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPrgCode()
        {
            return prgCode;
        }

        /**
         * Sets the value of the prgCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPrgCode(String value)
        {
            this.prgCode = value;
        }

        /**
         * Gets the value of the alLevel property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getAlLevel()
        {
            return alLevel;
        }

        /**
         * Sets the value of the alLevel property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setAlLevel(Short value)
        {
            this.alLevel = value;
        }

        /**
         * Gets the value of the status property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getStatus()
        {
            return status;
        }

        /**
         * Sets the value of the status property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setStatus(Short value)
        {
            this.status = value;
        }

        /**
         * Gets the value of the campusCode property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getCampusCode()
        {
            return campusCode;
        }

        /**
         * Sets the value of the campusCode property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setCampusCode(String value)
        {
            this.campusCode = value;
        }

        /**
         * Gets the value of the delMode property.
         * 
         * @return possible object is {@link Short }
         * 
         */
        public Short getDelMode()
        {
            return delMode;
        }

        /**
         * Sets the value of the delMode property.
         * 
         * @param value
         *            allowed object is {@link Short }
         * 
         */
        public void setDelMode(Short value)
        {
            this.delMode = value;
        }

        /**
         * Gets the value of the alInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getAlInd()
        {
            return alInd;
        }

        /**
         * Sets the value of the alInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setAlInd(String value)
        {
            this.alInd = value;
        }

        /**
         * Gets the value of the ftPtInd property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getFtPtInd()
        {
            return ftPtInd;
        }

        /**
         * Sets the value of the ftPtInd property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setFtPtInd(String value)
        {
            this.ftPtInd = value;
        }

        /**
         * Gets the value of the vsnStartDate property.
         * 
         * @return possible object is {@link XMLGregorianCalendar }
         * 
         */
        public XMLGregorianCalendar getVsnStartDate()
        {
            return vsnStartDate;
        }

        /**
         * Sets the value of the vsnStartDate property.
         * 
         * @param value
         *            allowed object is {@link XMLGregorianCalendar }
         * 
         */
        public void setVsnStartDate(XMLGregorianCalendar value)
        {
            this.vsnStartDate = value;
        }

        /**
         * Gets the value of the statusDescription property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getStatusDescription()
        {
            return statusDescription;
        }

        /**
         * Sets the value of the statusDescription property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setStatusDescription(String value)
        {
            this.statusDescription = value;
        }

        /**
         * Gets the value of the prgTitle property.
         * 
         * @return possible object is {@link String }
         * 
         */
        public String getPrgTitle()
        {
            return prgTitle;
        }

        /**
         * Sets the value of the prgTitle property.
         * 
         * @param value
         *            allowed object is {@link String }
         * 
         */
        public void setPrgTitle(String value)
        {
            this.prgTitle = value;
        }

        /**
         * Gets the value of the statusChgDate property.
         * 
         * @return possible object is {@link XMLGregorianCalendar }
         * 
         */
        public XMLGregorianCalendar getStatusChgDate()
        {
            return statusChgDate;
        }

        /**
         * Sets the value of the statusChgDate property.
         * 
         * @param value
         *            allowed object is {@link XMLGregorianCalendar }
         * 
         */
        public void setStatusChgDate(XMLGregorianCalendar value)
        {
            this.statusChgDate = value;
        }

    }

}
