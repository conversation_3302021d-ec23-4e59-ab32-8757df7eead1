package org.tempuri.studentinfodataset;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the org.tempuri.studentinfodataset package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory
{

    /**
     * Create a new ObjectFactory that can be used to create new instances of
     * schema derived classes for package: org.tempuri.studentinfodataset
     * 
     */
    public ObjectFactory()
    {
    }

    /**
     * Create an instance of {@link StudentInfoDataSet.TblStudentCourseStatus }
     * 
     */
    public StudentInfoDataSet.TblStudentCourseStatus createStudentInfoDataSetTblStudentCourseStatus()
    {
        return new StudentInfoDataSet.TblStudentCourseStatus();
    }

    /**
     * Create an instance of {@link StudentInfoDataSet.TblStudentInfo }
     * 
     */
    public StudentInfoDataSet.TblStudentInfo createStudentInfoDataSetTblStudentInfo()
    {
        return new StudentInfoDataSet.TblStudentInfo();
    }

    /**
     * Create an instance of {@link StudentInfoDataSet }
     * 
     */
    public StudentInfoDataSet createStudentInfoDataSet()
    {
        return new StudentInfoDataSet();
    }

    /**
     * Create an instance of {@link StudentInfoDataSet.TblStudentProgramStatus }
     * 
     */
    public StudentInfoDataSet.TblStudentProgramStatus createStudentInfoDataSetTblStudentProgramStatus()
    {
        return new StudentInfoDataSet.TblStudentProgramStatus();
    }

    /**
     * Create an instance of {@link StudentInfoDataSet.TblStudentAddress }
     * 
     */
    public StudentInfoDataSet.TblStudentAddress createStudentInfoDataSetTblStudentAddress()
    {
        return new StudentInfoDataSet.TblStudentAddress();
    }

}
