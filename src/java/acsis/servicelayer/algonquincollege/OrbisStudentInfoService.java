package acsis.servicelayer.algonquincollege;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceFeature;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.2-hudson-752-
 * Generated source version: 2.2
 * 
 */
public class OrbisStudentInfoService extends Service
{

    public static String wsUrl = null;

    private static URL ORBISSTUDENTINFOSERVICE_WSDL_LOCATION;

    private final static QName ORBISSTUDENTINFOSERVICE_QNAME = new QName(
            "http://AlgonquinCollege.ServiceLayer.ACSIS", "OrbisStudentInfoService");

    public OrbisStudentInfoService()
    {
        super(__getWsdlLocation(), ORBISSTUDENTINFOSERVICE_QNAME);
    }

    public OrbisStudentInfoService(URL wsdlLocation, QName serviceName)
    {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return returns OrbisStudentInfoServiceSoap
     */
    @WebEndpoint(name = "OrbisStudentInfoServiceSoap")
    public OrbisStudentInfoServiceSoap getOrbisStudentInfoServiceSoap()
    {
        return super.getPort(new QName(
                "http://AlgonquinCollege.ServiceLayer.ACSIS",
                "OrbisStudentInfoServiceSoap"), OrbisStudentInfoServiceSoap.class);
    }

    /**
     * 
     * @param features
     *            A list of {@link javax.xml.ws.WebServiceFeature} to configure
     *            on the proxy. Supported features not in the
     *            <code>features</code> parameter will have their default
     *            values.
     * @return returns OrbisStudentInfoServiceSoap
     */
    @WebEndpoint(name = "OrbisStudentInfoServiceSoap")
    public OrbisStudentInfoServiceSoap getOrbisStudentInfoServiceSoap(
            WebServiceFeature... features)
    {
        return super.getPort(new QName(
                "http://AlgonquinCollege.ServiceLayer.ACSIS",
                "OrbisStudentInfoServiceSoap"), OrbisStudentInfoServiceSoap.class,
                features);
    }

    /**
     * 
     * @return returns OrbisStudentInfoServiceSoap
     */
    @WebEndpoint(name = "OrbisStudentInfoServiceSoap12")
    public OrbisStudentInfoServiceSoap getOrbisStudentInfoServiceSoap12()
    {
        return super
                .getPort(new QName("http://AlgonquinCollege.ServiceLayer.ACSIS",
                        "OrbisStudentInfoServiceSoap12"),
                        OrbisStudentInfoServiceSoap.class);
    }

    /**
     * 
     * @param features
     *            A list of {@link javax.xml.ws.WebServiceFeature} to configure
     *            on the proxy. Supported features not in the
     *            <code>features</code> parameter will have their default
     *            values.
     * @return returns OrbisStudentInfoServiceSoap
     */
    @WebEndpoint(name = "OrbisStudentInfoServiceSoap12")
    public OrbisStudentInfoServiceSoap getOrbisStudentInfoServiceSoap12(
            WebServiceFeature... features)
    {
        return super.getPort(new QName(
                "http://AlgonquinCollege.ServiceLayer.ACSIS",
                "OrbisStudentInfoServiceSoap12"),
                OrbisStudentInfoServiceSoap.class, features);
    }

    private static URL __getWsdlLocation()
    {
        if (ORBISSTUDENTINFOSERVICE_WSDL_LOCATION == null)
        {
            try
            {
                ORBISSTUDENTINFOSERVICE_WSDL_LOCATION = new URL(wsUrl);
            }
            catch (MalformedURLException e)
            {
                e.printStackTrace();
            }
        }

        return ORBISSTUDENTINFOSERVICE_WSDL_LOCATION;
    }

}
