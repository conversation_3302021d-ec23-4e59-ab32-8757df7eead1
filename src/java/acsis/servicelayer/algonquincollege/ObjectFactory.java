package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the acsis.servicelayer.algonquincollege
 * package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory
{

    /**
     * Create a new ObjectFactory that can be used to create new instances of
     * schema derived classes for package: acsis.servicelayer.algonquincollege
     * 
     */
    public ObjectFactory()
    {
    }

    /**
     * Create an instance of {@link GetStudentInformationResponse.StudentData }
     * 
     */
    public GetStudentInformationResponse.StudentData createGetStudentInformationResponseStudentData()
    {
        return new GetStudentInformationResponse.StudentData();
    }

    /**
     * Create an instance of {@link ArrayOfString }
     * 
     */
    public ArrayOfString createArrayOfString()
    {
        return new ArrayOfString();
    }

    /**
     * Create an instance of {@link GetStudentInformationResponse }
     * 
     */
    public GetStudentInformationResponse createGetStudentInformationResponse()
    {
        return new GetStudentInformationResponse();
    }

    /**
     * Create an instance of {@link GetStudentInformationLegacyModeResponse }
     * 
     */
    public GetStudentInformationLegacyModeResponse createGetStudentInformationLegacyModeResponse()
    {
        return new GetStudentInformationLegacyModeResponse();
    }

    /**
     * Create an instance of {@link GetStudentInformationLegacyMode }
     * 
     */
    public GetStudentInformationLegacyMode createGetStudentInformationLegacyMode()
    {
        return new GetStudentInformationLegacyMode();
    }

    /**
     * Create an instance of {@link GetStudentInformationRequest }
     * 
     */
    public GetStudentInformationRequest createGetStudentInformationRequest()
    {
        return new GetStudentInformationRequest();
    }

    /**
     * Create an instance of {@link Parameter }
     * 
     */
    public Parameter createParameter()
    {
        return new Parameter();
    }

    /**
     * Create an instance of {@link ArrayOfParameter }
     * 
     */
    public ArrayOfParameter createArrayOfParameter()
    {
        return new ArrayOfParameter();
    }

}
