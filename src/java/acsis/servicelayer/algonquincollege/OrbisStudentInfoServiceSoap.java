package acsis.servicelayer.algonquincollege;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.2-hudson-752-
 * Generated source version: 2.2
 * 
 */
@WebService(name = "OrbisStudentInfoServiceSoap", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS")
@XmlSeeAlso({ org.tempuri.studentinfodataset.ObjectFactory.class,
        acsis.servicelayer.algonquincollege.ObjectFactory.class })
public interface OrbisStudentInfoServiceSoap
{

    /**
     * 
     * @param username
     * @param request
     * @param password
     * @return returns
     *         acsis.servicelayer.algonquincollege.GetStudentInformationResponse
     */
    @WebMethod(operationName = "GetStudentInformationLegacyMode", action = "http://AlgonquinCollege.ServiceLayer.ACSIS/GetStudentInformationLegacyMode")
    @WebResult(name = "GetStudentInformationLegacyModeResult", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS")
    @RequestWrapper(localName = "GetStudentInformationLegacyMode", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS", className = "acsis.servicelayer.algonquincollege.GetStudentInformationLegacyMode")
    @ResponseWrapper(localName = "GetStudentInformationLegacyModeResponse", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS", className = "acsis.servicelayer.algonquincollege.GetStudentInformationLegacyModeResponse")
    public GetStudentInformationResponse getStudentInformationLegacyMode(
            @WebParam(name = "request", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS") GetStudentInformationRequest request,
            @WebParam(name = "username", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS") String username,
            @WebParam(name = "password", targetNamespace = "http://AlgonquinCollege.ServiceLayer.ACSIS") String password);

}
