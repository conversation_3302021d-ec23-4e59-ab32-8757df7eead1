package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for GetStudentInformationRequest complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="GetStudentInformationRequest">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="IdsToRetrieve" type="{http://AlgonquinCollege.ServiceLayer.ACSIS}ArrayOfString" minOccurs="0"/>
 *         &lt;element name="NamedParameters" type="{http://AlgonquinCollege.ServiceLayer.ACSIS}ArrayOfParameter" minOccurs="0"/>
 *         &lt;element name="WhereConjuntion" type="{http://AlgonquinCollege.ServiceLayer.ACSIS}Conjuntion"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetStudentInformationRequest", propOrder = { "idsToRetrieve",
        "namedParameters", "whereConjuntion" })
public class GetStudentInformationRequest
{

    @XmlElement(name = "IdsToRetrieve")
    protected ArrayOfString idsToRetrieve;

    @XmlElement(name = "NamedParameters")
    protected ArrayOfParameter namedParameters;

    @XmlElement(name = "WhereConjuntion", required = true)
    protected Conjuntion whereConjuntion;

    /**
     * Gets the value of the idsToRetrieve property.
     * 
     * @return possible object is {@link ArrayOfString }
     * 
     */
    public ArrayOfString getIdsToRetrieve()
    {
        return idsToRetrieve;
    }

    /**
     * Sets the value of the idsToRetrieve property.
     * 
     * @param value
     *            allowed object is {@link ArrayOfString }
     * 
     */
    public void setIdsToRetrieve(ArrayOfString value)
    {
        this.idsToRetrieve = value;
    }

    /**
     * Gets the value of the namedParameters property.
     * 
     * @return possible object is {@link ArrayOfParameter }
     * 
     */
    public ArrayOfParameter getNamedParameters()
    {
        return namedParameters;
    }

    /**
     * Sets the value of the namedParameters property.
     * 
     * @param value
     *            allowed object is {@link ArrayOfParameter }
     * 
     */
    public void setNamedParameters(ArrayOfParameter value)
    {
        this.namedParameters = value;
    }

    /**
     * Gets the value of the whereConjuntion property.
     * 
     * @return possible object is {@link Conjuntion }
     * 
     */
    public Conjuntion getWhereConjuntion()
    {
        return whereConjuntion;
    }

    /**
     * Sets the value of the whereConjuntion property.
     * 
     * @param value
     *            allowed object is {@link Conjuntion }
     * 
     */
    public void setWhereConjuntion(Conjuntion value)
    {
        this.whereConjuntion = value;
    }

}
