package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for GetStudentInformationResponse complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="GetStudentInformationResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="StudentData" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;any namespace='http://tempuri.org/StudentInfoDataSet.xsd'/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetStudentInformationResponse", propOrder = { "studentData" })
public class GetStudentInformationResponse
{

    @XmlElement(name = "StudentData")
    protected GetStudentInformationResponse.StudentData studentData;

    /**
     * Gets the value of the studentData property.
     * 
     * @return possible object is
     *         {@link GetStudentInformationResponse.StudentData }
     * 
     */
    public GetStudentInformationResponse.StudentData getStudentData()
    {
        return studentData;
    }

    /**
     * Sets the value of the studentData property.
     * 
     * @param value
     *            allowed object is
     *            {@link GetStudentInformationResponse.StudentData }
     * 
     */
    public void setStudentData(GetStudentInformationResponse.StudentData value)
    {
        this.studentData = value;
    }

    /**
     * <p>
     * Java class for anonymous complex type.
     * 
     * <p>
     * The following schema fragment specifies the expected content contained
     * within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;any namespace='http://tempuri.org/StudentInfoDataSet.xsd'/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = { "any" })
    public static class StudentData
    {

        @XmlAnyElement(lax = true)
        protected Object any;

        /**
         * Gets the value of the any property.
         * 
         * @return possible object is {@link Object }
         * 
         */
        public Object getAny()
        {
            return any;
        }

        /**
         * Sets the value of the any property.
         * 
         * @param value
         *            allowed object is {@link Object }
         * 
         */
        public void setAny(Object value)
        {
            this.any = value;
        }

    }

}
