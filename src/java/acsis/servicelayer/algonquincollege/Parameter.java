package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for Parameter complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Parameter">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ColumnName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Parameter", propOrder = { "columnName", "value" })
public class Parameter
{

    @XmlElement(name = "ColumnName")
    protected String columnName;

    @XmlElement(name = "Value")
    protected Object value;

    /**
     * Gets the value of the columnName property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getColumnName()
    {
        return columnName;
    }

    /**
     * Sets the value of the columnName property.
     * 
     * @param value
     *            allowed object is {@link String }
     * 
     */
    public void setColumnName(String value)
    {
        this.columnName = value;
    }

    /**
     * Gets the value of the value property.
     * 
     * @return possible object is {@link Object }
     * 
     */
    public Object getValue()
    {
        return value;
    }

    /**
     * Sets the value of the value property.
     * 
     * @param value
     *            allowed object is {@link Object }
     * 
     */
    public void setValue(Object value)
    {
        this.value = value;
    }

}
