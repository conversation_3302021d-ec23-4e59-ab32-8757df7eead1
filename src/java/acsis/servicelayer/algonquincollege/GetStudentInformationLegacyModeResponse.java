package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="GetStudentInformationLegacyModeResult" type="{http://AlgonquinCollege.ServiceLayer.ACSIS}GetStudentInformationResponse" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "getStudentInformationLegacyModeResult" })
@XmlRootElement(name = "GetStudentInformationLegacyModeResponse")
public class GetStudentInformationLegacyModeResponse
{

    @XmlElement(name = "GetStudentInformationLegacyModeResult")
    protected GetStudentInformationResponse getStudentInformationLegacyModeResult;

    /**
     * Gets the value of the getStudentInformationLegacyModeResult property.
     * 
     * @return possible object is {@link GetStudentInformationResponse }
     * 
     */
    public GetStudentInformationResponse getGetStudentInformationLegacyModeResult()
    {
        return getStudentInformationLegacyModeResult;
    }

    /**
     * Sets the value of the getStudentInformationLegacyModeResult property.
     * 
     * @param value
     *            allowed object is {@link GetStudentInformationResponse }
     * 
     */
    public void setGetStudentInformationLegacyModeResult(
            GetStudentInformationResponse value)
    {
        this.getStudentInformationLegacyModeResult = value;
    }

}
