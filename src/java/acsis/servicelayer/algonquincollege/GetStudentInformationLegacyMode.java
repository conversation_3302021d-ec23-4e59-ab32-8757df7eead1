package acsis.servicelayer.algonquincollege;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="request" type="{http://AlgonquinCollege.ServiceLayer.ACSIS}GetStudentInformationRequest" minOccurs="0"/>
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "request", "username", "password" })
@XmlRootElement(name = "GetStudentInformationLegacyMode")
public class GetStudentInformationLegacyMode
{

    protected GetStudentInformationRequest request;

    protected String username;

    protected String password;

    /**
     * Gets the value of the request property.
     * 
     * @return possible object is {@link GetStudentInformationRequest }
     * 
     */
    public GetStudentInformationRequest getRequest()
    {
        return request;
    }

    /**
     * Sets the value of the request property.
     * 
     * @param value
     *            allowed object is {@link GetStudentInformationRequest }
     * 
     */
    public void setRequest(GetStudentInformationRequest value)
    {
        this.request = value;
    }

    /**
     * Gets the value of the username property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getUsername()
    {
        return username;
    }

    /**
     * Sets the value of the username property.
     * 
     * @param value
     *            allowed object is {@link String }
     * 
     */
    public void setUsername(String value)
    {
        this.username = value;
    }

    /**
     * Gets the value of the password property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getPassword()
    {
        return password;
    }

    /**
     * Sets the value of the password property.
     * 
     * @param value
     *            allowed object is {@link String }
     * 
     */
    public void setPassword(String value)
    {
        this.password = value;
    }

}
