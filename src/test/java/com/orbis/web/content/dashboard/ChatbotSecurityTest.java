package com.orbis.web.content.dashboard;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;

/**
 * Security tests for chatbot functionality
 */
@ExtendWith(MockitoExtension.class)
public class ChatbotSecurityTest
{
    @Mock
    private PortalConfig mockConfig;
    
    private ChatbotRateLimiter rateLimiter;
    private IntentParser intentParser;
    private IntentRouter intentRouter;
    
    @BeforeEach
    void setUp()
    {
        rateLimiter = new ChatbotRateLimiter();
        intentParser = new IntentParser();
        intentRouter = new IntentRouter();
    }
    
    @Test
    void testRateLimiterAllowsNormalUsage()
    {
        // Test that normal usage is allowed
        String userId = "testuser";
        
        // Should allow first request
        assertTrue(rateLimiter.isRequestAllowed(userId));
        
        // Should allow multiple requests within limit
        for (int i = 0; i < 19; i++)
        {
            assertTrue(rateLimiter.isRequestAllowed(userId));
        }
        
        // 20th request should still be allowed (default limit is 20)
        assertTrue(rateLimiter.isRequestAllowed(userId));
    }
    
    @Test
    void testRateLimiterBlocksExcessiveRequests()
    {
        String userId = "testuser";
        
        // Use up the rate limit
        for (int i = 0; i < 20; i++)
        {
            assertTrue(rateLimiter.isRequestAllowed(userId));
        }
        
        // 21st request should be blocked
        assertFalse(rateLimiter.isRequestAllowed(userId));
    }
    
    @Test
    void testRateLimiterHandlesEmptyUserId()
    {
        assertFalse(rateLimiter.isRequestAllowed(""));
        assertFalse(rateLimiter.isRequestAllowed(null));
    }
    
    @Test
    void testIntentParserRejectsEmptyInput()
    {
        assertThrows(IllegalArgumentException.class, () -> {
            IntentParser.parse("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            IntentParser.parse(null);
        });
    }
    
    @Test
    void testIntentParserRejectsUnauthorizedIntent()
    {
        String response = "Intent Name: unauthorizedIntent\nAction: malicious";
        
        assertThrows(IllegalArgumentException.class, () -> {
            IntentParser.parse(response);
        });
    }
    
    @Test
    void testIntentParserAcceptsValidIntent()
    {
        String response = "Intent Name: getUpcomingEvents\nAction: list\nFromDate: 2024-01-01\nToDate: 2024-01-31";
        
        ParsedIntent parsed = IntentParser.parse(response);
        
        assertNotNull(parsed);
        assertEquals("getUpcomingEvents", parsed.getIntent());
        assertNotNull(parsed.getArguments());
        assertTrue(parsed.getArguments().containsKey("Action"));
    }
    
    @Test
    void testIntentParserSanitizesInput()
    {
        // Test with potentially malicious content
        String response = "Intent Name: getUpcomingEvents\nAction: <script>alert('xss')</script>\nFromDate: null";
        
        ParsedIntent parsed = IntentParser.parse(response);
        
        assertNotNull(parsed);
        assertEquals("getUpcomingEvents", parsed.getIntent());
        
        // Verify that script tags are removed/sanitized
        String action = (String) parsed.getArguments().get("Action");
        assertFalse(action.contains("<script>"));
    }
    
    @Test
    void testIntentRouterRejectsInvalidInput()
    {
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.route("", new HashMap<>());
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.route("validFunction", null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.route(null, new HashMap<>());
        });
    }
    
    @Test
    void testIntentRouterRejectsUnregisteredFunction()
    {
        Map<String, Object> args = new HashMap<>();
        
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.route("unregisteredFunction", args);
        });
    }
    
    @Test
    void testIntentRouterExecutesRegisteredFunction()
    {
        // Register a test function
        FunctionHandler testHandler = (args) -> "test result";
        intentRouter.registerFunction("testFunction", testHandler);
        
        Map<String, Object> args = new HashMap<>();
        Object result = intentRouter.route("testFunction", args);
        
        assertEquals("test result", result);
    }
    
    @Test
    void testIntentRouterHandlesFunctionErrors()
    {
        // Register a function that throws an exception
        FunctionHandler errorHandler = (args) -> {
            throw new RuntimeException("Test error");
        };
        intentRouter.registerFunction("errorFunction", errorHandler);
        
        Map<String, Object> args = new HashMap<>();
        
        assertThrows(RuntimeException.class, () -> {
            intentRouter.route("errorFunction", args);
        });
    }
    
    @Test
    void testIntentRouterValidatesRegistration()
    {
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.registerFunction("", (args) -> "test");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.registerFunction("validName", null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            intentRouter.registerFunction(null, (args) -> "test");
        });
    }
    
    @Test
    void testIntentRouterUtilityMethods()
    {
        assertFalse(intentRouter.isRegistered("nonexistent"));
        assertTrue(intentRouter.getRegisteredFunctions().isEmpty());
        
        intentRouter.registerFunction("testFunction", (args) -> "test");
        
        assertTrue(intentRouter.isRegistered("testFunction"));
        assertEquals(1, intentRouter.getRegisteredFunctions().size());
        assertTrue(intentRouter.getRegisteredFunctions().contains("testFunction"));
    }
}
