-- Configuration entries for secure chatbot implementation
-- These should be added to the PortalConfig table

-- OpenAI API Configuration (encrypted values should be used in production)
INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('OPENAI_API_KEY', 'ENCRYPTED_API_KEY_HERE', 'OpenAI API key for chatbot functionality (encrypted)');

INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('OPENAI_API_URL', 'https://api.openai.com/v1/chat/completions', 'OpenAI API endpoint URL');

INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('OPENAI_MAX_TOKENS', '500', 'Maximum tokens for OpenAI responses');

INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('OPENAI_TEMPERATURE', '0.7', 'Temperature setting for OpenAI model');

-- Rate Limiting Configuration
INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('CHATBOT_RATE_LIMIT_PER_HOUR', '20', 'Maximum chatbot requests per user per hour');

INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('CHATBOT_RATE_LIMIT_WINDOW_MINUTES', '60', 'Rate limiting window in minutes');

-- Dashboard Item Configuration for Student Chatbot Widget
INSERT INTO PortalConfig (orbisKey, orbisValue, description) 
VALUES ('sw_chatbot', '1', 'Enable chatbot widget on student dashboard');

-- Add to secret configs list (this should be updated to include OPENAI_API_KEY)
-- UPDATE PortalConfig SET orbisValue = CONCAT(orbisValue, ',"OPENAI_API_KEY"') 
-- WHERE orbisKey = 'SECRET_CONFIGS';

-- Note: In production, the API key should be encrypted using the CryptoUtils.encrypt() method
-- Example of how to encrypt the API key:
-- String encryptedKey = CryptoUtils.getInstance().encrypt("your-actual-api-key-here");
-- Then store the encrypted value in the OPENAI_API_KEY configuration

-- Internationalization entries for the chatbot
-- These would typically go in the i18n message files, but shown here for reference:
-- i18n.dashboard_studentHome.EventsAssistant=Events Assistant
-- i18n.chatbot.rateLimit=Rate limit exceeded. Please try again later.
-- i18n.chatbot.accessDenied=Access denied - feature only available to students.
-- i18n.chatbot.serviceUnavailable=Service temporarily unavailable.
