--Sample schemaUpdate format:yyyymmdd_JIRAID
?20230106_OUTCOME-10706
insert portal_migration_history (migrationKey, dateRun, success)  values ('sample1' ,dbo.fn_currentDate(), 1)
?202301013_OUTCOME-11141
INSERT INTO user_group (name,category,externalGroup,primaryGroup,description) SELECT 'Can View Satisfaction Survey Report', 'Appointment Booking', 0, 0, 'This permission controls who can view the satisfaction survey report within the reports tab of each appointments module' EXCEPT SELECT name, 'Appointment Booking', 0, 0, 'This permission controls who can view the satisfaction survey report within the reports tab of each appointments module' FROM user_group WHERE name='Can View Satisfaction Survey Report'; --NOFAIL
?20230120_OUTCOME-10990
DELETE nwtra2 FROM interaction_note_work_term_record_assigned nwtra JOIN coop_wtr_main wtr ON wtr.id= nwtra.workTermRecordd AND wtr.jobPosting IS NOT NULL JOIN interaction_note_work_term_record_assigned nwtra2 ON nwtra2.note= nwtra.note AND nwtra2.id> nwtra.id AND nwtra2.workTermRecordd= wtr.jobPosting; --NOFAIL
?20230106_OUTCOME-11067
update lcol set lcol.visible=1, lcol.ordinal=5 from grid g inner join grid_category gcat on gcat.grid=g.id inner join grid_column lcol on gcat.id=lcol.category where g.gridID='qf_studentQualificationsWaitingForApproval' and lcol.colID='qualified' --NOFAIL
update lcol set lcol.ordinal=5 from grid g inner join grid_category gcat on gcat.grid=g.id inner join grid_column lcol on gcat.id=lcol.category where g.gridID='qf_studentQualificationsWaitingForApproval' and lcol.colID='uploadedDocumentId' --NOFAIL
insert into portal_migration_history(migrationKey, dateRun, success) values ('OUTCOME-11067_qf_studentQualificationsWaitingForApproval', dbo.fn_currentDate(), 1)
?20230123_OUTCOME-10955
insert into coop_currency (name, l2Name, module, ordinal) select 'Canadian', 'Canadien', m.id, 1 from coop_module m except select c.name, 'Canadien', c.module, 1 from coop_currency c where c.name='Canadian'; --NOFAIL
insert into coop_currency (name, l2Name, module, ordinal) select 'US', 'US', m.id, 2 from coop_module m except select c.name, 'US', c.module, 2 from coop_currency c where c.name='US'; --NOFAIL
insert into coop_currency (name, l2Name, module, ordinal) select 'Other', 'Autre', m.id, 3 from coop_module m except select c.name, 'Autre', c.module, 3 from coop_currency c where c.name='Other'; --NOFAIL
?20230322_OUTCOME-11323
JAVA upgradePortalTo20230322_OUTCOME11323
?20230316_OUTCOME-11125
JAVA upgradePortalTo20230316_OUTCOME_11125
?20230406_OUTCOME-11388
UPDATE insights_module SET enableSpiralRobot = 1; --NOFAIL
?20230410_OUTCOME-11406
update lv_lastViewed set insightFilters = null where insightFilters is not null; --NOFAIL
?20230220_OUTCOME-11161
update exp_posting set numberOfRecords = (select count (r.id) from exp_record r where r.posting=exp_posting.id); --NOFAIL
?20230413_OUTCOME-10447
JAVA updatePortalTo20230413_OUTCOME_10447
?20230420_OUTCOME-11450
with currencyOrder as (select ordinal, row_number() over (partition by [module] order by id asc) as newOrder from coop_currency) update currencyOrder set ordinal=newOrder; --NOFAIL
?20230427_OUTCOME-11484
DELETE qm FROM acrm_registration_question_df_question_mapping qm JOIN acrm_registration_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.regQuestion=qm.regQuestion AND qm2.id!=qm.id AND qm2.mappingType=qm.mappingType WHERE qm.module IS NULL AND qm2.module IS NOT NULL AND qm.mappingType='com.orbis.web.content.acrm.AcrmRegistrationQuestionWorkTermMapping'; --NOFAIL
DELETE qm FROM st_rank_work_term_mapping qm JOIN st_rank_work_term_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.rankingField=qm.rankingField AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
DELETE qm FROM acrm_division_question_df_question_mapping qm JOIN acrm_division_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.divQuestion=qm.divQuestion AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
DELETE qm FROM acrm_org_question_df_question_mapping qm JOIN acrm_org_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.orgQuestion=qm.orgQuestion AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_registration_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL AND qm.mappingType='com.orbis.web.content.acrm.AcrmRegistrationQuestionWorkTermMapping'; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM st_rank_work_term_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_division_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_org_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
?20230426_OUTCOME-11370
JAVA upgradePortalTo20230426_OUTCOME_11370
?20230404_OUTCOME-10941
insert into portal_config(orbisKey, orbisValue) values('ALLOWED_EMAIL_SENDER_DOMAIN', ''); --NOFAIL
?20230314_OUTCOME-11241
insert into portal_config (orbisKey, orbisValue) values ('SMTP_CONNECTION_CACHE_MESSAGE_COUNT', '1'); --NOFAIL
?20230314_OUTCOME-11338
JAVA upgradePortalTo20230316_OUTCOME11338
?20230509_OUTCOME-11487
insert into portal_config(orbisKey, orbisValue) values('SECRET_CONFIGS', ''); --NOFAIL
?20230508_OUTCOME-10974
insert into portal_config(orbisKey, orbisValue) values('GTSM_RACE_OR_ETHNICITY', 'i18n.PortalConfig.GTSM.RaceOrEthnicity'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_YEAR_OF_STUDY', 'i18n.PortalConfig.GTSM.YearOfStudy'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ACADEMIC_PROGRAM_OF_STUDY', 'i18n.PortalConfig.GTSM.AcademicProgramOfStudy'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_FINANCIAL_AID', 'i18n.PortalConfig.GTSM.FinancialAid'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ACCESSIBILITY', 'i18n.PortalConfig.GTSM.Accessibility'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CITIZENSHIP_INDICATOR', 'i18n.PortalConfig.GTSM.CitizenshipIndicator'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_RESIDENCY_INDICATOR', 'i18n.PortalConfig.GTSM.ResidencyIndicator'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_GENDER_IDENTITY', 'i18n.PortalConfig.GTSM.GenderIdentity'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_STUDENT_CONDUCT', 'i18n.PortalConfig.GTSM.StudentConduct'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INTERESTED_SERVICES', 'i18n.PortalConfig.GTSM.InterestedServices'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INDUSTRY_CLASSIFICATION', 'i18n.PortalConfig.GTSM.IndustryClassification'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ORGANIZATION_SIZE', 'i18n.PortalConfig.GTSM.OrganizationSize'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ORGANIZATION_TYPE', 'i18n.PortalConfig.GTSM.OrganizationType'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_WORK_SAFETY_COMPLIANCE', 'i18n.PortalConfig.GTSM.WorkSafetyCompliance'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INSURANCE', 'i18n.PortalConfig.GTSM.Insurance'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_EMPLOYER_LOCATION', 'i18n.PortalConfig.GTSM.EmployerLocation'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_EMPLOYER_CONDUCT', 'i18n.PortalConfig.GTSM.EmployerConduct'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_1', 'i18n.PortalConfig.GTSM.CustomMapping1'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_2', 'i18n.PortalConfig.GTSM.CustomMapping2'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_3', 'i18n.PortalConfig.GTSM.CustomMapping3'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_4', 'i18n.PortalConfig.GTSM.CustomMapping4'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_5', 'i18n.PortalConfig.GTSM.CustomMapping5'); --NOFAIL
?20230515_OUTCOME-11499
update df_question_site_mapping set siteMapping = (select pc.id from portal_config pc where pc.orbisKey = 'DFQSM_JOB_LOCATION_TYPE') where id in (select dqsm.id from df_model dm join df_category dc on dm.id = dc.model join df_question dq on dc.id = dq.category join df_question_site_mapping dqsm on dq.id = dqsm.dfQuestion where dm.modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeRecordModel' and dq.answerField1 = 's9'); --NOFAIL
INSERT INTO df_question_site_mapping (dfQuestion, siteMapping) select dq.id, (select pc.id from portal_config pc where pc.orbisKey = 'DFQSM_JOB_LOCATION_TYPE') from df_model dm join df_category dc on dm.id = dc.model join df_question dq on dc.id = dq.category where dm.modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeRecordModel' and dq.answerField1 = 's9'; --NOFAIL
?20230515_OUTCOME-11505
JAVA upgradePortalTo20230515_OUTCOME11505
?20230510_OUTCOME-11488
JAVA upgradePortalTo20230510_OUTCOME_11488
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_URL', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_USERNAME', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_PASSWORD', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_CERTIFICATION_KEY', ''); --NOFAIL
?20230523_OUTCOME-11559
UPDATE npp SET npp.crmOrganization=o.id, npp.organization=o.name FROM np_posting npp JOIN company comp ON comp.id = npp.crmCompany join organization o on o.id=comp.organization WHERE npp.crmOrganization is NULL; --NOFAIL
UPDATE cwm SET cwm.organization = npp.crmOrganization FROM coop_wtr_main cwm JOIN np_posting npp ON cwm.jobPosting = npp.id WHERE cwm.organization is NULL; --NOFAIL
?20230606_OUTCOME-11480
UPDATE exp_type_workflow_template SET integrationCreateRecords = IIF(importDontCreateRecords = 1, 0, 1); --NOFAIL
DECLARE @command VARCHAR(255) = 'ALTER TABLE exp_type_workflow_template DROP CONSTRAINT ' + (SELECT TOP 1 c.name FROM sys.default_constraints c JOIN sys.columns col ON col.column_id = c.parent_column_id AND col.object_id=c.parent_object_id WHERE col.name='importDontCreateRecords' AND c.parent_object_id=OBJECT_ID('exp_type_workflow_template')); EXEC (@command); --NOFAIL
ALTER TABLE exp_type_workflow_template DROP COLUMN IF EXISTS importDontCreateRecords; --NOFAIL
?20230615_OUTCOME-11540
INSERT INTO portal_config (orbisKey, orbisValue) SELECT 'INCLUDE_PROSPECTS_IN_GLOBAL_SEARCH','0' EXCEPT SELECT orbisKey, '0' FROM portal_config WHERE orbisKey='INCLUDE_PROSPECTS_IN_GLOBAL_SEARCH'; --NOFAIL
?20230703_OUTCOME-11625
IF NOT EXISTS (SELECT name FROM sys.indexes WHERE name = N'idx_code_unique_notnull' AND object_id = OBJECT_ID(N'interview_interviewer', N'U')) CREATE UNIQUE NONCLUSTERED INDEX idx_code_unique_notnull ON interview_interviewer(code) WHERE code IS NOT NULL; --NOFAIL
?20230712_OUTCOME-11488
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_DOC_ID', ''); --NOFAIL
?20230522_OUTCOME-11449
JAVA upgradePortalTo20230522_OUTCOME_11449
?20230720_OUTCOME_11555
UPDATE event_letter_template SET defaultForType = 0 WHERE type = 0; --NOFAIL
JAVA upgradePortalTo20230720_OUTCOME_11555
?20230727_OUTCOME-11702
JAVA upgradePortalTo20230801_OUTCOME_11702
?20230802_OUTCOME-11644
JAVA upgradePortalTo20230802_OUTCOME_11644
?20230828_OUTCOME-11649
JAVA upgradePortalTo20230828_OUTCOME_11649
?20230831_OUTCOME-11124
JAVA upgradePortalTo20230831_OUTCOME_11124
?20230905_OUTCOME-11497
update dashboard_message set legacy = 1; --NOFAIL
?20230928_OUTCOME-11845
JAVA upgradePortalTo20230928_OUTCOME_11845
?20231204_OUTCOME-11992
insert into portal_config(orbisKey, orbisValue) values('GOOGLE_ANALYTICS_MEASUREMENT_ID', ''); --NOFAIL
?20231017_OUTCOME-11842
JAVA upgradePortalTo20231017_OUTCOME_11842
?20231114_OUTCOME-11964
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('COMPETENCY_DISPLAY_SETTING', '1'); --NOFAIL
?20230729_OUTCOME-11682
UPDATE portal_config SET orbisKey = 'DIGITARY_DOC_TYPE' WHERE orbisKey = 'DIGITARY_DOC_ID'; --NOFAIL
?20240102_OUTCOME-12068
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_IDP', ''); --NOFAIL
?20240111_OUTCOME-11987
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_APPLICATION_INSTRUCTION'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_APPLICATION_INSTRUCTION'; --NOFAIL
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_DESCRIPTION'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_DESCRIPTION'; --NOFAIL
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_TITLE'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_TITLE'; --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PLACEMENT_METHOD', 'i18n.PortalConfig.DFQSM.PlacementM1125259841418166'); --NOFAIL
?20240116_OUTCOME-12093
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PAYMENT_NAME', 'i18n.PortalConfig.DFQSM.PaymentNam1418040940091500'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PAYMENT_VALUE', 'i18n.PortalConfig.DFQSM.PaymentVal7634117895411759'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_WORKPLACE_TYPE', 'i18n.PortalConfig.DFQSM.WorkplaceT1544261977268078'); --NOFAIL
?20231019_OUTCOME-11382
JAVA upgradePortalTo20231019_OUTCOME_11382
?20230920_OUTCOME-11794
JAVA upgradePortalTo20230920_OUTCOME_11794
?20240221_OUTCOME-12154
JAVA upgradePortalTo20240221_OUTCOME_12154
update dashboard_module set studentTabsOrder = studentTabsOrder + ',st16'; --NOFAIL
update dashboard_module set portalStaffTabsOrder = portalStaffTabsOrder + ',pt24'; --NOFAIL
?20230804_OUTCOME-11709
JAVA upgradePortalTo20230804_OUTCOME11709
?20230804_OUTCOME-11751
UPDATE interaction_form SET draftSubmittedOn = dateCreated WHERE draftSubmittedOn IS NULL AND draft = 0; --NOFAIL
?20230810_OUTCOME-11711
JAVA upgradePortalTo202308810_OUTCOME_11711
?20231111_OUTCOME-11585
update doc_type set maxDefaultDocs=1 where allowDefault=1; --NOFAIL
?20240222_OUTCOME-12177
DELETE FROM portal_config WHERE orbisKey='SHOW_CAMPUS_LINK_TAB'; --NOFAIL
?20240319_OUTCOME-12208
DECLARE @command varchar(255) = 'ALTER TABLE np_posting DROP CONSTRAINT ' + (select top 1 c.name from sys.default_constraints c join sys.columns col on col.column_id = c.parent_column_id and col.object_id=c.parent_object_id where col.name='featuredPaymentApproved'); exec (@command); --NOFAIL
ALTER TABLE np_posting DROP COLUMN IF EXISTS featuredPaymentApproved; --NOFAIL
?20240409_OUTCOME-12229
JAVA upgradePortalTo20240409_OUTCOME_12229
UPDATE user_group SET name='Experiential - Faculty View Access' WHERE name='Experiential Faculty Advisor - Can View'; --NOFAIL
UPDATE user_group SET name='Faculty Course Management' WHERE name='Experiential Faculty Advisor - Can Manage'; --NOFAIL
?20240405_OUTCOME-12057
JAVA upgradePortalTo20240405_OUTCOME_12057
?20240501_OUTCOME-12226
DELETE FROM portal_config WHERE orbisKey='VC_BROADCAST_MESSAGE_ACTIVE';
?20240209_OUTCOME-12137
JAVA upgradePortalTo20240109_OUTCOME_12137
?20240229_OUTCOME-12148
UPDATE user_details SET preferredFirstName = firstName WHERE preferredFirstName IS NULL; --NOFAIL
?20240502_OUTCOME-12229_part2
INSERT INTO user_details_groups(userDetailsId,userGroupId) SELECT udg.userDetailsId, ug_to.id FROM user_details_groups udg JOIN user_group ug_from ON udg.userGroupId = ug_from.id JOIN user_group ug_to ON ug_to.name = 'Faculty Experiential Workflow Management' WHERE ug_from.name = 'Faculty Course Management'; --NOFAIL
?20240502_OUTCOME-12280
INSERT INTO portal_config(orbisKey, orbisValue) SELECT 'DFQSM_SECTOR', 'i18n.PortalConfig.DFQSM.Sector' EXCEPT SELECT orbisKey, 'i18n.PortalConfig.DFQSM.Sector' FROM portal_config WHERE orbisKey = 'DFQSM_SECTOR'; --NOFAIL
?20230630_OUTCOME-11542
JAVA upgradePortalTo20230630_OUTCOME_11542
?20240531_OUTCOME-12217
INSERT INTO portal_config (orbisKey, orbisValue) SELECT 'REGISTRATION_APPROVAL_SHOW_INFO_SESSIONS', '0' EXCEPT SELECT orbisKey, '0' FROM portal_config WHERE orbisKey = 'REGISTRATION_APPROVAL_SHOW_INFO_SESSIONS'; --NOFAIL
?20240522_OUTCOME-11943
JAVA upgradePortalTo20240522_OUTCOME_11943
?20240522_OUTCOME-11932
JAVA upgradePortalTo20240522_OUTCOME_11932
?20240611_OUTCOME-12339
update r set r.status=1 from acrm_reflection_record_exp_student_experience_step rrses join exp_student_experience_step ses on rrses.expStudentExperienceStep=ses.id join acrm_reflection_record_assign rra on rra.id=rrses.id join acrm_reflection_record r on r.id=rra.acrmReflectionRecord where ses.status=2 and r.status=0; --NOFAIL
JAVA upgradePortalTo20240611_OUTCOME_12339
?20240702_OUTCOME-12467
JAVA upgradePortalTo20240611_OUTCOME_12467
?20240809_OUTCOME-12558
update np_posting_view set reportedToOCC=1
update exp_posting_view set reportedToOCC=1
?20240126_OUTCOME-12112
JAVA upgradePortalTo20240126_OUTCOME_12112
?20240809_OUTCOME-12698
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='googleMapAdminController' --NOFAIL
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='googleMapController' --NOFAIL
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='miniListController' --NOFAIL
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='searchListController' --NOFAIL
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='carletonCCRLoginController' --NOFAIL
update site_element set type='deprecatedController', contentItem=null, contentItemClass = null where type='georgeBrownAlumniLoginController' --NOFAIL
?20241016_OUTCOME-12722
UPDATE ecommerce_order SET orderEntityClass = LEFT(orderEntityClass, CHARINDEX('$HibernateProxy$', orderEntityClass) - 1) WHERE orderEntityClass LIKE '%$HibernateProxy$%'; --NOFAIL
?20240922_OUTCOME-12432
JAVA upgradePortalTo20240922_OUTCOME_12432
?20241028_OUTCOME-12738
UPDATE calendar_filter SET dropInFilter = '2' WHERE dropInFilter is null --NOFAIL
?20241115_OUTCOME-12808
update df_question set header=''    where header='<p>&nbsp;</p>' --NOFAIL
update df_question set headerL2=''  where headerL2='<p>&nbsp;</p>' --NOFAIL
update df_question set footer=''    where footer='<p>&nbsp;</p>' --NOFAIL
update df_question set footerL2=''  where footerL2='<p>&nbsp;</p>' --NOFAIL
?20241119_OUTCOME-12816
UPDATE integration_table_config SET assignCoopCoordinators = 0 WHERE assignCoopCoordinators IS NULL; --NOFAIL
ALTER TABLE integration_table_config ALTER COLUMN assignCoopCoordinators TINYINT NOT NULL; --NOFAIL
?20241203_OUTCOME-12963
UPDATE acrm_division_question_df_question_mapping SET mappingType = RTRIM(mappingType);
UPDATE acrm_registration_question_df_question_mapping SET mappingType = RTRIM(mappingType);
UPDATE ec_mediaAttachment SET contentType = RTRIM(contentType);
UPDATE ec_mediaAttachment_assign SET assignType = RTRIM(assignType);
UPDATE exp_system_activity_record SET recordType = RTRIM(recordType);
UPDATE integration_post_process_action SET actionType = RTRIM(actionType);
UPDATE interaction_engagement_activity SET entityType = RTRIM(entityType);
UPDATE interaction_engagement_notification SET notificationType = RTRIM(notificationType);
UPDATE interaction_form_notification SET notificationType = RTRIM(notificationType);
UPDATE interaction_message SET messageType = RTRIM(messageType);
UPDATE interaction_message_recipient SET recipientType = RTRIM(recipientType);
UPDATE interaction_message_template SET templateType = RTRIM(templateType);
UPDATE interaction_note_notification SET notificationType = RTRIM(notificationType);
?20241106_OUTCOME-12697
INSERT INTO portal_config(orbisKey, orbisValue) VALUES ('SENDGRID_API_KEY', ''); --NOFAIL
?20250110_OUTCOME-13306
ALTER TABLE search_entity ALTER COLUMN staticFromHql VARCHAR(max); --NOFAIL
?20250113_OUTCOME-13310
INSERT INTO portal_config (orbisKey, orbisValue) values ('JWT_LOGIN_ISSUERS','login.outcome.orbis.tools'); --NOFAIL
?20250108_OUTCOME-13258
INSERT INTO portal_config(orbisKey, orbisValue) SELECT 'ZENDESK_SHARED_SECRET', '' EXCEPT SELECT orbisKey, '' FROM portal_config WHERE orbisKey = 'ZENDESK_SHARED_SECRET';
INSERT INTO portal_config(orbisKey, orbisValue) SELECT 'ZENDESK_ORG_NAME', '' EXCEPT SELECT orbisKey, '' FROM portal_config WHERE orbisKey = 'ZENDESK_ORG_NAME';
INSERT INTO user_group (name,category,externalGroup,primaryGroup,description) SELECT 'Support - Access', 'Support', 0, 0, 'Provides access to Zendesk support' EXCEPT SELECT name, 'Support', 0, 0, 'Provides access to Zendesk support' FROM user_group WHERE name='Support - Access';
INSERT INTO user_group_visibility (groupp, visibleGroup) SELECT null, id FROM user_group WHERE name='Support - Access' EXCEPT SELECT null, visibleGroup FROM user_group_visibility INNER JOIN user_group ON visibleGroup=user_group.id AND user_group.name='Support - Access';
INSERT INTO user_group_visibility (groupp, visibleGroup) SELECT ug2.id, user_group.id FROM user_group, user_group ug2 WHERE user_group.name='Support - Access' AND ug2.name='System Configuration Rights' EXCEPT SELECT ug2.id, visibleGroup FROM user_group_visibility INNER JOIN user_group ON visibleGroup=user_group.id AND user_group.name='Support - Access' INNER JOIN user_group ug2 ON groupp=ug2.id AND ug2.name='System Configuration Rights';
INSERT INTO user_group (name,category,externalGroup,primaryGroup,description) SELECT 'Support - Submit Tickets', 'Support', 0, 0, 'Allows the user to submit tickets in Zendesk' EXCEPT SELECT name, 'Support', 0, 0, 'Allows the user to submit tickets in Zendesk' FROM user_group WHERE name='Support - Submit Tickets';
INSERT INTO user_group_visibility (groupp, visibleGroup) SELECT null, id FROM user_group WHERE name='Support - Submit Tickets' EXCEPT SELECT null, visibleGroup FROM user_group_visibility INNER JOIN user_group ON visibleGroup=user_group.id AND user_group.name='Support - Submit Tickets';
INSERT INTO user_group_visibility (groupp, visibleGroup) SELECT ug2.id, user_group.id FROM user_group, user_group ug2 WHERE user_group.name='Support - Submit Tickets' AND ug2.name='System Configuration Rights' EXCEPT SELECT ug2.id, visibleGroup FROM user_group_visibility INNER JOIN user_group ON visibleGroup=user_group.id AND user_group.name='Support - Submit Tickets' INNER JOIN user_group ug2 ON groupp=ug2.id AND ug2.name='System Configuration Rights';
JAVA upgradePortalTo20250108_OUTCOME_13258
?20240722_OUTCOME-12508
UPDATE np_module SET savedSearchNumDays = IIF(jobSearchLifespan = 0 OR savedSearchNumDays < jobSearchLifespan, savedSearchNumDays, jobSearchLifespan);
DECLARE @command VARCHAR(255) = 'ALTER TABLE np_module DROP CONSTRAINT ' + (SELECT TOP 1 c.name FROM sys.default_constraints c JOIN sys.columns col ON col.column_id = c.parent_column_id AND col.object_id=c.parent_object_id WHERE col.name='jobSearchLifespan' AND c.parent_object_id=OBJECT_ID('np_module')); EXEC (@command); --NOFAIL
ALTER TABLE np_module DROP COLUMN IF EXISTS jobSearchLifespan; --NOFAIL
?20250124_OUTCOME-12697
JAVA upgradePortalTo20250124_OUTCOME_12697
?20250128_OUTCOME-13372
DELETE FROM portal_config WHERE orbisKey='JWT_SIGNIN_KEY_URL'; --NOFAIL
?20250206_OUTCOME-13423
UPDATE df_model SET modelEntityClassName = LEFT(modelEntityClassName, CHARINDEX('$HibernateProxy$', modelEntityClassName) - 1) WHERE modelEntityClassName LIKE '%$HibernateProxy$%'; --NOFAIL
?20250203_OUTCOME-13358
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('CC_CHALLENGE_CODE', 'NEWSALT'); --NOFAIL
?20250210_OUTCOME-13410
update df_model set modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeJournalModel' where modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeReflectionModel' --NOFAIL
?20250207_OUTCOME-13427
INSERT INTO portal_config(orbisKey, orbisValue) SELECT 'ZENDESK_API_KEY', '' EXCEPT SELECT orbisKey, '' FROM portal_config WHERE orbisKey = 'ZENDESK_API_KEY';
JAVA upgradePortalTo20250207_OUTCOME_13427
?20240913_OUTCOME-12617
JAVA upgradePortalTo20240913_OUTCOME_12617
?20240924_OUTCOME-12267
UPDATE dashboard_module SET employerTabsOrder = CONCAT(employerTabsOrder, ',et17')
?20250408_OUTCOME-13626
update interaction_note set visibleToEmployer = 0 where visibleToEmployer is null and noteType='com.orbis.web.content.interaction.InteractionNoteWorkTermRecord'; --NOFAIL
update interaction_note set visibleToStudent = 0 where visibleToStudent is null and noteType='com.orbis.web.content.interaction.InteractionNoteWorkTermRecord'; --NOFAIL
update interaction_note set visibleToFacultyAdvisor = 0 where visibleToFacultyAdvisor is null and noteType='com.orbis.web.content.interaction.InteractionNoteWorkTermRecord'; --NOFAIL
?20250425_OUTCOME-12697
update dashboard_module set portalStaffTabsOrder = portalStaffTabsOrder + ',pt25'; --NOFAIL
?20250506_OUTCOME-13810
JAVA upgradePortalTo20250506_OUTCOME_13810
?20250512_OUTCOME-13830
UPDATE q SET q.deleteLocked = 0 FROM exp_type type JOIN df_model model ON type.DFModel = model.id JOIN df_category cat ON model.id = cat.model JOIN df_question q ON cat.id = q.category WHERE q.deleteLocked = 1; --NOFAIL
?20250602_OUTCOME-13927
INSERT INTO portal_config(orbisKey, orbisValue) VALUES ('SITE_NAME', ''), ('INSTITUTION_NAME', ''); --NOFAIL
?20250704_OUTCOME-13348
update df_model set simplifiedModelWithHeaderFooter = 1 where modelEntityClassName = 'com.orbis.web.content.exp.EXPPostingQualifierModel' or modelEntityClassName = 'com.orbis.web.content.exp.EXPPostingPrescreeningQuestionsForm'; -- NOFAIL
?20250716_OUTCOME-14015
 update user_details set gender = 'Unknown' where gender is null --NOFAIL
?20250822_CHATBOT-SECURITY
-- Configuration entries for secure chatbot implementation
-- OpenAI API Configuration (encrypted values should be used in production)
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('OPENAI_API_KEY', 'ENCRYPTED_API_KEY_HERE'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('OPENAI_API_URL', 'https://api.openai.com/v1/chat/completions'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('OPENAI_MAX_TOKENS', '500'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('OPENAI_TEMPERATURE', '0.7'); --NOFAIL
-- Rate Limiting Configuration
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('CHATBOT_RATE_LIMIT_PER_HOUR', '20'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('CHATBOT_RATE_LIMIT_WINDOW_MINUTES', '60'); --NOFAIL
-- Dashboard Item Configuration for Student Chatbot Widget
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('sw_chatbot', '1'); --NOFAIL
-- Add OPENAI_API_KEY to secret configs list for encryption
UPDATE portal_config SET orbisValue = CASE WHEN orbisValue = '' OR orbisValue IS NULL THEN '["OPENAI_API_KEY"]' ELSE JSON_MODIFY(orbisValue, 'append $', 'OPENAI_API_KEY') END WHERE orbisKey = 'SECRET_CONFIGS'; --NOFAIL
--make sure this comment is at the End of File, empty lines break schema upgrade