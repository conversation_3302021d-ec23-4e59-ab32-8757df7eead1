<%@ include file="/WEB-INF/jsp/include.jsp" %>

<!-- Student Events Assistant Widget -->
<c:if test="${o:canViewDashboardItem('sw_chatbot', currentUser, siteElement.contentItem)}">
    <div class="panel panel-default" id="studentEventsAssistant">
        <div class="panel-heading">
            <strong><orbis:message code="i18n.dashboard_studentHome.EventsAssistant" /></strong>
            <small class="text-muted">Ask about upcoming events</small>
        </div>
        <div class="panel-body">
            <!-- Chat window to display conversation -->
            <div id="chat-window" class="chat-window" style="height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9; border-radius: 4px; margin-bottom: 10px;">
                <div class="message assistant" style="margin-bottom: 8px; padding: 8px; background-color: #e3f2fd; border-radius: 4px;">
                    <strong>Events Assistant:</strong> Hello! I can help you find information about upcoming events. Try asking "What events are coming up this week?"
                </div>
            </div>

            <!-- Chat input form -->
            <form id="chat-form" method="post" style="margin: 0;">
                <div class="input-group">
                    <textarea
                        id="prompt"
                        name="prompt"
                        class="form-control"
                        rows="2"
                        placeholder="Ask about upcoming events..."
                        maxlength="500"
                        style="resize: none;"
                        required></textarea>
                    <span class="input-group-btn">
                        <button type="submit" id="btnChat" class="btn btn-primary" style="height: 50px;">
                            <i class="fa fa-paper-plane"></i> Send
                        </button>
                    </span>
                </div>
                <small class="text-muted">Maximum 500 characters</small>
            </form>

            <!-- Loading indicator -->
            <div id="loading-indicator" style="display: none; text-align: center; margin-top: 10px;">
                <i class="fa fa-spinner fa-spin"></i> Processing your request...
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            var chatWindow = $('#chat-window');
            var chatForm = $('#chat-form');
            var promptInput = $('#prompt');
            var submitBtn = $('#btnChat');
            var loadingIndicator = $('#loading-indicator');

            // Function to safely escape HTML to prevent XSS
            function escapeHtml(text) {
                var map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;'
                };
                return text.replace(/[&<>"']/g, function(m) { return map[m]; });
            }

            // Function to add message to chat window
            function addMessage(sender, message, isUser) {
                var messageClass = isUser ? 'user' : 'assistant';
                var bgColor = isUser ? '#e8f5e8' : '#e3f2fd';
                var escapedMessage = escapeHtml(message);

                var messageHtml = '<div class="message ' + messageClass + '" style="margin-bottom: 8px; padding: 8px; background-color: ' + bgColor + '; border-radius: 4px;">' +
                    '<strong>' + escapeHtml(sender) + ':</strong> ' + escapedMessage +
                    '</div>';

                chatWindow.append(messageHtml);
                chatWindow.scrollTop(chatWindow[0].scrollHeight);
            }

            // Form submission handler
            chatForm.on('submit', function (e) {
                e.preventDefault();

                var prompt = promptInput.val().trim();
                if (prompt === '') {
                    return;
                }

                // Validate input length
                if (prompt.length > 500) {
                    alert('Message is too long. Please keep it under 500 characters.');
                    return;
                }

                // Disable form and show loading
                submitBtn.prop('disabled', true);
                loadingIndicator.show();

                // Add user message to chat
                addMessage('You', prompt, true);
                promptInput.val('');

                // Prepare secure request
                var requestData = {
                    prompt: prompt,
                    action: '<o:encrypt action="submitPromptIntent" />',
                    _token: $('meta[name="csrf-token"]').attr('content') // CSRF protection if available
                };

                // Send AJAX request
                $.ajax({
                    method: 'POST',
                    data: requestData,
                    timeout: 30000, // 30 second timeout
                    success: function (response) {
                        if (response && response.trim() !== '') {
                            addMessage('Events Assistant', response, false);
                        } else {
                            addMessage('Events Assistant', 'I apologize, but I could not process your request at this time.', false);
                        }
                    },
                    error: function (xhr, status, error) {
                        var errorMessage = 'Sorry, I encountered an error while processing your request.';
                        if (status === 'timeout') {
                            errorMessage = 'Request timed out. Please try again.';
                        }
                        addMessage('Events Assistant', errorMessage, false);
                    },
                    complete: function () {
                        // Re-enable form and hide loading
                        submitBtn.prop('disabled', false);
                        loadingIndicator.hide();
                        promptInput.focus();
                    }
                });
            });

            // Auto-resize textarea
            promptInput.on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        });
    </script>
</c:if>
