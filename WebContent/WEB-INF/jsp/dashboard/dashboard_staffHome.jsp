<%@ include file="/WEB-INF/jsp/include.jsp"%>

<%@ include file="dashboard_staffTitle.jsp"%>
<%@ include file="dashboard_staff_action_bar.jsp"%>


<c:if test="${staffViewHeaderPC.orbisValue == '1'}">
	<%@ include file="dashboard_staffViewHeader.jsp"%>
</c:if>
<%@ include file="dashboard_partnerLinks.jsp"%>

<div class="orbisTabContainer">
	<c:set var="selectedTab" value="overview" />
	<%@ include file="dashboard_nav.jsp"%>
	<div class="tab-content">
		<div class="tab-pane active" id="dashboard">
			<c:if test="${o:canViewDashboardItem('pc8', currentUser, siteElement.contentItem) && siteElement.contentItem.pc8d == 0}">
				<%@ include file="dashboard_homeMessages.jsp"%>
			</c:if>
			<div class="row-fluid">
				<div class="span6">
					<c:if test="${o:canViewDashboardItem('pc8', currentUser, siteElement.contentItem) && siteElement.contentItem.pc8d == 1}"><%@ include file="dashboard_homeMessages.jsp"%></c:if>
					<c:set var="dwsc" value="${o:dwsc(false) }" scope="request" />
					<c:if test="${myDashboardNav==0 || myDashboardNav==1}">
						<c:if test="${(o:canViewDashboardItem('pw11', currentUser, siteElement.contentItem)) || (o:canViewDashboardItem('pw3', currentUser, siteElement.contentItem)) || (o:canViewDashboardItem('pw1', currentUser, siteElement.contentItem)) || (o:canViewDashboardItem('pw2', currentUser, siteElement.contentItem)) || (o:canViewDashboardItem('pw16', currentUser, siteElement.contentItem))}">
							<div class="panel panel-default">
								<div class="panel-heading">
									<orbis:message code="i18n.dashboard_staffHome.strongstro18873791060044243" />
								</div>
								<div class="panel-body">
									<ui:ajax action="ajaxLoadStaffDashboardInteractionAlertsStatsPanel" />
								</div>
							</div>
						</c:if>

						<%@ include file="dashboard_staffHome_interactions_column.jsp"%>
					</c:if>
				</div>
				<div class="span6">
					<c:if test="${o:canViewDashboardItem('pc8', currentUser, siteElement.contentItem) && siteElement.contentItem.pc8d == 2}">
						<%@ include file="dashboard_homeMessages.jsp"%>
					</c:if>

					<%@ include file="dashboard_staffHome_nonInteractions_column.jsp"%>

					<c:if test="${o:canViewDashboardItem('pw13', currentUser, siteElement.contentItem) && not empty currentUser.assignedTypes['Orbis - In Development']}">
						<div class="panel panel-default">
							<div class="panel-heading">
								{{siteElement}}
								<strong>${o:getDashboardItemLabel('pw13', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							</div>
							<div class="panel-body">
								<div class="row-fluid">
									<div class="span12">
										<table class="table stat-table table-hover">
											<tbody>
												<tr>
													<td>
														<orbis:message code="i18n.dashboard_staffHome.UnderDevelopment" />
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</c:if>
				</div>
			</div>
		</div>
	</div>
</div>

<c:if test="${staffViewHeaderPC.orbisValue == '1'}">
	<%@ include file="dashboard_staffViewFooter.jsp"%>
</c:if>
 